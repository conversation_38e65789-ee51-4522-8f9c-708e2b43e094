#!/usr/bin/env python3
"""
知识库功能使用示例

这个示例展示了如何：
1. 创建知识库类型的Bundle
2. 模拟添加OCR结果
3. 构建知识库
4. 查询知识库
"""
import asyncio
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tortoise import Tortoise
from app.models.bundle import Bundle
from app.models.page import Page
from app.models.enums import BundleType, ProcessingStatus
from app.controllers.bundle import bundle_controller
from app.ai.kb.manager import KnowledgeBaseManager
from app.settings import settings


async def init_db():
    """初始化数据库连接"""
    await Tortoise.init(config=settings.TORTOISE_ORM)


async def close_db():
    """关闭数据库连接"""
    await Tortoise.close_connections()


async def create_knowledge_base_bundle():
    """创建知识库Bundle"""
    print("1. 创建知识库Bundle...")
    
    # 创建Bundle
    bundle = await Bundle.create(
        name="测试知识库",
        description="这是一个测试知识库，包含一些示例文档",
        bundle_type=BundleType.KNOWLEDGE_BASE,
        custom_metadata={
            "embedding_type": "basic",
            "llm_type": "basic",
            "status": "not_built"
        }
    )
    
    print(f"✓ 创建Bundle成功，ID: {bundle.id}")
    return bundle


async def add_sample_pages(bundle: Bundle):
    """添加示例页面数据"""
    print("2. 添加示例页面数据...")
    
    sample_texts = [
        """
        人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，
        它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。
        该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。
        """,
        """
        机器学习是人工智能的一个重要分支，它是一种通过算法使机器能够从数据中学习并做出决策或预测的技术。
        机器学习的主要类型包括监督学习、无监督学习和强化学习。
        监督学习使用标记的训练数据来学习输入和输出之间的映射关系。
        """,
        """
        深度学习是机器学习的一个子集，它模仿人脑神经网络的结构和功能。
        深度学习使用多层神经网络来学习数据的复杂模式和表示。
        它在图像识别、语音识别、自然语言处理等领域取得了突破性进展。
        """,
        """
        自然语言处理（Natural Language Processing，NLP）是人工智能和语言学领域的分支学科。
        它研究能实现人与计算机之间用自然语言进行有效通信的各种理论和方法。
        NLP的应用包括机器翻译、情感分析、文本摘要、问答系统等。
        """
    ]
    
    pages = []
    for i, text in enumerate(sample_texts, 1):
        page = await Page.create(
            bundle=bundle,
            page_number=i,
            process_status=ProcessingStatus.COMPLETED,
            ocr_result=text.strip(),
            custom_metadata={"source": f"sample_doc_{i}"}
        )
        pages.append(page)
    
    print(f"✓ 添加了 {len(pages)} 个示例页面")
    return pages


async def build_knowledge_base(bundle: Bundle):
    """构建知识库"""
    print("3. 构建知识库...")
    
    # 获取RAG引擎配置
    config = bundle_controller._get_kb_config(bundle)
    
    # 获取RAG引擎实例
    rag_engine = await KnowledgeBaseManager.get_rag_engine(bundle.id, config)
    
    # 构建知识库
    success = await bundle_controller.build_knowledge_base(bundle.id, rag_engine)
    
    if success:
        print("✓ 知识库构建成功")
        
        # 获取更新后的Bundle信息
        await bundle.refresh_from_db()
        metadata = bundle.custom_metadata or {}
        print(f"  - 文档数量: {metadata.get('document_count', 0)}")
        print(f"  - 构建状态: {metadata.get('status', 'unknown')}")
        print(f"  - 构建时间: {metadata.get('last_build_at', 'unknown')}")
    else:
        print("✗ 知识库构建失败")
    
    return success


async def query_knowledge_base(bundle: Bundle):
    """查询知识库"""
    print("4. 查询知识库...")
    
    queries = [
        "什么是人工智能？",
        "机器学习有哪些类型？",
        "深度学习的特点是什么？",
        "NLP的应用有哪些？"
    ]
    
    for query in queries:
        print(f"\n问题: {query}")
        
        result = await bundle_controller.query_knowledge_base(
            bundle.id, 
            query, 
            mode="hybrid"
        )
        
        if result:
            print(f"回答: {result[:200]}...")
        else:
            print("查询失败")


async def cleanup(bundle: Bundle):
    """清理资源"""
    print("\n5. 清理资源...")
    
    # 清理RAG引擎实例
    await KnowledgeBaseManager.remove_instance(bundle.id)
    
    # 删除测试数据
    await Page.filter(bundle=bundle).delete()
    await bundle.delete()
    
    print("✓ 清理完成")


async def main():
    """主函数"""
    print("=== 知识库功能示例 ===\n")
    
    # 检查环境变量
    required_env_vars = [
        "BASIC_EMBEDDING_API_KEY",
        "BASIC_MODEL_API_KEY"
    ]
    
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    if missing_vars:
        print(f"⚠️  缺少环境变量: {', '.join(missing_vars)}")
        print("请设置相应的API密钥环境变量")
        print("\n示例:")
        print("export BASIC_EMBEDDING_API_KEY='your_embedding_api_key'")
        print("export BASIC_MODEL_API_KEY='your_llm_api_key'")
        return
    
    try:
        # 初始化数据库
        await init_db()
        
        # 创建知识库Bundle
        bundle = await create_knowledge_base_bundle()
        
        # 添加示例页面
        await add_sample_pages(bundle)
        
        # 构建知识库
        success = await build_knowledge_base(bundle)
        
        if success:
            # 查询知识库
            await query_knowledge_base(bundle)
        
        # 清理资源
        await cleanup(bundle)
        
        print("\n=== 示例完成 ===")
        
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭数据库连接
        await close_db()


if __name__ == "__main__":
    asyncio.run(main())
