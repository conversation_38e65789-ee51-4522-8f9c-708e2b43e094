#!/usr/bin/env python3
"""
测试知识库功能的脚本
"""
import asyncio
import os
import sys
from dotenv import load_dotenv

load_dotenv()


# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.models.bundle import Bundle
from app.models.enums import BundleType, ProcessingStatus
from app.controllers.bundle import bundle_controller
from app.ai.kb.manager import KnowledgeBaseManager
from app.ai.embeddings.embedding import get_embedding
from app.ai.llms.llm import get_llm_by_type


async def test_embedding():
    """测试embedding功能"""
    print("=== 测试Embedding功能 ===")
    try:
        embedding = get_embedding("basic")
        print(f"✓ 成功获取embedding实例: {embedding}")
        print(f"  - 模型: {getattr(embedding, 'model', 'unknown')}")
        print(f"  - 维度: {getattr(embedding, 'dimensions', 'unknown')}")

        # 如果有API密钥，测试embedding
        if os.getenv("BASIC_EMBEDDING_API_KEY"):
            test_texts = ["这是一个测试文本", "This is a test text"]
            embeddings = await embedding.aembed_documents(test_texts)
            print(f"✓ 成功生成embeddings，维度: {len(embeddings[0])}")
        else:
            print("⚠️  跳过embedding测试（缺少API密钥）")

    except Exception as e:
        print(f"✗ Embedding测试失败: {e}")


async def test_llm():
    """测试LLM功能"""
    print("\n=== 测试LLM功能 ===")
    try:
        llm = get_llm_by_type("basic")
        print(f"✓ 成功获取LLM实例: {llm}")
        print(f"  - 模型: {getattr(llm, 'model_name', 'unknown')}")

        # 如果有API密钥，测试LLM
        if os.getenv("BASIC_MODEL_API_KEY"):
            messages = [{"role": "user", "content": "你好，请简单回复"}]
            response = await llm.ainvoke(messages)
            print(f"✓ 成功获取LLM响应: {response.content[:50]}...")
        else:
            print("⚠️  跳过LLM测试（缺少API密钥）")

    except Exception as e:
        print(f"✗ LLM测试失败: {e}")


async def test_knowledge_base_manager():
    """测试知识库管理器"""
    print("\n=== 测试知识库管理器 ===")
    try:
        # 创建测试配置
        config = {
            "working_dir": "data/kb/test_bundle",
            "embedding_type": "basic",
            "llm_type": "basic"
        }

        print("✓ 创建测试配置成功")

        # 如果有API密钥，测试RAG引擎
        if os.getenv("BASIC_EMBEDDING_API_KEY") and os.getenv("BASIC_MODEL_API_KEY"):
            # 获取RAG引擎
            rag_engine = await KnowledgeBaseManager.get_rag_engine(999, config)
            print(f"✓ 成功创建RAG引擎实例")

            # 测试插入文档
            await rag_engine.insert("这是一个测试文档，包含一些知识内容。", ids="test_doc_1")
            await rag_engine.insert(["孙悟空在花果山认识了牛魔王。", "牛魔王和孙悟空在花果山结拜为兄弟， 牛魔王的老婆芭蕉公主表示非常赞同，并以身相许"],
                                    ids=["test_doc_2", "test_doc_3"])
            print("✓ 成功插入测试文档")

            # 测试查询
            result = await rag_engine.query("孙悟空和芭蕉公主有什么故事吗？")
            print(f"✓ 成功查询知识库: {result[:100]}...")

            # 清理
            await KnowledgeBaseManager.remove_instance(999)
            print("✓ 成功清理测试实例")
        else:
            print("⚠️  跳过RAG引擎测试（缺少API密钥）")

    except Exception as e:
        print(f"✗ 知识库管理器测试失败: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """主测试函数"""
    print("开始测试知识库功能...\n")
    
    # 检查环境变量
    required_env_vars = [
        "BASIC_EMBEDDING_API_KEY",
        "BASIC_MODEL_API_KEY"
    ]

    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    if missing_vars:
        print(f"⚠️  缺少环境变量: {', '.join(missing_vars)}")
        print("将进行基础测试（不包含API调用）")
    else:
        print("✓ 环境变量配置完整")
    
    # 运行测试
    await test_embedding()
    await test_llm()
    await test_knowledge_base_manager()
    
    print("\n测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
