# 知识库功能使用指南

## 概述

本项目集成了基于 LightRAG 的知识库功能，支持将 Bundle 中的 OCR 结果构建为可查询的知识库。

## 架构设计

### 分层架构
- **Database Model** (`app/models/bundle.py`) - 纯数据模型，不包含业务逻辑
- **Knowledge Base Manager** (`app/ai/kb/manager.py`) - 底层知识库引擎管理（LightRAG实例）
- **Bundle Service** (`app/controllers/bundle.py`) - 业务逻辑层，协调各组件
- **API Layer** (`app/api/v1/knowledge_base/routes.py`) - REST API接口

### 核心组件
1. **Embedding 模块** - 基于 langchain-openai 的嵌入模型
2. **RAG 引擎** - LightRAG 实例的封装和管理
3. **知识库管理器** - 单例模式管理多个知识库实例

## 配置说明

### 环境变量配置
```bash
# Embedding 模型配置
BASIC_EMBEDDING_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
BASIC_EMBEDDING_MODEL=text-embedding-v3
BASIC_EMBEDDING_API_KEY=your_api_key
BASIC_EMBEDDING_DIMENSIONS=1536

# LLM 模型配置（复用现有配置）
BASIC_MODEL_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
BASIC_MODEL_NAME=qwen-max-2025-01-25
BASIC_MODEL_API_KEY=your_api_key
```

### Bundle 配置
对于 `bundle_type = "knowledge_base"` 的 Bundle，`custom_metadata` 字段包含：

```json
{
    "embedding_type": "basic",     // 嵌入模型类型
    "llm_type": "basic",          // LLM模型类型
    "last_build_at": null,        // 最后构建时间
    "document_count": 0,          // 文档数量
    "status": "not_built"         // 构建状态
}
```

## 使用流程

### 1. 创建知识库 Bundle
```python
from app.models.enums import BundleType

bundle_data = {
    "name": "我的知识库",
    "description": "测试知识库",
    "bundle_type": BundleType.KNOWLEDGE_BASE,
    "custom_metadata": {
        "embedding_type": "basic",
        "llm_type": "basic"
    }
}
```

### 2. 上传文件并进行 OCR
- 将文件关联到 Bundle
- 执行 OCR 处理，确保 Page 的 `ocr_result` 字段有内容

### 3. 构建知识库
```bash
POST /api/v1/kb/bundles/{bundle_id}/build
```

### 4. 查询知识库
```bash
POST /api/v1/kb/bundles/{bundle_id}/query
{
    "query": "你的问题",
    "mode": "hybrid"  // naive, local, global, hybrid
}
```

## API 接口

### 构建知识库
- **URL**: `POST /api/v1/kb/bundles/{bundle_id}/build`
- **描述**: 构建指定Bundle的知识库
- **参数**: 
  - `bundle_id`: Bundle ID（必须是knowledge_base类型）

### 查询知识库
- **URL**: `POST /api/v1/kb/bundles/{bundle_id}/query`
- **描述**: 查询指定Bundle的知识库
- **参数**:
  - `bundle_id`: Bundle ID
  - `query`: 查询问题
  - `mode`: 查询模式（naive, local, global, hybrid）

### 获取知识库状态
- **URL**: `GET /api/v1/kb/bundles/{bundle_id}/status`
- **描述**: 获取知识库构建状态和统计信息

### 清理知识库
- **URL**: `DELETE /api/v1/kb/bundles/{bundle_id}/clear`
- **描述**: 清理知识库实例和重置状态

## 数据存储

### 目录结构
```
data/
└── kb/                    # 知识库根目录
    ├── bundle_1/          # Bundle ID 1 的知识库数据
    ├── bundle_2/          # Bundle ID 2 的知识库数据
    └── ...
```

### LightRAG 数据文件
每个知识库目录包含：
- `graph_chunk_entity_relation.graphml` - 知识图谱
- `kv_store_*.json` - 键值存储
- `vdb_*.json` - 向量数据库

## 测试

运行测试脚本：
```bash
python test_knowledge_base.py
```

确保设置了必要的环境变量：
- `BASIC_EMBEDDING_API_KEY`
- `BASIC_MODEL_API_KEY`

## 注意事项

1. **单例模式**: 每个 Bundle 只维护一个 LightRAG 实例
2. **异步处理**: 所有操作都是异步的，适合大量数据处理
3. **错误处理**: 完善的错误处理和日志记录
4. **资源管理**: 支持实例清理和资源释放
5. **扩展性**: 支持未来添加其他 RAG 引擎

## 故障排除

### 常见问题
1. **API 密钥错误**: 检查环境变量配置
2. **知识库未构建**: 确保先调用构建接口
3. **OCR 结果为空**: 检查 Page 的 `ocr_result` 字段
4. **内存不足**: 大量数据时考虑分批处理

### 日志查看
```bash
# 查看应用日志
tail -f app/logs/app.log

# 查看知识库相关日志
grep "knowledge" app/logs/app.log
```
