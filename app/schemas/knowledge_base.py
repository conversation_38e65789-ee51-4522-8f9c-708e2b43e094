from typing import Optional, Literal
from pydantic import BaseModel, Field

from app.schemas.base import BaseSchema


class KnowledgeBaseBuildRequest(BaseSchema):
    """知识库构建请求"""
    pass  # 暂时不需要额外参数


class KnowledgeBaseQueryRequest(BaseSchema):
    """知识库查询请求"""
    query: str = Field(..., description="查询问题", min_length=1, max_length=1000)
    mode: Literal["naive", "local", "global", "hybrid"] = Field(
        default="hybrid", 
        description="查询模式"
    )


class KnowledgeBaseQueryResponse(BaseSchema):
    """知识库查询响应"""
    answer: str = Field(..., description="查询结果")
    bundle_id: int = Field(..., description="Bundle ID")
    query: str = Field(..., description="原始查询")
    mode: str = Field(..., description="查询模式")


class KnowledgeBaseBuildResponse(BaseSchema):
    """知识库构建响应"""
    bundle_id: int = Field(..., description="Bundle ID")
    document_count: int = Field(..., description="插入的文档数量")
    status: str = Field(..., description="构建状态")


class KnowledgeBaseStatusResponse(BaseSchema):
    """知识库状态响应"""
    bundle_id: int = Field(..., description="Bundle ID")
    status: str = Field(..., description="知识库状态")
    last_build_at: Optional[str] = Field(None, description="最后构建时间")
    document_count: int = Field(default=0, description="文档数量")
    error: Optional[str] = Field(None, description="错误信息")
