from enum import Enum, StrEnum


class EnumBase(Enum):
    @classmethod
    def get_member_values(cls):
        return [item.value for item in cls._member_map_.values()]

    @classmethod
    def get_member_names(cls):
        return [name for name in cls._member_names_]


class MethodType(StrEnum):
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"
    PATCH = "PATCH"

class BundleType(str, Enum):
    GENERAL = "general"
    INVOICE = "invoice"
    CONTRACT = "contract"
    EDUCATION = "education"
    KNOWLEDGE_BASE = "knowledge_base"
    # Add more as needed

class ProcessingStatus(StrEnum):
    NEW = "NEW"
    PENDING = "PENDING"
    PROCESSING = "PROCESSING"
    COMPLETED = "COMPLETED"
    CANCELLED = "CANCELLED"
    FAILED = "FAILED"

class HashAlgorithm(StrEnum):
    MD5 = "md5"
    SHA256 = "sha256"

