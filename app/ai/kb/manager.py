import os
from typing import Dict, Optional, Protocol, Any
from abc import ABC, abstractmethod

from lightrag import LightRAG, QueryParam
from lightrag.kg.shared_storage import initialize_pipeline_status

from app.ai.embeddings.embedding import get_embedding, EmbeddingType
from app.ai.llms.llm import get_llm_by_type, LLMType
from app.log import logger


class RAGEngine(Protocol):
    """RAG引擎协议，支持不同的RAG实现"""
    
    async def insert(self, text: str | list[str], **kwargs: Any) -> None:
        """插入文档到知识库"""
        ...
    
    async def query(self, query: str, mode: str = "hybrid", **kwargs: Any) -> str:
        """查询知识库"""
        ...
    
    async def initialize(self) -> None:
        """初始化RAG引擎"""
        ...
    
    async def finalize(self) -> None:
        """清理RAG引擎资源"""
        ...


class LightRAGEngine:
    """LightRAG引擎实现"""
    
    def __init__(self, working_dir: str, embedding_type: EmbeddingType, llm_type: LLMType):
        self.working_dir = working_dir
        self.embedding_type = embedding_type
        self.llm_type = llm_type
        self._rag_instance = None
        
        # 确保工作目录存在
        os.makedirs(working_dir, exist_ok=True)
    
    async def _get_rag_instance(self):
        """获取LightRAG实例（延迟初始化）"""
        if self._rag_instance is None:
            try:
                
                # 获取embedding函数
                embedding_model = get_embedding(self.embedding_type)

                # 创建embedding函数适配器
                async def embedding_func(texts):
                    embeddings = await embedding_model.aembed_documents(texts)
                    import numpy as np
                    return np.array(embeddings)

                # 为embedding函数添加LightRAG需要的属性
                embedding_func.embedding_dim = getattr(embedding_model, 'dimensions', 1536)
                embedding_func.max_token_size = getattr(embedding_model, 'max_token_size', 8192)
                
                # 获取LLM函数
                llm_model = get_llm_by_type(self.llm_type)
                
                # 创建LLM函数适配器
                async def llm_func(prompt, system_prompt=None, history_messages=None, **kwargs):
                    messages = []
                    if system_prompt:
                        messages.append({"role": "system", "content": system_prompt})
                    if history_messages:
                        messages.extend(history_messages)
                    messages.append({"role": "user", "content": prompt})
                    
                    response = await llm_model.ainvoke(messages)
                    return response.content
                
                # 创建LightRAG实例
                self._rag_instance = LightRAG(
                    working_dir=self.working_dir,
                    embedding_func=embedding_func,
                    llm_model_func=llm_func,
                )
                
                await self._rag_instance.initialize_storages()
                await initialize_pipeline_status()
                logger.info(f"LightRAG instance initialized for {self.working_dir}")
                
            except Exception as e:
                logger.error(f"Failed to initialize LightRAG: {e}")
                raise
        
        return self._rag_instance
    
    async def insert(self, text: str | list[str], **kwargs: Any) -> None:
        """插入文档到知识库"""
        try:
            rag = await self._get_rag_instance()
            await rag.ainsert(text, **kwargs)
        except Exception as e:
            logger.error(f"Failed to insert document: {e}")
            raise
    
    async def query(self, query: str, mode: str = "hybrid", **kwargs: Any) -> str:
        """查询知识库"""
        try:
            rag = await self._get_rag_instance()
            result = await rag.aquery(query, param=QueryParam(mode=mode, **kwargs))
            return result
        except Exception as e:
            logger.error(f"Failed to query knowledge base: {e}")
            raise
    
    async def initialize(self) -> None:
        """初始化RAG引擎"""
        await self._get_rag_instance()
    
    async def finalize(self) -> None:
        """清理RAG引擎资源"""
        if self._rag_instance:
            try:
                await self._rag_instance.finalize_storages()
                logger.info(f"LightRAG instance finalized for {self.working_dir}")
            except Exception as e:
                logger.error(f"Failed to finalize LightRAG: {e}")
            finally:
                self._rag_instance = None


class KnowledgeBaseManager:
    """知识库管理器 - 负责RAG引擎实例的创建和管理"""
    
    _instances: Dict[int, RAGEngine] = {}  # Bundle ID -> RAG Engine
    
    @classmethod
    async def get_rag_engine(cls, bundle_id: int, config: Dict) -> RAGEngine:
        """获取或创建RAG引擎实例（单例）"""
        if bundle_id not in cls._instances:
            cls._instances[bundle_id] = await cls._create_rag_engine(config)
        return cls._instances[bundle_id]
    
    @classmethod
    async def _create_rag_engine(cls, config: Dict) -> RAGEngine:
        """根据配置创建RAG引擎"""
        # 目前支持LightRAG，未来可扩展其他引擎
        engine = LightRAGEngine(
            working_dir=config["working_dir"],
            embedding_type=config.get("embedding_type", "basic"),
            llm_type=config.get("llm_type", "basic")
        )
        
        # 初始化引擎
        await engine.initialize()
        return engine
    
    @classmethod
    async def remove_instance(cls, bundle_id: int):
        """移除RAG引擎实例"""
        if bundle_id in cls._instances:
            try:
                await cls._instances[bundle_id].finalize()
            except Exception as e:
                logger.error(f"Failed to finalize RAG engine for bundle {bundle_id}: {e}")
            finally:
                del cls._instances[bundle_id]
                logger.info(f"Removed RAG engine instance for bundle {bundle_id}")
    
    @classmethod
    async def cleanup_all(cls):
        """清理所有RAG引擎实例"""
        bundle_ids = list(cls._instances.keys())
        for bundle_id in bundle_ids:
            await cls.remove_instance(bundle_id)
        logger.info("All RAG engine instances cleaned up")
