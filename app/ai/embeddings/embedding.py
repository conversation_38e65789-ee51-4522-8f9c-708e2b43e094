from typing import Dict, Literal
from langchain_openai import OpenAIEmbeddings

from app.settings import settings

EmbeddingType = Literal["basic"]

# Cache for embedding instances
_embedding_cache: Dict[EmbeddingType, OpenAIEmbeddings] = {}


def _create_embedding_by_type(embedding_type: EmbeddingType) -> OpenAIEmbeddings:
    """根据类型创建embedding实例"""
    embedding_type_map = {
        "basic": settings.BASIC_EMBEDDING,
    }
    
    embedding_conf = embedding_type_map.get(embedding_type)
    if not embedding_conf:
        raise ValueError(f"Unknown embedding type: {embedding_type}")
    if not isinstance(embedding_conf, dict):
        raise ValueError(f"Invalid embedding conf: {embedding_type}")
    
    return OpenAIEmbeddings(**embedding_conf)


def get_embedding(embedding_type: EmbeddingType = "basic") -> OpenAIEmbeddings:
    """
    Get embedding instance by type. Returns cached instance if available.
    参考 llm.py 的风格实现
    """
    if embedding_type in _embedding_cache:
        return _embedding_cache[embedding_type]

    embedding = _create_embedding_by_type(embedding_type)
    _embedding_cache[embedding_type] = embedding
    return embedding


if __name__ == "__main__":
    # Test embedding
    basic_embedding = get_embedding("basic")
    print(f"Basic embedding model: {basic_embedding.model}")
