import{a as xe,b as Yt,g as kt,d as Te,c as ye,e as Ee}from"./chunk-D6G4REZN-EKNDM_rA.js";import{I as be}from"./chunk-XZIHB7SX-B2fPcWzi.js";import{_ as p,o as me,c as $,d as _t,l as G,j as Zt,e as we,f as Ie,k as L,b as Gt,s as Le,q as _e,a as Pe,g as Ae,t as ke,z as Ne,i as Pt,u as Y,V as ot,W as bt,M as Qt,Z as ve,X as jt,G as Ct}from"./mermaid-vendor-S2u3NfNd.js";import"./feature-graph-DGPXw7qg.js";import"./react-vendor-DEwriMA6.js";import"./graph-vendor-B-X5JegA.js";import"./ui-vendor-CeCm8EER.js";import"./utils-vendor-BysuhMZA.js";var Ot=function(){var e=p(function(dt,I,_,A){for(_=_||{},A=dt.length;A--;_[dt[A]]=I);return _},"o"),t=[1,2],c=[1,3],s=[1,4],a=[2,4],i=[1,9],n=[1,11],d=[1,13],h=[1,14],r=[1,16],g=[1,17],E=[1,18],f=[1,24],T=[1,25],m=[1,26],w=[1,27],k=[1,28],O=[1,29],S=[1,30],B=[1,31],D=[1,32],F=[1,33],q=[1,34],X=[1,35],tt=[1,36],z=[1,37],H=[1,38],W=[1,39],M=[1,41],J=[1,42],K=[1,43],Z=[1,44],et=[1,45],v=[1,46],y=[1,4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,48,49,50,52,53,54,59,60,61,62,70],P=[4,5,16,50,52,53],Q=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],at=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,49,50,52,53,54,59,60,61,62,70],N=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,48,50,52,53,54,59,60,61,62,70],qt=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,50,52,53,54,59,60,61,62,70],it=[68,69,70],ct=[1,122],vt={trace:p(function(){},"trace"),yy:{},symbols_:{error:2,start:3,SPACE:4,NEWLINE:5,SD:6,document:7,line:8,statement:9,box_section:10,box_line:11,participant_statement:12,create:13,box:14,restOfLine:15,end:16,signal:17,autonumber:18,NUM:19,off:20,activate:21,actor:22,deactivate:23,note_statement:24,links_statement:25,link_statement:26,properties_statement:27,details_statement:28,title:29,legacy_title:30,acc_title:31,acc_title_value:32,acc_descr:33,acc_descr_value:34,acc_descr_multiline_value:35,loop:36,rect:37,opt:38,alt:39,else_sections:40,par:41,par_sections:42,par_over:43,critical:44,option_sections:45,break:46,option:47,and:48,else:49,participant:50,AS:51,participant_actor:52,destroy:53,note:54,placement:55,text2:56,over:57,actor_pair:58,links:59,link:60,properties:61,details:62,spaceList:63,",":64,left_of:65,right_of:66,signaltype:67,"+":68,"-":69,ACTOR:70,SOLID_OPEN_ARROW:71,DOTTED_OPEN_ARROW:72,SOLID_ARROW:73,BIDIRECTIONAL_SOLID_ARROW:74,DOTTED_ARROW:75,BIDIRECTIONAL_DOTTED_ARROW:76,SOLID_CROSS:77,DOTTED_CROSS:78,SOLID_POINT:79,DOTTED_POINT:80,TXT:81,$accept:0,$end:1},terminals_:{2:"error",4:"SPACE",5:"NEWLINE",6:"SD",13:"create",14:"box",15:"restOfLine",16:"end",18:"autonumber",19:"NUM",20:"off",21:"activate",23:"deactivate",29:"title",30:"legacy_title",31:"acc_title",32:"acc_title_value",33:"acc_descr",34:"acc_descr_value",35:"acc_descr_multiline_value",36:"loop",37:"rect",38:"opt",39:"alt",41:"par",43:"par_over",44:"critical",46:"break",47:"option",48:"and",49:"else",50:"participant",51:"AS",52:"participant_actor",53:"destroy",54:"note",57:"over",59:"links",60:"link",61:"properties",62:"details",64:",",65:"left_of",66:"right_of",68:"+",69:"-",70:"ACTOR",71:"SOLID_OPEN_ARROW",72:"DOTTED_OPEN_ARROW",73:"SOLID_ARROW",74:"BIDIRECTIONAL_SOLID_ARROW",75:"DOTTED_ARROW",76:"BIDIRECTIONAL_DOTTED_ARROW",77:"SOLID_CROSS",78:"DOTTED_CROSS",79:"SOLID_POINT",80:"DOTTED_POINT",81:"TXT"},productions_:[0,[3,2],[3,2],[3,2],[7,0],[7,2],[8,2],[8,1],[8,1],[10,0],[10,2],[11,2],[11,1],[11,1],[9,1],[9,2],[9,4],[9,2],[9,4],[9,3],[9,3],[9,2],[9,3],[9,3],[9,2],[9,2],[9,2],[9,2],[9,2],[9,1],[9,1],[9,2],[9,2],[9,1],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[45,1],[45,4],[42,1],[42,4],[40,1],[40,4],[12,5],[12,3],[12,5],[12,3],[12,3],[24,4],[24,4],[25,3],[26,3],[27,3],[28,3],[63,2],[63,1],[58,3],[58,1],[55,1],[55,1],[17,5],[17,5],[17,4],[22,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[56,1]],performAction:p(function(I,_,A,b,R,l,Et){var u=l.length-1;switch(R){case 3:return b.apply(l[u]),l[u];case 4:case 9:this.$=[];break;case 5:case 10:l[u-1].push(l[u]),this.$=l[u-1];break;case 6:case 7:case 11:case 12:this.$=l[u];break;case 8:case 13:this.$=[];break;case 15:l[u].type="createParticipant",this.$=l[u];break;case 16:l[u-1].unshift({type:"boxStart",boxData:b.parseBoxData(l[u-2])}),l[u-1].push({type:"boxEnd",boxText:l[u-2]}),this.$=l[u-1];break;case 18:this.$={type:"sequenceIndex",sequenceIndex:Number(l[u-2]),sequenceIndexStep:Number(l[u-1]),sequenceVisible:!0,signalType:b.LINETYPE.AUTONUMBER};break;case 19:this.$={type:"sequenceIndex",sequenceIndex:Number(l[u-1]),sequenceIndexStep:1,sequenceVisible:!0,signalType:b.LINETYPE.AUTONUMBER};break;case 20:this.$={type:"sequenceIndex",sequenceVisible:!1,signalType:b.LINETYPE.AUTONUMBER};break;case 21:this.$={type:"sequenceIndex",sequenceVisible:!0,signalType:b.LINETYPE.AUTONUMBER};break;case 22:this.$={type:"activeStart",signalType:b.LINETYPE.ACTIVE_START,actor:l[u-1].actor};break;case 23:this.$={type:"activeEnd",signalType:b.LINETYPE.ACTIVE_END,actor:l[u-1].actor};break;case 29:b.setDiagramTitle(l[u].substring(6)),this.$=l[u].substring(6);break;case 30:b.setDiagramTitle(l[u].substring(7)),this.$=l[u].substring(7);break;case 31:this.$=l[u].trim(),b.setAccTitle(this.$);break;case 32:case 33:this.$=l[u].trim(),b.setAccDescription(this.$);break;case 34:l[u-1].unshift({type:"loopStart",loopText:b.parseMessage(l[u-2]),signalType:b.LINETYPE.LOOP_START}),l[u-1].push({type:"loopEnd",loopText:l[u-2],signalType:b.LINETYPE.LOOP_END}),this.$=l[u-1];break;case 35:l[u-1].unshift({type:"rectStart",color:b.parseMessage(l[u-2]),signalType:b.LINETYPE.RECT_START}),l[u-1].push({type:"rectEnd",color:b.parseMessage(l[u-2]),signalType:b.LINETYPE.RECT_END}),this.$=l[u-1];break;case 36:l[u-1].unshift({type:"optStart",optText:b.parseMessage(l[u-2]),signalType:b.LINETYPE.OPT_START}),l[u-1].push({type:"optEnd",optText:b.parseMessage(l[u-2]),signalType:b.LINETYPE.OPT_END}),this.$=l[u-1];break;case 37:l[u-1].unshift({type:"altStart",altText:b.parseMessage(l[u-2]),signalType:b.LINETYPE.ALT_START}),l[u-1].push({type:"altEnd",signalType:b.LINETYPE.ALT_END}),this.$=l[u-1];break;case 38:l[u-1].unshift({type:"parStart",parText:b.parseMessage(l[u-2]),signalType:b.LINETYPE.PAR_START}),l[u-1].push({type:"parEnd",signalType:b.LINETYPE.PAR_END}),this.$=l[u-1];break;case 39:l[u-1].unshift({type:"parStart",parText:b.parseMessage(l[u-2]),signalType:b.LINETYPE.PAR_OVER_START}),l[u-1].push({type:"parEnd",signalType:b.LINETYPE.PAR_END}),this.$=l[u-1];break;case 40:l[u-1].unshift({type:"criticalStart",criticalText:b.parseMessage(l[u-2]),signalType:b.LINETYPE.CRITICAL_START}),l[u-1].push({type:"criticalEnd",signalType:b.LINETYPE.CRITICAL_END}),this.$=l[u-1];break;case 41:l[u-1].unshift({type:"breakStart",breakText:b.parseMessage(l[u-2]),signalType:b.LINETYPE.BREAK_START}),l[u-1].push({type:"breakEnd",optText:b.parseMessage(l[u-2]),signalType:b.LINETYPE.BREAK_END}),this.$=l[u-1];break;case 43:this.$=l[u-3].concat([{type:"option",optionText:b.parseMessage(l[u-1]),signalType:b.LINETYPE.CRITICAL_OPTION},l[u]]);break;case 45:this.$=l[u-3].concat([{type:"and",parText:b.parseMessage(l[u-1]),signalType:b.LINETYPE.PAR_AND},l[u]]);break;case 47:this.$=l[u-3].concat([{type:"else",altText:b.parseMessage(l[u-1]),signalType:b.LINETYPE.ALT_ELSE},l[u]]);break;case 48:l[u-3].draw="participant",l[u-3].type="addParticipant",l[u-3].description=b.parseMessage(l[u-1]),this.$=l[u-3];break;case 49:l[u-1].draw="participant",l[u-1].type="addParticipant",this.$=l[u-1];break;case 50:l[u-3].draw="actor",l[u-3].type="addParticipant",l[u-3].description=b.parseMessage(l[u-1]),this.$=l[u-3];break;case 51:l[u-1].draw="actor",l[u-1].type="addParticipant",this.$=l[u-1];break;case 52:l[u-1].type="destroyParticipant",this.$=l[u-1];break;case 53:this.$=[l[u-1],{type:"addNote",placement:l[u-2],actor:l[u-1].actor,text:l[u]}];break;case 54:l[u-2]=[].concat(l[u-1],l[u-1]).slice(0,2),l[u-2][0]=l[u-2][0].actor,l[u-2][1]=l[u-2][1].actor,this.$=[l[u-1],{type:"addNote",placement:b.PLACEMENT.OVER,actor:l[u-2].slice(0,2),text:l[u]}];break;case 55:this.$=[l[u-1],{type:"addLinks",actor:l[u-1].actor,text:l[u]}];break;case 56:this.$=[l[u-1],{type:"addALink",actor:l[u-1].actor,text:l[u]}];break;case 57:this.$=[l[u-1],{type:"addProperties",actor:l[u-1].actor,text:l[u]}];break;case 58:this.$=[l[u-1],{type:"addDetails",actor:l[u-1].actor,text:l[u]}];break;case 61:this.$=[l[u-2],l[u]];break;case 62:this.$=l[u];break;case 63:this.$=b.PLACEMENT.LEFTOF;break;case 64:this.$=b.PLACEMENT.RIGHTOF;break;case 65:this.$=[l[u-4],l[u-1],{type:"addMessage",from:l[u-4].actor,to:l[u-1].actor,signalType:l[u-3],msg:l[u],activate:!0},{type:"activeStart",signalType:b.LINETYPE.ACTIVE_START,actor:l[u-1].actor}];break;case 66:this.$=[l[u-4],l[u-1],{type:"addMessage",from:l[u-4].actor,to:l[u-1].actor,signalType:l[u-3],msg:l[u]},{type:"activeEnd",signalType:b.LINETYPE.ACTIVE_END,actor:l[u-4].actor}];break;case 67:this.$=[l[u-3],l[u-1],{type:"addMessage",from:l[u-3].actor,to:l[u-1].actor,signalType:l[u-2],msg:l[u]}];break;case 68:this.$={type:"addParticipant",actor:l[u]};break;case 69:this.$=b.LINETYPE.SOLID_OPEN;break;case 70:this.$=b.LINETYPE.DOTTED_OPEN;break;case 71:this.$=b.LINETYPE.SOLID;break;case 72:this.$=b.LINETYPE.BIDIRECTIONAL_SOLID;break;case 73:this.$=b.LINETYPE.DOTTED;break;case 74:this.$=b.LINETYPE.BIDIRECTIONAL_DOTTED;break;case 75:this.$=b.LINETYPE.SOLID_CROSS;break;case 76:this.$=b.LINETYPE.DOTTED_CROSS;break;case 77:this.$=b.LINETYPE.SOLID_POINT;break;case 78:this.$=b.LINETYPE.DOTTED_POINT;break;case 79:this.$=b.parseMessage(l[u].trim().substring(1));break}},"anonymous"),table:[{3:1,4:t,5:c,6:s},{1:[3]},{3:5,4:t,5:c,6:s},{3:6,4:t,5:c,6:s},e([1,4,5,13,14,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],a,{7:7}),{1:[2,1]},{1:[2,2]},{1:[2,3],4:i,5:n,8:8,9:10,12:12,13:d,14:h,17:15,18:r,21:g,22:40,23:E,24:19,25:20,26:21,27:22,28:23,29:f,30:T,31:m,33:w,35:k,36:O,37:S,38:B,39:D,41:F,43:q,44:X,46:tt,50:z,52:H,53:W,54:M,59:J,60:K,61:Z,62:et,70:v},e(y,[2,5]),{9:47,12:12,13:d,14:h,17:15,18:r,21:g,22:40,23:E,24:19,25:20,26:21,27:22,28:23,29:f,30:T,31:m,33:w,35:k,36:O,37:S,38:B,39:D,41:F,43:q,44:X,46:tt,50:z,52:H,53:W,54:M,59:J,60:K,61:Z,62:et,70:v},e(y,[2,7]),e(y,[2,8]),e(y,[2,14]),{12:48,50:z,52:H,53:W},{15:[1,49]},{5:[1,50]},{5:[1,53],19:[1,51],20:[1,52]},{22:54,70:v},{22:55,70:v},{5:[1,56]},{5:[1,57]},{5:[1,58]},{5:[1,59]},{5:[1,60]},e(y,[2,29]),e(y,[2,30]),{32:[1,61]},{34:[1,62]},e(y,[2,33]),{15:[1,63]},{15:[1,64]},{15:[1,65]},{15:[1,66]},{15:[1,67]},{15:[1,68]},{15:[1,69]},{15:[1,70]},{22:71,70:v},{22:72,70:v},{22:73,70:v},{67:74,71:[1,75],72:[1,76],73:[1,77],74:[1,78],75:[1,79],76:[1,80],77:[1,81],78:[1,82],79:[1,83],80:[1,84]},{55:85,57:[1,86],65:[1,87],66:[1,88]},{22:89,70:v},{22:90,70:v},{22:91,70:v},{22:92,70:v},e([5,51,64,71,72,73,74,75,76,77,78,79,80,81],[2,68]),e(y,[2,6]),e(y,[2,15]),e(P,[2,9],{10:93}),e(y,[2,17]),{5:[1,95],19:[1,94]},{5:[1,96]},e(y,[2,21]),{5:[1,97]},{5:[1,98]},e(y,[2,24]),e(y,[2,25]),e(y,[2,26]),e(y,[2,27]),e(y,[2,28]),e(y,[2,31]),e(y,[2,32]),e(Q,a,{7:99}),e(Q,a,{7:100}),e(Q,a,{7:101}),e(at,a,{40:102,7:103}),e(N,a,{42:104,7:105}),e(N,a,{7:105,42:106}),e(qt,a,{45:107,7:108}),e(Q,a,{7:109}),{5:[1,111],51:[1,110]},{5:[1,113],51:[1,112]},{5:[1,114]},{22:117,68:[1,115],69:[1,116],70:v},e(it,[2,69]),e(it,[2,70]),e(it,[2,71]),e(it,[2,72]),e(it,[2,73]),e(it,[2,74]),e(it,[2,75]),e(it,[2,76]),e(it,[2,77]),e(it,[2,78]),{22:118,70:v},{22:120,58:119,70:v},{70:[2,63]},{70:[2,64]},{56:121,81:ct},{56:123,81:ct},{56:124,81:ct},{56:125,81:ct},{4:[1,128],5:[1,130],11:127,12:129,16:[1,126],50:z,52:H,53:W},{5:[1,131]},e(y,[2,19]),e(y,[2,20]),e(y,[2,22]),e(y,[2,23]),{4:i,5:n,8:8,9:10,12:12,13:d,14:h,16:[1,132],17:15,18:r,21:g,22:40,23:E,24:19,25:20,26:21,27:22,28:23,29:f,30:T,31:m,33:w,35:k,36:O,37:S,38:B,39:D,41:F,43:q,44:X,46:tt,50:z,52:H,53:W,54:M,59:J,60:K,61:Z,62:et,70:v},{4:i,5:n,8:8,9:10,12:12,13:d,14:h,16:[1,133],17:15,18:r,21:g,22:40,23:E,24:19,25:20,26:21,27:22,28:23,29:f,30:T,31:m,33:w,35:k,36:O,37:S,38:B,39:D,41:F,43:q,44:X,46:tt,50:z,52:H,53:W,54:M,59:J,60:K,61:Z,62:et,70:v},{4:i,5:n,8:8,9:10,12:12,13:d,14:h,16:[1,134],17:15,18:r,21:g,22:40,23:E,24:19,25:20,26:21,27:22,28:23,29:f,30:T,31:m,33:w,35:k,36:O,37:S,38:B,39:D,41:F,43:q,44:X,46:tt,50:z,52:H,53:W,54:M,59:J,60:K,61:Z,62:et,70:v},{16:[1,135]},{4:i,5:n,8:8,9:10,12:12,13:d,14:h,16:[2,46],17:15,18:r,21:g,22:40,23:E,24:19,25:20,26:21,27:22,28:23,29:f,30:T,31:m,33:w,35:k,36:O,37:S,38:B,39:D,41:F,43:q,44:X,46:tt,49:[1,136],50:z,52:H,53:W,54:M,59:J,60:K,61:Z,62:et,70:v},{16:[1,137]},{4:i,5:n,8:8,9:10,12:12,13:d,14:h,16:[2,44],17:15,18:r,21:g,22:40,23:E,24:19,25:20,26:21,27:22,28:23,29:f,30:T,31:m,33:w,35:k,36:O,37:S,38:B,39:D,41:F,43:q,44:X,46:tt,48:[1,138],50:z,52:H,53:W,54:M,59:J,60:K,61:Z,62:et,70:v},{16:[1,139]},{16:[1,140]},{4:i,5:n,8:8,9:10,12:12,13:d,14:h,16:[2,42],17:15,18:r,21:g,22:40,23:E,24:19,25:20,26:21,27:22,28:23,29:f,30:T,31:m,33:w,35:k,36:O,37:S,38:B,39:D,41:F,43:q,44:X,46:tt,47:[1,141],50:z,52:H,53:W,54:M,59:J,60:K,61:Z,62:et,70:v},{4:i,5:n,8:8,9:10,12:12,13:d,14:h,16:[1,142],17:15,18:r,21:g,22:40,23:E,24:19,25:20,26:21,27:22,28:23,29:f,30:T,31:m,33:w,35:k,36:O,37:S,38:B,39:D,41:F,43:q,44:X,46:tt,50:z,52:H,53:W,54:M,59:J,60:K,61:Z,62:et,70:v},{15:[1,143]},e(y,[2,49]),{15:[1,144]},e(y,[2,51]),e(y,[2,52]),{22:145,70:v},{22:146,70:v},{56:147,81:ct},{56:148,81:ct},{56:149,81:ct},{64:[1,150],81:[2,62]},{5:[2,55]},{5:[2,79]},{5:[2,56]},{5:[2,57]},{5:[2,58]},e(y,[2,16]),e(P,[2,10]),{12:151,50:z,52:H,53:W},e(P,[2,12]),e(P,[2,13]),e(y,[2,18]),e(y,[2,34]),e(y,[2,35]),e(y,[2,36]),e(y,[2,37]),{15:[1,152]},e(y,[2,38]),{15:[1,153]},e(y,[2,39]),e(y,[2,40]),{15:[1,154]},e(y,[2,41]),{5:[1,155]},{5:[1,156]},{56:157,81:ct},{56:158,81:ct},{5:[2,67]},{5:[2,53]},{5:[2,54]},{22:159,70:v},e(P,[2,11]),e(at,a,{7:103,40:160}),e(N,a,{7:105,42:161}),e(qt,a,{7:108,45:162}),e(y,[2,48]),e(y,[2,50]),{5:[2,65]},{5:[2,66]},{81:[2,61]},{16:[2,47]},{16:[2,45]},{16:[2,43]}],defaultActions:{5:[2,1],6:[2,2],87:[2,63],88:[2,64],121:[2,55],122:[2,79],123:[2,56],124:[2,57],125:[2,58],147:[2,67],148:[2,53],149:[2,54],157:[2,65],158:[2,66],159:[2,61],160:[2,47],161:[2,45],162:[2,43]},parseError:p(function(I,_){if(_.recoverable)this.trace(I);else{var A=new Error(I);throw A.hash=_,A}},"parseError"),parse:p(function(I){var _=this,A=[0],b=[],R=[null],l=[],Et=this.table,u="",wt=0,zt=0,ue=2,Ht=1,pe=l.slice.call(arguments,1),V=Object.create(this.lexer),ht={yy:{}};for(var St in this.yy)Object.prototype.hasOwnProperty.call(this.yy,St)&&(ht.yy[St]=this.yy[St]);V.setInput(I,ht.yy),ht.yy.lexer=V,ht.yy.parser=this,typeof V.yylloc>"u"&&(V.yylloc={});var Mt=V.yylloc;l.push(Mt);var fe=V.options&&V.options.ranges;typeof ht.yy.parseError=="function"?this.parseError=ht.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function ge(j){A.length=A.length-2*j,R.length=R.length-j,l.length=l.length-j}p(ge,"popStack");function Kt(){var j;return j=b.pop()||V.lex()||Ht,typeof j!="number"&&(j instanceof Array&&(b=j,j=b.pop()),j=_.symbols_[j]||j),j}p(Kt,"lex");for(var U,ut,st,Rt,gt={},It,lt,Ut,Lt;;){if(ut=A[A.length-1],this.defaultActions[ut]?st=this.defaultActions[ut]:((U===null||typeof U>"u")&&(U=Kt()),st=Et[ut]&&Et[ut][U]),typeof st>"u"||!st.length||!st[0]){var Dt="";Lt=[];for(It in Et[ut])this.terminals_[It]&&It>ue&&Lt.push("'"+this.terminals_[It]+"'");V.showPosition?Dt="Parse error on line "+(wt+1)+`:
`+V.showPosition()+`
Expecting `+Lt.join(", ")+", got '"+(this.terminals_[U]||U)+"'":Dt="Parse error on line "+(wt+1)+": Unexpected "+(U==Ht?"end of input":"'"+(this.terminals_[U]||U)+"'"),this.parseError(Dt,{text:V.match,token:this.terminals_[U]||U,line:V.yylineno,loc:Mt,expected:Lt})}if(st[0]instanceof Array&&st.length>1)throw new Error("Parse Error: multiple actions possible at state: "+ut+", token: "+U);switch(st[0]){case 1:A.push(U),R.push(V.yytext),l.push(V.yylloc),A.push(st[1]),U=null,zt=V.yyleng,u=V.yytext,wt=V.yylineno,Mt=V.yylloc;break;case 2:if(lt=this.productions_[st[1]][1],gt.$=R[R.length-lt],gt._$={first_line:l[l.length-(lt||1)].first_line,last_line:l[l.length-1].last_line,first_column:l[l.length-(lt||1)].first_column,last_column:l[l.length-1].last_column},fe&&(gt._$.range=[l[l.length-(lt||1)].range[0],l[l.length-1].range[1]]),Rt=this.performAction.apply(gt,[u,zt,wt,ht.yy,st[1],R,l].concat(pe)),typeof Rt<"u")return Rt;lt&&(A=A.slice(0,-1*lt*2),R=R.slice(0,-1*lt),l=l.slice(0,-1*lt)),A.push(this.productions_[st[1]][0]),R.push(gt.$),l.push(gt._$),Ut=Et[A[A.length-2]][A[A.length-1]],A.push(Ut);break;case 3:return!0}}return!0},"parse")},he=function(){var dt={EOF:1,parseError:p(function(_,A){if(this.yy.parser)this.yy.parser.parseError(_,A);else throw new Error(_)},"parseError"),setInput:p(function(I,_){return this.yy=_||this.yy||{},this._input=I,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:p(function(){var I=this._input[0];this.yytext+=I,this.yyleng++,this.offset++,this.match+=I,this.matched+=I;var _=I.match(/(?:\r\n?|\n).*/g);return _?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),I},"input"),unput:p(function(I){var _=I.length,A=I.split(/(?:\r\n?|\n)/g);this._input=I+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-_),this.offset-=_;var b=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),A.length-1&&(this.yylineno-=A.length-1);var R=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:A?(A.length===b.length?this.yylloc.first_column:0)+b[b.length-A.length].length-A[0].length:this.yylloc.first_column-_},this.options.ranges&&(this.yylloc.range=[R[0],R[0]+this.yyleng-_]),this.yyleng=this.yytext.length,this},"unput"),more:p(function(){return this._more=!0,this},"more"),reject:p(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:p(function(I){this.unput(this.match.slice(I))},"less"),pastInput:p(function(){var I=this.matched.substr(0,this.matched.length-this.match.length);return(I.length>20?"...":"")+I.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:p(function(){var I=this.match;return I.length<20&&(I+=this._input.substr(0,20-I.length)),(I.substr(0,20)+(I.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:p(function(){var I=this.pastInput(),_=new Array(I.length+1).join("-");return I+this.upcomingInput()+`
`+_+"^"},"showPosition"),test_match:p(function(I,_){var A,b,R;if(this.options.backtrack_lexer&&(R={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(R.yylloc.range=this.yylloc.range.slice(0))),b=I[0].match(/(?:\r\n?|\n).*/g),b&&(this.yylineno+=b.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:b?b[b.length-1].length-b[b.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+I[0].length},this.yytext+=I[0],this.match+=I[0],this.matches=I,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(I[0].length),this.matched+=I[0],A=this.performAction.call(this,this.yy,this,_,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),A)return A;if(this._backtrack){for(var l in R)this[l]=R[l];return!1}return!1},"test_match"),next:p(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var I,_,A,b;this._more||(this.yytext="",this.match="");for(var R=this._currentRules(),l=0;l<R.length;l++)if(A=this._input.match(this.rules[R[l]]),A&&(!_||A[0].length>_[0].length)){if(_=A,b=l,this.options.backtrack_lexer){if(I=this.test_match(A,R[l]),I!==!1)return I;if(this._backtrack){_=!1;continue}else return!1}else if(!this.options.flex)break}return _?(I=this.test_match(_,R[b]),I!==!1?I:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:p(function(){var _=this.next();return _||this.lex()},"lex"),begin:p(function(_){this.conditionStack.push(_)},"begin"),popState:p(function(){var _=this.conditionStack.length-1;return _>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:p(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:p(function(_){return _=this.conditionStack.length-1-Math.abs(_||0),_>=0?this.conditionStack[_]:"INITIAL"},"topState"),pushState:p(function(_){this.begin(_)},"pushState"),stateStackSize:p(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:p(function(_,A,b,R){switch(b){case 0:return 5;case 1:break;case 2:break;case 3:break;case 4:break;case 5:break;case 6:return 19;case 7:return this.begin("LINE"),14;case 8:return this.begin("ID"),50;case 9:return this.begin("ID"),52;case 10:return 13;case 11:return this.begin("ID"),53;case 12:return A.yytext=A.yytext.trim(),this.begin("ALIAS"),70;case 13:return this.popState(),this.popState(),this.begin("LINE"),51;case 14:return this.popState(),this.popState(),5;case 15:return this.begin("LINE"),36;case 16:return this.begin("LINE"),37;case 17:return this.begin("LINE"),38;case 18:return this.begin("LINE"),39;case 19:return this.begin("LINE"),49;case 20:return this.begin("LINE"),41;case 21:return this.begin("LINE"),43;case 22:return this.begin("LINE"),48;case 23:return this.begin("LINE"),44;case 24:return this.begin("LINE"),47;case 25:return this.begin("LINE"),46;case 26:return this.popState(),15;case 27:return 16;case 28:return 65;case 29:return 66;case 30:return 59;case 31:return 60;case 32:return 61;case 33:return 62;case 34:return 57;case 35:return 54;case 36:return this.begin("ID"),21;case 37:return this.begin("ID"),23;case 38:return 29;case 39:return 30;case 40:return this.begin("acc_title"),31;case 41:return this.popState(),"acc_title_value";case 42:return this.begin("acc_descr"),33;case 43:return this.popState(),"acc_descr_value";case 44:this.begin("acc_descr_multiline");break;case 45:this.popState();break;case 46:return"acc_descr_multiline_value";case 47:return 6;case 48:return 18;case 49:return 20;case 50:return 64;case 51:return 5;case 52:return A.yytext=A.yytext.trim(),70;case 53:return 73;case 54:return 74;case 55:return 75;case 56:return 76;case 57:return 71;case 58:return 72;case 59:return 77;case 60:return 78;case 61:return 79;case 62:return 80;case 63:return 81;case 64:return 68;case 65:return 69;case 66:return 5;case 67:return"INVALID"}},"anonymous"),rules:[/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:((?!\n)\s)+)/i,/^(?:#[^\n]*)/i,/^(?:%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[0-9]+(?=[ \n]+))/i,/^(?:box\b)/i,/^(?:participant\b)/i,/^(?:actor\b)/i,/^(?:create\b)/i,/^(?:destroy\b)/i,/^(?:[^\<->\->:\n,;]+?([\-]*[^\<->\->:\n,;]+?)*?(?=((?!\n)\s)+as(?!\n)\s|[#\n;]|$))/i,/^(?:as\b)/i,/^(?:(?:))/i,/^(?:loop\b)/i,/^(?:rect\b)/i,/^(?:opt\b)/i,/^(?:alt\b)/i,/^(?:else\b)/i,/^(?:par\b)/i,/^(?:par_over\b)/i,/^(?:and\b)/i,/^(?:critical\b)/i,/^(?:option\b)/i,/^(?:break\b)/i,/^(?:(?:[:]?(?:no)?wrap)?[^#\n;]*)/i,/^(?:end\b)/i,/^(?:left of\b)/i,/^(?:right of\b)/i,/^(?:links\b)/i,/^(?:link\b)/i,/^(?:properties\b)/i,/^(?:details\b)/i,/^(?:over\b)/i,/^(?:note\b)/i,/^(?:activate\b)/i,/^(?:deactivate\b)/i,/^(?:title\s[^#\n;]+)/i,/^(?:title:\s[^#\n;]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:sequenceDiagram\b)/i,/^(?:autonumber\b)/i,/^(?:off\b)/i,/^(?:,)/i,/^(?:;)/i,/^(?:[^\+\<->\->:\n,;]+((?!(-x|--x|-\)|--\)))[\-]*[^\+\<->\->:\n,;]+)*)/i,/^(?:->>)/i,/^(?:<<->>)/i,/^(?:-->>)/i,/^(?:<<-->>)/i,/^(?:->)/i,/^(?:-->)/i,/^(?:-[x])/i,/^(?:--[x])/i,/^(?:-[\)])/i,/^(?:--[\)])/i,/^(?::(?:(?:no)?wrap)?[^#\n;]+)/i,/^(?:\+)/i,/^(?:-)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[45,46],inclusive:!1},acc_descr:{rules:[43],inclusive:!1},acc_title:{rules:[41],inclusive:!1},ID:{rules:[2,3,12],inclusive:!1},ALIAS:{rules:[2,3,13,14],inclusive:!1},LINE:{rules:[2,3,26],inclusive:!1},INITIAL:{rules:[0,1,3,4,5,6,7,8,9,10,11,15,16,17,18,19,20,21,22,23,24,25,27,28,29,30,31,32,33,34,35,36,37,38,39,40,42,44,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67],inclusive:!0}}};return dt}();vt.lexer=he;function mt(){this.yy={}}return p(mt,"Parser"),mt.prototype=vt,vt.Parser=mt,new mt}();Ot.parser=Ot;var Se=Ot,Me={SOLID:0,DOTTED:1,NOTE:2,SOLID_CROSS:3,DOTTED_CROSS:4,SOLID_OPEN:5,DOTTED_OPEN:6,LOOP_START:10,LOOP_END:11,ALT_START:12,ALT_ELSE:13,ALT_END:14,OPT_START:15,OPT_END:16,ACTIVE_START:17,ACTIVE_END:18,PAR_START:19,PAR_AND:20,PAR_END:21,RECT_START:22,RECT_END:23,SOLID_POINT:24,DOTTED_POINT:25,AUTONUMBER:26,CRITICAL_START:27,CRITICAL_OPTION:28,CRITICAL_END:29,BREAK_START:30,BREAK_END:31,PAR_OVER_START:32,BIDIRECTIONAL_SOLID:33,BIDIRECTIONAL_DOTTED:34},Re={FILLED:0,OPEN:1},De={LEFTOF:0,RIGHTOF:1,OVER:2},Tt,Ce=(Tt=class{constructor(){this.state=new be(()=>({prevActor:void 0,actors:new Map,createdActors:new Map,destroyedActors:new Map,boxes:[],messages:[],notes:[],sequenceNumbersEnabled:!1,wrapEnabled:void 0,currentBox:void 0,lastCreated:void 0,lastDestroyed:void 0})),this.setAccTitle=Gt,this.setAccDescription=Le,this.setDiagramTitle=_e,this.getAccTitle=Pe,this.getAccDescription=Ae,this.getDiagramTitle=ke,this.apply=this.apply.bind(this),this.parseBoxData=this.parseBoxData.bind(this),this.parseMessage=this.parseMessage.bind(this),this.clear(),this.setWrap($().wrap),this.LINETYPE=Me,this.ARROWTYPE=Re,this.PLACEMENT=De}addBox(t){this.state.records.boxes.push({name:t.text,wrap:t.wrap??this.autoWrap(),fill:t.color,actorKeys:[]}),this.state.records.currentBox=this.state.records.boxes.slice(-1)[0]}addActor(t,c,s,a){let i=this.state.records.currentBox;const n=this.state.records.actors.get(t);if(n){if(this.state.records.currentBox&&n.box&&this.state.records.currentBox!==n.box)throw new Error(`A same participant should only be defined in one Box: ${n.name} can't be in '${n.box.name}' and in '${this.state.records.currentBox.name}' at the same time.`);if(i=n.box?n.box:this.state.records.currentBox,n.box=i,n&&c===n.name&&s==null)return}if((s==null?void 0:s.text)==null&&(s={text:c,type:a}),(a==null||s.text==null)&&(s={text:c,type:a}),this.state.records.actors.set(t,{box:i,name:c,description:s.text,wrap:s.wrap??this.autoWrap(),prevActor:this.state.records.prevActor,links:{},properties:{},actorCnt:null,rectData:null,type:a??"participant"}),this.state.records.prevActor){const d=this.state.records.actors.get(this.state.records.prevActor);d&&(d.nextActor=t)}this.state.records.currentBox&&this.state.records.currentBox.actorKeys.push(t),this.state.records.prevActor=t}activationCount(t){let c,s=0;if(!t)return 0;for(c=0;c<this.state.records.messages.length;c++)this.state.records.messages[c].type===this.LINETYPE.ACTIVE_START&&this.state.records.messages[c].from===t&&s++,this.state.records.messages[c].type===this.LINETYPE.ACTIVE_END&&this.state.records.messages[c].from===t&&s--;return s}addMessage(t,c,s,a){this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:t,to:c,message:s.text,wrap:s.wrap??this.autoWrap(),answer:a})}addSignal(t,c,s,a,i=!1){if(a===this.LINETYPE.ACTIVE_END&&this.activationCount(t??"")<1){const d=new Error("Trying to inactivate an inactive participant ("+t+")");throw d.hash={text:"->>-",token:"->>-",line:"1",loc:{first_line:1,last_line:1,first_column:1,last_column:1},expected:["'ACTIVE_PARTICIPANT'"]},d}return this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:t,to:c,message:(s==null?void 0:s.text)??"",wrap:(s==null?void 0:s.wrap)??this.autoWrap(),type:a,activate:i}),!0}hasAtLeastOneBox(){return this.state.records.boxes.length>0}hasAtLeastOneBoxWithTitle(){return this.state.records.boxes.some(t=>t.name)}getMessages(){return this.state.records.messages}getBoxes(){return this.state.records.boxes}getActors(){return this.state.records.actors}getCreatedActors(){return this.state.records.createdActors}getDestroyedActors(){return this.state.records.destroyedActors}getActor(t){return this.state.records.actors.get(t)}getActorKeys(){return[...this.state.records.actors.keys()]}enableSequenceNumbers(){this.state.records.sequenceNumbersEnabled=!0}disableSequenceNumbers(){this.state.records.sequenceNumbersEnabled=!1}showSequenceNumbers(){return this.state.records.sequenceNumbersEnabled}setWrap(t){this.state.records.wrapEnabled=t}extractWrap(t){if(t===void 0)return{};t=t.trim();const c=/^:?wrap:/.exec(t)!==null?!0:/^:?nowrap:/.exec(t)!==null?!1:void 0;return{cleanedText:(c===void 0?t:t.replace(/^:?(?:no)?wrap:/,"")).trim(),wrap:c}}autoWrap(){var t;return this.state.records.wrapEnabled!==void 0?this.state.records.wrapEnabled:((t=$().sequence)==null?void 0:t.wrap)??!1}clear(){this.state.reset(),Ne()}parseMessage(t){const c=t.trim(),{wrap:s,cleanedText:a}=this.extractWrap(c),i={text:a,wrap:s};return G.debug(`parseMessage: ${JSON.stringify(i)}`),i}parseBoxData(t){const c=/^((?:rgba?|hsla?)\s*\(.*\)|\w*)(.*)$/.exec(t);let s=c!=null&&c[1]?c[1].trim():"transparent",a=c!=null&&c[2]?c[2].trim():void 0;if(window!=null&&window.CSS)window.CSS.supports("color",s)||(s="transparent",a=t.trim());else{const d=new Option().style;d.color=s,d.color!==s&&(s="transparent",a=t.trim())}const{wrap:i,cleanedText:n}=this.extractWrap(a);return{text:n?Pt(n,$()):void 0,color:s,wrap:i}}addNote(t,c,s){const a={actor:t,placement:c,message:s.text,wrap:s.wrap??this.autoWrap()},i=[].concat(t,t);this.state.records.notes.push(a),this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:i[0],to:i[1],message:s.text,wrap:s.wrap??this.autoWrap(),type:this.LINETYPE.NOTE,placement:c})}addLinks(t,c){const s=this.getActor(t);try{let a=Pt(c.text,$());a=a.replace(/&equals;/g,"="),a=a.replace(/&amp;/g,"&");const i=JSON.parse(a);this.insertLinks(s,i)}catch(a){G.error("error while parsing actor link text",a)}}addALink(t,c){const s=this.getActor(t);try{const a={};let i=Pt(c.text,$());const n=i.indexOf("@");i=i.replace(/&equals;/g,"="),i=i.replace(/&amp;/g,"&");const d=i.slice(0,n-1).trim(),h=i.slice(n+1).trim();a[d]=h,this.insertLinks(s,a)}catch(a){G.error("error while parsing actor link text",a)}}insertLinks(t,c){if(t.links==null)t.links=c;else for(const s in c)t.links[s]=c[s]}addProperties(t,c){const s=this.getActor(t);try{const a=Pt(c.text,$()),i=JSON.parse(a);this.insertProperties(s,i)}catch(a){G.error("error while parsing actor properties text",a)}}insertProperties(t,c){if(t.properties==null)t.properties=c;else for(const s in c)t.properties[s]=c[s]}boxEnd(){this.state.records.currentBox=void 0}addDetails(t,c){const s=this.getActor(t),a=document.getElementById(c.text);try{const i=a.innerHTML,n=JSON.parse(i);n.properties&&this.insertProperties(s,n.properties),n.links&&this.insertLinks(s,n.links)}catch(i){G.error("error while parsing actor details text",i)}}getActorProperty(t,c){if((t==null?void 0:t.properties)!==void 0)return t.properties[c]}apply(t){if(Array.isArray(t))t.forEach(c=>{this.apply(c)});else switch(t.type){case"sequenceIndex":this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:void 0,to:void 0,message:{start:t.sequenceIndex,step:t.sequenceIndexStep,visible:t.sequenceVisible},wrap:!1,type:t.signalType});break;case"addParticipant":this.addActor(t.actor,t.actor,t.description,t.draw);break;case"createParticipant":if(this.state.records.actors.has(t.actor))throw new Error("It is not possible to have actors with the same id, even if one is destroyed before the next is created. Use 'AS' aliases to simulate the behavior");this.state.records.lastCreated=t.actor,this.addActor(t.actor,t.actor,t.description,t.draw),this.state.records.createdActors.set(t.actor,this.state.records.messages.length);break;case"destroyParticipant":this.state.records.lastDestroyed=t.actor,this.state.records.destroyedActors.set(t.actor,this.state.records.messages.length);break;case"activeStart":this.addSignal(t.actor,void 0,void 0,t.signalType);break;case"activeEnd":this.addSignal(t.actor,void 0,void 0,t.signalType);break;case"addNote":this.addNote(t.actor,t.placement,t.text);break;case"addLinks":this.addLinks(t.actor,t.text);break;case"addALink":this.addALink(t.actor,t.text);break;case"addProperties":this.addProperties(t.actor,t.text);break;case"addDetails":this.addDetails(t.actor,t.text);break;case"addMessage":if(this.state.records.lastCreated){if(t.to!==this.state.records.lastCreated)throw new Error("The created participant "+this.state.records.lastCreated.name+" does not have an associated creating message after its declaration. Please check the sequence diagram.");this.state.records.lastCreated=void 0}else if(this.state.records.lastDestroyed){if(t.to!==this.state.records.lastDestroyed&&t.from!==this.state.records.lastDestroyed)throw new Error("The destroyed participant "+this.state.records.lastDestroyed.name+" does not have an associated destroying message after its declaration. Please check the sequence diagram.");this.state.records.lastDestroyed=void 0}this.addSignal(t.from,t.to,t.msg,t.signalType,t.activate);break;case"boxStart":this.addBox(t.boxData);break;case"boxEnd":this.boxEnd();break;case"loopStart":this.addSignal(void 0,void 0,t.loopText,t.signalType);break;case"loopEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"rectStart":this.addSignal(void 0,void 0,t.color,t.signalType);break;case"rectEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"optStart":this.addSignal(void 0,void 0,t.optText,t.signalType);break;case"optEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"altStart":this.addSignal(void 0,void 0,t.altText,t.signalType);break;case"else":this.addSignal(void 0,void 0,t.altText,t.signalType);break;case"altEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"setAccTitle":Gt(t.text);break;case"parStart":this.addSignal(void 0,void 0,t.parText,t.signalType);break;case"and":this.addSignal(void 0,void 0,t.parText,t.signalType);break;case"parEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"criticalStart":this.addSignal(void 0,void 0,t.criticalText,t.signalType);break;case"option":this.addSignal(void 0,void 0,t.optionText,t.signalType);break;case"criticalEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"breakStart":this.addSignal(void 0,void 0,t.breakText,t.signalType);break;case"breakEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break}}getConfig(){return $().sequence}},p(Tt,"SequenceDB"),Tt),Oe=p(e=>`.actor {
    stroke: ${e.actorBorder};
    fill: ${e.actorBkg};
  }

  text.actor > tspan {
    fill: ${e.actorTextColor};
    stroke: none;
  }

  .actor-line {
    stroke: ${e.actorLineColor};
  }

  .messageLine0 {
    stroke-width: 1.5;
    stroke-dasharray: none;
    stroke: ${e.signalColor};
  }

  .messageLine1 {
    stroke-width: 1.5;
    stroke-dasharray: 2, 2;
    stroke: ${e.signalColor};
  }

  #arrowhead path {
    fill: ${e.signalColor};
    stroke: ${e.signalColor};
  }

  .sequenceNumber {
    fill: ${e.sequenceNumberColor};
  }

  #sequencenumber {
    fill: ${e.signalColor};
  }

  #crosshead path {
    fill: ${e.signalColor};
    stroke: ${e.signalColor};
  }

  .messageText {
    fill: ${e.signalTextColor};
    stroke: none;
  }

  .labelBox {
    stroke: ${e.labelBoxBorderColor};
    fill: ${e.labelBoxBkgColor};
  }

  .labelText, .labelText > tspan {
    fill: ${e.labelTextColor};
    stroke: none;
  }

  .loopText, .loopText > tspan {
    fill: ${e.loopTextColor};
    stroke: none;
  }

  .loopLine {
    stroke-width: 2px;
    stroke-dasharray: 2, 2;
    stroke: ${e.labelBoxBorderColor};
    fill: ${e.labelBoxBorderColor};
  }

  .note {
    //stroke: #decc93;
    stroke: ${e.noteBorderColor};
    fill: ${e.noteBkgColor};
  }

  .noteText, .noteText > tspan {
    fill: ${e.noteTextColor};
    stroke: none;
  }

  .activation0 {
    fill: ${e.activationBkgColor};
    stroke: ${e.activationBorderColor};
  }

  .activation1 {
    fill: ${e.activationBkgColor};
    stroke: ${e.activationBorderColor};
  }

  .activation2 {
    fill: ${e.activationBkgColor};
    stroke: ${e.activationBorderColor};
  }

  .actorPopupMenu {
    position: absolute;
  }

  .actorPopupMenuPanel {
    position: absolute;
    fill: ${e.actorBkg};
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    filter: drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));
}
  .actor-man line {
    stroke: ${e.actorBorder};
    fill: ${e.actorBkg};
  }
  .actor-man circle, line {
    stroke: ${e.actorBorder};
    fill: ${e.actorBkg};
    stroke-width: 2px;
  }
`,"getStyles"),Be=Oe,pt=18*2,$t="actor-top",te="actor-bottom",Ve="actor-box",Xt="actor-man",Wt=p(function(e,t){return Te(e,t)},"drawRect"),Ye=p(function(e,t,c,s,a){if(t.links===void 0||t.links===null||Object.keys(t.links).length===0)return{height:0,width:0};const i=t.links,n=t.actorCnt,d=t.rectData;var h="none";a&&(h="block !important");const r=e.append("g");r.attr("id","actor"+n+"_popup"),r.attr("class","actorPopupMenu"),r.attr("display",h);var g="";d.class!==void 0&&(g=" "+d.class);let E=d.width>c?d.width:c;const f=r.append("rect");if(f.attr("class","actorPopupMenuPanel"+g),f.attr("x",d.x),f.attr("y",d.height),f.attr("fill",d.fill),f.attr("stroke",d.stroke),f.attr("width",E),f.attr("height",d.height),f.attr("rx",d.rx),f.attr("ry",d.ry),i!=null){var T=20;for(let k in i){var m=r.append("a"),w=Zt.sanitizeUrl(i[k]);m.attr("xlink:href",w),m.attr("target","_blank"),ss(s)(k,m,d.x+10,d.height+T,E,20,{class:"actor"},s),T+=30}}return f.attr("height",T),{height:d.height+T,width:E}},"drawPopup"),We=p(function(e){return"var pu = document.getElementById('"+e+"'); if (pu != null) { pu.style.display = pu.style.display == 'block' ? 'none' : 'block'; }"},"popupMenuToggle"),At=p(async function(e,t,c=null){let s=e.append("foreignObject");const a=await jt(t.text,Ct()),n=s.append("xhtml:div").attr("style","width: fit-content;").attr("xmlns","http://www.w3.org/1999/xhtml").html(a).node().getBoundingClientRect();if(s.attr("height",Math.round(n.height)).attr("width",Math.round(n.width)),t.class==="noteText"){const d=e.node().firstChild;d.setAttribute("height",n.height+2*t.textMargin);const h=d.getBBox();s.attr("x",Math.round(h.x+h.width/2-n.width/2)).attr("y",Math.round(h.y+h.height/2-n.height/2))}else if(c){let{startx:d,stopx:h,starty:r}=c;if(d>h){const g=d;d=h,h=g}s.attr("x",Math.round(d+Math.abs(d-h)/2-n.width/2)),t.class==="loopText"?s.attr("y",Math.round(r)):s.attr("y",Math.round(r-n.height))}return[s]},"drawKatex"),yt=p(function(e,t){let c=0,s=0;const a=t.text.split(L.lineBreakRegex),[i,n]=Qt(t.fontSize);let d=[],h=0,r=p(()=>t.y,"yfunc");if(t.valign!==void 0&&t.textMargin!==void 0&&t.textMargin>0)switch(t.valign){case"top":case"start":r=p(()=>Math.round(t.y+t.textMargin),"yfunc");break;case"middle":case"center":r=p(()=>Math.round(t.y+(c+s+t.textMargin)/2),"yfunc");break;case"bottom":case"end":r=p(()=>Math.round(t.y+(c+s+2*t.textMargin)-t.textMargin),"yfunc");break}if(t.anchor!==void 0&&t.textMargin!==void 0&&t.width!==void 0)switch(t.anchor){case"left":case"start":t.x=Math.round(t.x+t.textMargin),t.anchor="start",t.dominantBaseline="middle",t.alignmentBaseline="middle";break;case"middle":case"center":t.x=Math.round(t.x+t.width/2),t.anchor="middle",t.dominantBaseline="middle",t.alignmentBaseline="middle";break;case"right":case"end":t.x=Math.round(t.x+t.width-t.textMargin),t.anchor="end",t.dominantBaseline="middle",t.alignmentBaseline="middle";break}for(let[g,E]of a.entries()){t.textMargin!==void 0&&t.textMargin===0&&i!==void 0&&(h=g*i);const f=e.append("text");f.attr("x",t.x),f.attr("y",r()),t.anchor!==void 0&&f.attr("text-anchor",t.anchor).attr("dominant-baseline",t.dominantBaseline).attr("alignment-baseline",t.alignmentBaseline),t.fontFamily!==void 0&&f.style("font-family",t.fontFamily),n!==void 0&&f.style("font-size",n),t.fontWeight!==void 0&&f.style("font-weight",t.fontWeight),t.fill!==void 0&&f.attr("fill",t.fill),t.class!==void 0&&f.attr("class",t.class),t.dy!==void 0?f.attr("dy",t.dy):h!==0&&f.attr("dy",h);const T=E||ve;if(t.tspan){const m=f.append("tspan");m.attr("x",t.x),t.fill!==void 0&&m.attr("fill",t.fill),m.text(T)}else f.text(T);t.valign!==void 0&&t.textMargin!==void 0&&t.textMargin>0&&(s+=(f._groups||f)[0][0].getBBox().height,c=s),d.push(f)}return d},"drawText"),ee=p(function(e,t){function c(a,i,n,d,h){return a+","+i+" "+(a+n)+","+i+" "+(a+n)+","+(i+d-h)+" "+(a+n-h*1.2)+","+(i+d)+" "+a+","+(i+d)}p(c,"genPoints");const s=e.append("polygon");return s.attr("points",c(t.x,t.y,t.width,t.height,7)),s.attr("class","labelBox"),t.y=t.y+t.height/2,yt(e,t),s},"drawLabel"),nt=-1,se=p((e,t,c,s)=>{e.select&&c.forEach(a=>{const i=t.get(a),n=e.select("#actor"+i.actorCnt);!s.mirrorActors&&i.stopy?n.attr("y2",i.stopy+i.height/2):s.mirrorActors&&n.attr("y2",i.stopy)})},"fixLifeLineHeights"),Fe=p(function(e,t,c,s){var T,m;const a=s?t.stopy:t.starty,i=t.x+t.width/2,n=a+t.height,d=e.append("g").lower();var h=d;s||(nt++,Object.keys(t.links||{}).length&&!c.forceMenus&&h.attr("onclick",We(`actor${nt}_popup`)).attr("cursor","pointer"),h.append("line").attr("id","actor"+nt).attr("x1",i).attr("y1",n).attr("x2",i).attr("y2",2e3).attr("class","actor-line 200").attr("stroke-width","0.5px").attr("stroke","#999").attr("name",t.name),h=d.append("g"),t.actorCnt=nt,t.links!=null&&h.attr("id","root-"+nt));const r=kt();var g="actor";(T=t.properties)!=null&&T.class?g=t.properties.class:r.fill="#eaeaea",s?g+=` ${te}`:g+=` ${$t}`,r.x=t.x,r.y=a,r.width=t.width,r.height=t.height,r.class=g,r.rx=3,r.ry=3,r.name=t.name;const E=Wt(h,r);if(t.rectData=r,(m=t.properties)!=null&&m.icon){const w=t.properties.icon.trim();w.charAt(0)==="@"?ye(h,r.x+r.width-20,r.y+10,w.substr(1)):Ee(h,r.x+r.width-20,r.y+10,w)}Ft(c,ot(t.description))(t.description,h,r.x,r.y,r.width,r.height,{class:`actor ${Ve}`},c);let f=t.height;if(E.node){const w=E.node().getBBox();t.height=w.height,f=w.height}return f},"drawActorTypeParticipant"),qe=p(function(e,t,c,s){const a=s?t.stopy:t.starty,i=t.x+t.width/2,n=a+80,d=e.append("g").lower();s||(nt++,d.append("line").attr("id","actor"+nt).attr("x1",i).attr("y1",n).attr("x2",i).attr("y2",2e3).attr("class","actor-line 200").attr("stroke-width","0.5px").attr("stroke","#999").attr("name",t.name),t.actorCnt=nt);const h=e.append("g");let r=Xt;s?r+=` ${te}`:r+=` ${$t}`,h.attr("class",r),h.attr("name",t.name);const g=kt();g.x=t.x,g.y=a,g.fill="#eaeaea",g.width=t.width,g.height=t.height,g.class="actor",g.rx=3,g.ry=3,h.append("line").attr("id","actor-man-torso"+nt).attr("x1",i).attr("y1",a+25).attr("x2",i).attr("y2",a+45),h.append("line").attr("id","actor-man-arms"+nt).attr("x1",i-pt/2).attr("y1",a+33).attr("x2",i+pt/2).attr("y2",a+33),h.append("line").attr("x1",i-pt/2).attr("y1",a+60).attr("x2",i).attr("y2",a+45),h.append("line").attr("x1",i).attr("y1",a+45).attr("x2",i+pt/2-2).attr("y2",a+60);const E=h.append("circle");E.attr("cx",t.x+t.width/2),E.attr("cy",a+10),E.attr("r",15),E.attr("width",t.width),E.attr("height",t.height);const f=h.node().getBBox();return t.height=f.height,Ft(c,ot(t.description))(t.description,h,g.x,g.y+35,g.width,g.height,{class:`actor ${Xt}`},c),t.height},"drawActorTypeActor"),ze=p(async function(e,t,c,s){switch(t.type){case"actor":return await qe(e,t,c,s);case"participant":return await Fe(e,t,c,s)}},"drawActor"),He=p(function(e,t,c){const a=e.append("g");ae(a,t),t.name&&Ft(c)(t.name,a,t.x,t.y+(t.textMaxHeight||0)/2,t.width,0,{class:"text"},c),a.lower()},"drawBox"),Ke=p(function(e){return e.append("g")},"anchorElement"),Ue=p(function(e,t,c,s,a){const i=kt(),n=t.anchored;i.x=t.startx,i.y=t.starty,i.class="activation"+a%3,i.width=t.stopx-t.startx,i.height=c-t.starty,Wt(n,i)},"drawActivation"),Ge=p(async function(e,t,c,s){const{boxMargin:a,boxTextMargin:i,labelBoxHeight:n,labelBoxWidth:d,messageFontFamily:h,messageFontSize:r,messageFontWeight:g}=s,E=e.append("g"),f=p(function(w,k,O,S){return E.append("line").attr("x1",w).attr("y1",k).attr("x2",O).attr("y2",S).attr("class","loopLine")},"drawLoopLine");f(t.startx,t.starty,t.stopx,t.starty),f(t.stopx,t.starty,t.stopx,t.stopy),f(t.startx,t.stopy,t.stopx,t.stopy),f(t.startx,t.starty,t.startx,t.stopy),t.sections!==void 0&&t.sections.forEach(function(w){f(t.startx,w.y,t.stopx,w.y).style("stroke-dasharray","3, 3")});let T=Yt();T.text=c,T.x=t.startx,T.y=t.starty,T.fontFamily=h,T.fontSize=r,T.fontWeight=g,T.anchor="middle",T.valign="middle",T.tspan=!1,T.width=d||50,T.height=n||20,T.textMargin=i,T.class="labelText",ee(E,T),T=re(),T.text=t.title,T.x=t.startx+d/2+(t.stopx-t.startx)/2,T.y=t.starty+a+i,T.anchor="middle",T.valign="middle",T.textMargin=i,T.class="loopText",T.fontFamily=h,T.fontSize=r,T.fontWeight=g,T.wrap=!0;let m=ot(T.text)?await At(E,T,t):yt(E,T);if(t.sectionTitles!==void 0){for(const[w,k]of Object.entries(t.sectionTitles))if(k.message){T.text=k.message,T.x=t.startx+(t.stopx-t.startx)/2,T.y=t.sections[w].y+a+i,T.class="loopText",T.anchor="middle",T.valign="middle",T.tspan=!1,T.fontFamily=h,T.fontSize=r,T.fontWeight=g,T.wrap=t.wrap,ot(T.text)?(t.starty=t.sections[w].y,await At(E,T,t)):yt(E,T);let O=Math.round(m.map(S=>(S._groups||S)[0][0].getBBox().height).reduce((S,B)=>S+B));t.sections[w].height+=O-(a+i)}}return t.height=Math.round(t.stopy-t.starty),E},"drawLoop"),ae=p(function(e,t){xe(e,t)},"drawBackgroundRect"),Xe=p(function(e){e.append("defs").append("symbol").attr("id","database").attr("fill-rule","evenodd").attr("clip-rule","evenodd").append("path").attr("transform","scale(.5)").attr("d","M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z")},"insertDatabaseIcon"),Je=p(function(e){e.append("defs").append("symbol").attr("id","computer").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z")},"insertComputerIcon"),Ze=p(function(e){e.append("defs").append("symbol").attr("id","clock").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z")},"insertClockIcon"),Qe=p(function(e){e.append("defs").append("marker").attr("id","arrowhead").attr("refX",7.9).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto-start-reverse").append("path").attr("d","M -1 0 L 10 5 L 0 10 z")},"insertArrowHead"),je=p(function(e){e.append("defs").append("marker").attr("id","filled-head").attr("refX",15.5).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"insertArrowFilledHead"),$e=p(function(e){e.append("defs").append("marker").attr("id","sequencenumber").attr("refX",15).attr("refY",15).attr("markerWidth",60).attr("markerHeight",40).attr("orient","auto").append("circle").attr("cx",15).attr("cy",15).attr("r",6)},"insertSequenceNumber"),ts=p(function(e){e.append("defs").append("marker").attr("id","crosshead").attr("markerWidth",15).attr("markerHeight",8).attr("orient","auto").attr("refX",4).attr("refY",4.5).append("path").attr("fill","none").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1pt").attr("d","M 1,2 L 6,7 M 6,2 L 1,7")},"insertArrowCrossHead"),re=p(function(){return{x:0,y:0,fill:void 0,anchor:void 0,style:"#666",width:void 0,height:void 0,textMargin:0,rx:0,ry:0,tspan:!0,valign:void 0}},"getTextObj"),es=p(function(){return{x:0,y:0,fill:"#EDF2AE",stroke:"#666",width:100,anchor:"start",height:100,rx:0,ry:0}},"getNoteRect"),Ft=function(){function e(i,n,d,h,r,g,E){const f=n.append("text").attr("x",d+r/2).attr("y",h+g/2+5).style("text-anchor","middle").text(i);a(f,E)}p(e,"byText");function t(i,n,d,h,r,g,E,f){const{actorFontSize:T,actorFontFamily:m,actorFontWeight:w}=f,[k,O]=Qt(T),S=i.split(L.lineBreakRegex);for(let B=0;B<S.length;B++){const D=B*k-k*(S.length-1)/2,F=n.append("text").attr("x",d+r/2).attr("y",h).style("text-anchor","middle").style("font-size",O).style("font-weight",w).style("font-family",m);F.append("tspan").attr("x",d+r/2).attr("dy",D).text(S[B]),F.attr("y",h+g/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),a(F,E)}}p(t,"byTspan");function c(i,n,d,h,r,g,E,f){const T=n.append("switch"),w=T.append("foreignObject").attr("x",d).attr("y",h).attr("width",r).attr("height",g).append("xhtml:div").style("display","table").style("height","100%").style("width","100%");w.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(i),t(i,T,d,h,r,g,E,f),a(w,E)}p(c,"byFo");async function s(i,n,d,h,r,g,E,f){const T=await bt(i,Ct()),m=n.append("switch"),k=m.append("foreignObject").attr("x",d+r/2-T.width/2).attr("y",h+g/2-T.height/2).attr("width",T.width).attr("height",T.height).append("xhtml:div").style("height","100%").style("width","100%");k.append("div").style("text-align","center").style("vertical-align","middle").html(await jt(i,Ct())),t(i,m,d,h,r,g,E,f),a(k,E)}p(s,"byKatex");function a(i,n){for(const d in n)n.hasOwnProperty(d)&&i.attr(d,n[d])}return p(a,"_setTextAttrs"),function(i,n=!1){return n?s:i.textPlacement==="fo"?c:i.textPlacement==="old"?e:t}}(),ss=function(){function e(a,i,n,d,h,r,g){const E=i.append("text").attr("x",n).attr("y",d).style("text-anchor","start").text(a);s(E,g)}p(e,"byText");function t(a,i,n,d,h,r,g,E){const{actorFontSize:f,actorFontFamily:T,actorFontWeight:m}=E,w=a.split(L.lineBreakRegex);for(let k=0;k<w.length;k++){const O=k*f-f*(w.length-1)/2,S=i.append("text").attr("x",n).attr("y",d).style("text-anchor","start").style("font-size",f).style("font-weight",m).style("font-family",T);S.append("tspan").attr("x",n).attr("dy",O).text(w[k]),S.attr("y",d+r/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),s(S,g)}}p(t,"byTspan");function c(a,i,n,d,h,r,g,E){const f=i.append("switch"),m=f.append("foreignObject").attr("x",n).attr("y",d).attr("width",h).attr("height",r).append("xhtml:div").style("display","table").style("height","100%").style("width","100%");m.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(a),t(a,f,n,d,h,r,g,E),s(m,g)}p(c,"byFo");function s(a,i){for(const n in i)i.hasOwnProperty(n)&&a.attr(n,i[n])}return p(s,"_setTextAttrs"),function(a){return a.textPlacement==="fo"?c:a.textPlacement==="old"?e:t}}(),C={drawRect:Wt,drawText:yt,drawLabel:ee,drawActor:ze,drawBox:He,drawPopup:Ye,anchorElement:Ke,drawActivation:Ue,drawLoop:Ge,drawBackgroundRect:ae,insertArrowHead:Qe,insertArrowFilledHead:je,insertSequenceNumber:$e,insertArrowCrossHead:ts,insertDatabaseIcon:Xe,insertComputerIcon:Je,insertClockIcon:Ze,getTextObj:re,getNoteRect:es,fixLifeLineHeights:se,sanitizeUrl:Zt.sanitizeUrl},o={},x={data:{startx:void 0,stopx:void 0,starty:void 0,stopy:void 0},verticalPos:0,sequenceItems:[],activations:[],models:{getHeight:p(function(){return Math.max.apply(null,this.actors.length===0?[0]:this.actors.map(e=>e.height||0))+(this.loops.length===0?0:this.loops.map(e=>e.height||0).reduce((e,t)=>e+t))+(this.messages.length===0?0:this.messages.map(e=>e.height||0).reduce((e,t)=>e+t))+(this.notes.length===0?0:this.notes.map(e=>e.height||0).reduce((e,t)=>e+t))},"getHeight"),clear:p(function(){this.actors=[],this.boxes=[],this.loops=[],this.messages=[],this.notes=[]},"clear"),addBox:p(function(e){this.boxes.push(e)},"addBox"),addActor:p(function(e){this.actors.push(e)},"addActor"),addLoop:p(function(e){this.loops.push(e)},"addLoop"),addMessage:p(function(e){this.messages.push(e)},"addMessage"),addNote:p(function(e){this.notes.push(e)},"addNote"),lastActor:p(function(){return this.actors[this.actors.length-1]},"lastActor"),lastLoop:p(function(){return this.loops[this.loops.length-1]},"lastLoop"),lastMessage:p(function(){return this.messages[this.messages.length-1]},"lastMessage"),lastNote:p(function(){return this.notes[this.notes.length-1]},"lastNote"),actors:[],boxes:[],loops:[],messages:[],notes:[]},init:p(function(){this.sequenceItems=[],this.activations=[],this.models.clear(),this.data={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0},this.verticalPos=0,oe($())},"init"),updateVal:p(function(e,t,c,s){e[t]===void 0?e[t]=c:e[t]=s(c,e[t])},"updateVal"),updateBounds:p(function(e,t,c,s){const a=this;let i=0;function n(d){return p(function(r){i++;const g=a.sequenceItems.length-i+1;a.updateVal(r,"starty",t-g*o.boxMargin,Math.min),a.updateVal(r,"stopy",s+g*o.boxMargin,Math.max),a.updateVal(x.data,"startx",e-g*o.boxMargin,Math.min),a.updateVal(x.data,"stopx",c+g*o.boxMargin,Math.max),d!=="activation"&&(a.updateVal(r,"startx",e-g*o.boxMargin,Math.min),a.updateVal(r,"stopx",c+g*o.boxMargin,Math.max),a.updateVal(x.data,"starty",t-g*o.boxMargin,Math.min),a.updateVal(x.data,"stopy",s+g*o.boxMargin,Math.max))},"updateItemBounds")}p(n,"updateFn"),this.sequenceItems.forEach(n()),this.activations.forEach(n("activation"))},"updateBounds"),insert:p(function(e,t,c,s){const a=L.getMin(e,c),i=L.getMax(e,c),n=L.getMin(t,s),d=L.getMax(t,s);this.updateVal(x.data,"startx",a,Math.min),this.updateVal(x.data,"starty",n,Math.min),this.updateVal(x.data,"stopx",i,Math.max),this.updateVal(x.data,"stopy",d,Math.max),this.updateBounds(a,n,i,d)},"insert"),newActivation:p(function(e,t,c){const s=c.get(e.from),a=Nt(e.from).length||0,i=s.x+s.width/2+(a-1)*o.activationWidth/2;this.activations.push({startx:i,starty:this.verticalPos+2,stopx:i+o.activationWidth,stopy:void 0,actor:e.from,anchored:C.anchorElement(t)})},"newActivation"),endActivation:p(function(e){const t=this.activations.map(function(c){return c.actor}).lastIndexOf(e.from);return this.activations.splice(t,1)[0]},"endActivation"),createLoop:p(function(e={message:void 0,wrap:!1,width:void 0},t){return{startx:void 0,starty:this.verticalPos,stopx:void 0,stopy:void 0,title:e.message,wrap:e.wrap,width:e.width,height:0,fill:t}},"createLoop"),newLoop:p(function(e={message:void 0,wrap:!1,width:void 0},t){this.sequenceItems.push(this.createLoop(e,t))},"newLoop"),endLoop:p(function(){return this.sequenceItems.pop()},"endLoop"),isLoopOverlap:p(function(){return this.sequenceItems.length?this.sequenceItems[this.sequenceItems.length-1].overlap:!1},"isLoopOverlap"),addSectionToLoop:p(function(e){const t=this.sequenceItems.pop();t.sections=t.sections||[],t.sectionTitles=t.sectionTitles||[],t.sections.push({y:x.getVerticalPos(),height:0}),t.sectionTitles.push(e),this.sequenceItems.push(t)},"addSectionToLoop"),saveVerticalPos:p(function(){this.isLoopOverlap()&&(this.savedVerticalPos=this.verticalPos)},"saveVerticalPos"),resetVerticalPos:p(function(){this.isLoopOverlap()&&(this.verticalPos=this.savedVerticalPos)},"resetVerticalPos"),bumpVerticalPos:p(function(e){this.verticalPos=this.verticalPos+e,this.data.stopy=L.getMax(this.data.stopy,this.verticalPos)},"bumpVerticalPos"),getVerticalPos:p(function(){return this.verticalPos},"getVerticalPos"),getBounds:p(function(){return{bounds:this.data,models:this.models}},"getBounds")},as=p(async function(e,t){x.bumpVerticalPos(o.boxMargin),t.height=o.boxMargin,t.starty=x.getVerticalPos();const c=kt();c.x=t.startx,c.y=t.starty,c.width=t.width||o.width,c.class="note";const s=e.append("g"),a=C.drawRect(s,c),i=Yt();i.x=t.startx,i.y=t.starty,i.width=c.width,i.dy="1em",i.text=t.message,i.class="noteText",i.fontFamily=o.noteFontFamily,i.fontSize=o.noteFontSize,i.fontWeight=o.noteFontWeight,i.anchor=o.noteAlign,i.textMargin=o.noteMargin,i.valign="center";const n=ot(i.text)?await At(s,i):yt(s,i),d=Math.round(n.map(h=>(h._groups||h)[0][0].getBBox().height).reduce((h,r)=>h+r));a.attr("height",d+2*o.noteMargin),t.height+=d+2*o.noteMargin,x.bumpVerticalPos(d+2*o.noteMargin),t.stopy=t.starty+d+2*o.noteMargin,t.stopx=t.startx+c.width,x.insert(t.startx,t.starty,t.stopx,t.stopy),x.models.addNote(t)},"drawNote"),ft=p(e=>({fontFamily:e.messageFontFamily,fontSize:e.messageFontSize,fontWeight:e.messageFontWeight}),"messageFont"),xt=p(e=>({fontFamily:e.noteFontFamily,fontSize:e.noteFontSize,fontWeight:e.noteFontWeight}),"noteFont"),Bt=p(e=>({fontFamily:e.actorFontFamily,fontSize:e.actorFontSize,fontWeight:e.actorFontWeight}),"actorFont");async function ie(e,t){x.bumpVerticalPos(10);const{startx:c,stopx:s,message:a}=t,i=L.splitBreaks(a).length,n=ot(a),d=n?await bt(a,$()):Y.calculateTextDimensions(a,ft(o));if(!n){const E=d.height/i;t.height+=E,x.bumpVerticalPos(E)}let h,r=d.height-10;const g=d.width;if(c===s){h=x.getVerticalPos()+r,o.rightAngles||(r+=o.boxMargin,h=x.getVerticalPos()+r),r+=30;const E=L.getMax(g/2,o.width/2);x.insert(c-E,x.getVerticalPos()-10+r,s+E,x.getVerticalPos()+30+r)}else r+=o.boxMargin,h=x.getVerticalPos()+r,x.insert(c,h-10,s,h);return x.bumpVerticalPos(r),t.height+=r,t.stopy=t.starty+t.height,x.insert(t.fromBounds,t.starty,t.toBounds,t.stopy),h}p(ie,"boundMessage");var rs=p(async function(e,t,c,s){const{startx:a,stopx:i,starty:n,message:d,type:h,sequenceIndex:r,sequenceVisible:g}=t,E=Y.calculateTextDimensions(d,ft(o)),f=Yt();f.x=a,f.y=n+10,f.width=i-a,f.class="messageText",f.dy="1em",f.text=d,f.fontFamily=o.messageFontFamily,f.fontSize=o.messageFontSize,f.fontWeight=o.messageFontWeight,f.anchor=o.messageAlign,f.valign="center",f.textMargin=o.wrapPadding,f.tspan=!1,ot(f.text)?await At(e,f,{startx:a,stopx:i,starty:c}):yt(e,f);const T=E.width;let m;a===i?o.rightAngles?m=e.append("path").attr("d",`M  ${a},${c} H ${a+L.getMax(o.width/2,T/2)} V ${c+25} H ${a}`):m=e.append("path").attr("d","M "+a+","+c+" C "+(a+60)+","+(c-10)+" "+(a+60)+","+(c+30)+" "+a+","+(c+20)):(m=e.append("line"),m.attr("x1",a),m.attr("y1",c),m.attr("x2",i),m.attr("y2",c)),h===s.db.LINETYPE.DOTTED||h===s.db.LINETYPE.DOTTED_CROSS||h===s.db.LINETYPE.DOTTED_POINT||h===s.db.LINETYPE.DOTTED_OPEN||h===s.db.LINETYPE.BIDIRECTIONAL_DOTTED?(m.style("stroke-dasharray","3, 3"),m.attr("class","messageLine1")):m.attr("class","messageLine0");let w="";o.arrowMarkerAbsolute&&(w=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,w=w.replace(/\(/g,"\\("),w=w.replace(/\)/g,"\\)")),m.attr("stroke-width",2),m.attr("stroke","none"),m.style("fill","none"),(h===s.db.LINETYPE.SOLID||h===s.db.LINETYPE.DOTTED)&&m.attr("marker-end","url("+w+"#arrowhead)"),(h===s.db.LINETYPE.BIDIRECTIONAL_SOLID||h===s.db.LINETYPE.BIDIRECTIONAL_DOTTED)&&(m.attr("marker-start","url("+w+"#arrowhead)"),m.attr("marker-end","url("+w+"#arrowhead)")),(h===s.db.LINETYPE.SOLID_POINT||h===s.db.LINETYPE.DOTTED_POINT)&&m.attr("marker-end","url("+w+"#filled-head)"),(h===s.db.LINETYPE.SOLID_CROSS||h===s.db.LINETYPE.DOTTED_CROSS)&&m.attr("marker-end","url("+w+"#crosshead)"),(g||o.showSequenceNumbers)&&(m.attr("marker-start","url("+w+"#sequencenumber)"),e.append("text").attr("x",a).attr("y",c+4).attr("font-family","sans-serif").attr("font-size","12px").attr("text-anchor","middle").attr("class","sequenceNumber").text(r))},"drawMessage"),is=p(function(e,t,c,s,a,i,n){let d=0,h=0,r,g=0;for(const E of s){const f=t.get(E),T=f.box;r&&r!=T&&(n||x.models.addBox(r),h+=o.boxMargin+r.margin),T&&T!=r&&(n||(T.x=d+h,T.y=a),h+=T.margin),f.width=f.width||o.width,f.height=L.getMax(f.height||o.height,o.height),f.margin=f.margin||o.actorMargin,g=L.getMax(g,f.height),c.get(f.name)&&(h+=f.width/2),f.x=d+h,f.starty=x.getVerticalPos(),x.insert(f.x,a,f.x+f.width,f.height),d+=f.width+h,f.box&&(f.box.width=d+T.margin-f.box.x),h=f.margin,r=f.box,x.models.addActor(f)}r&&!n&&x.models.addBox(r),x.bumpVerticalPos(g)},"addActorRenderingData"),Vt=p(async function(e,t,c,s){if(s){let a=0;x.bumpVerticalPos(o.boxMargin*2);for(const i of c){const n=t.get(i);n.stopy||(n.stopy=x.getVerticalPos());const d=await C.drawActor(e,n,o,!0);a=L.getMax(a,d)}x.bumpVerticalPos(a+o.boxMargin)}else for(const a of c){const i=t.get(a);await C.drawActor(e,i,o,!1)}},"drawActors"),ne=p(function(e,t,c,s){let a=0,i=0;for(const n of c){const d=t.get(n),h=os(d),r=C.drawPopup(e,d,h,o,o.forceMenus,s);r.height>a&&(a=r.height),r.width+d.x>i&&(i=r.width+d.x)}return{maxHeight:a,maxWidth:i}},"drawActorsPopup"),oe=p(function(e){Ie(o,e),e.fontFamily&&(o.actorFontFamily=o.noteFontFamily=o.messageFontFamily=e.fontFamily),e.fontSize&&(o.actorFontSize=o.noteFontSize=o.messageFontSize=e.fontSize),e.fontWeight&&(o.actorFontWeight=o.noteFontWeight=o.messageFontWeight=e.fontWeight)},"setConf"),Nt=p(function(e){return x.activations.filter(function(t){return t.actor===e})},"actorActivations"),Jt=p(function(e,t){const c=t.get(e),s=Nt(e),a=s.reduce(function(n,d){return L.getMin(n,d.startx)},c.x+c.width/2-1),i=s.reduce(function(n,d){return L.getMax(n,d.stopx)},c.x+c.width/2+1);return[a,i]},"activationBounds");function rt(e,t,c,s,a){x.bumpVerticalPos(c);let i=s;if(t.id&&t.message&&e[t.id]){const n=e[t.id].width,d=ft(o);t.message=Y.wrapLabel(`[${t.message}]`,n-2*o.wrapPadding,d),t.width=n,t.wrap=!0;const h=Y.calculateTextDimensions(t.message,d),r=L.getMax(h.height,o.labelBoxHeight);i=s+r,G.debug(`${r} - ${t.message}`)}a(t),x.bumpVerticalPos(i)}p(rt,"adjustLoopHeightForWrap");function ce(e,t,c,s,a,i,n){function d(r,g){r.x<a.get(e.from).x?(x.insert(t.stopx-g,t.starty,t.startx,t.stopy+r.height/2+o.noteMargin),t.stopx=t.stopx+g):(x.insert(t.startx,t.starty,t.stopx+g,t.stopy+r.height/2+o.noteMargin),t.stopx=t.stopx-g)}p(d,"receiverAdjustment");function h(r,g){r.x<a.get(e.to).x?(x.insert(t.startx-g,t.starty,t.stopx,t.stopy+r.height/2+o.noteMargin),t.startx=t.startx+g):(x.insert(t.stopx,t.starty,t.startx+g,t.stopy+r.height/2+o.noteMargin),t.startx=t.startx-g)}if(p(h,"senderAdjustment"),i.get(e.to)==s){const r=a.get(e.to),g=r.type=="actor"?pt/2+3:r.width/2+3;d(r,g),r.starty=c-r.height/2,x.bumpVerticalPos(r.height/2)}else if(n.get(e.from)==s){const r=a.get(e.from);if(o.mirrorActors){const g=r.type=="actor"?pt/2:r.width/2;h(r,g)}r.stopy=c-r.height/2,x.bumpVerticalPos(r.height/2)}else if(n.get(e.to)==s){const r=a.get(e.to);if(o.mirrorActors){const g=r.type=="actor"?pt/2+3:r.width/2+3;d(r,g)}r.stopy=c-r.height/2,x.bumpVerticalPos(r.height/2)}}p(ce,"adjustCreatedDestroyedData");var ns=p(async function(e,t,c,s){const{securityLevel:a,sequence:i}=$();o=i;let n;a==="sandbox"&&(n=_t("#i"+t));const d=a==="sandbox"?_t(n.nodes()[0].contentDocument.body):_t("body"),h=a==="sandbox"?n.nodes()[0].contentDocument:document;x.init(),G.debug(s.db);const r=a==="sandbox"?d.select(`[id="${t}"]`):_t(`[id="${t}"]`),g=s.db.getActors(),E=s.db.getCreatedActors(),f=s.db.getDestroyedActors(),T=s.db.getBoxes();let m=s.db.getActorKeys();const w=s.db.getMessages(),k=s.db.getDiagramTitle(),O=s.db.hasAtLeastOneBox(),S=s.db.hasAtLeastOneBoxWithTitle(),B=await le(g,w,s);if(o.height=await de(g,B,T),C.insertComputerIcon(r),C.insertDatabaseIcon(r),C.insertClockIcon(r),O&&(x.bumpVerticalPos(o.boxMargin),S&&x.bumpVerticalPos(T[0].textMaxHeight)),o.hideUnusedParticipants===!0){const y=new Set;w.forEach(P=>{y.add(P.from),y.add(P.to)}),m=m.filter(P=>y.has(P))}is(r,g,E,m,0,w,!1);const D=await ds(w,g,B,s);C.insertArrowHead(r),C.insertArrowCrossHead(r),C.insertArrowFilledHead(r),C.insertSequenceNumber(r);function F(y,P){const Q=x.endActivation(y);Q.starty+18>P&&(Q.starty=P-6,P+=12),C.drawActivation(r,Q,P,o,Nt(y.from).length),x.insert(Q.startx,P-10,Q.stopx,P)}p(F,"activeEnd");let q=1,X=1;const tt=[],z=[];let H=0;for(const y of w){let P,Q,at;switch(y.type){case s.db.LINETYPE.NOTE:x.resetVerticalPos(),Q=y.noteModel,await as(r,Q);break;case s.db.LINETYPE.ACTIVE_START:x.newActivation(y,r,g);break;case s.db.LINETYPE.ACTIVE_END:F(y,x.getVerticalPos());break;case s.db.LINETYPE.LOOP_START:rt(D,y,o.boxMargin,o.boxMargin+o.boxTextMargin,N=>x.newLoop(N));break;case s.db.LINETYPE.LOOP_END:P=x.endLoop(),await C.drawLoop(r,P,"loop",o),x.bumpVerticalPos(P.stopy-x.getVerticalPos()),x.models.addLoop(P);break;case s.db.LINETYPE.RECT_START:rt(D,y,o.boxMargin,o.boxMargin,N=>x.newLoop(void 0,N.message));break;case s.db.LINETYPE.RECT_END:P=x.endLoop(),z.push(P),x.models.addLoop(P),x.bumpVerticalPos(P.stopy-x.getVerticalPos());break;case s.db.LINETYPE.OPT_START:rt(D,y,o.boxMargin,o.boxMargin+o.boxTextMargin,N=>x.newLoop(N));break;case s.db.LINETYPE.OPT_END:P=x.endLoop(),await C.drawLoop(r,P,"opt",o),x.bumpVerticalPos(P.stopy-x.getVerticalPos()),x.models.addLoop(P);break;case s.db.LINETYPE.ALT_START:rt(D,y,o.boxMargin,o.boxMargin+o.boxTextMargin,N=>x.newLoop(N));break;case s.db.LINETYPE.ALT_ELSE:rt(D,y,o.boxMargin+o.boxTextMargin,o.boxMargin,N=>x.addSectionToLoop(N));break;case s.db.LINETYPE.ALT_END:P=x.endLoop(),await C.drawLoop(r,P,"alt",o),x.bumpVerticalPos(P.stopy-x.getVerticalPos()),x.models.addLoop(P);break;case s.db.LINETYPE.PAR_START:case s.db.LINETYPE.PAR_OVER_START:rt(D,y,o.boxMargin,o.boxMargin+o.boxTextMargin,N=>x.newLoop(N)),x.saveVerticalPos();break;case s.db.LINETYPE.PAR_AND:rt(D,y,o.boxMargin+o.boxTextMargin,o.boxMargin,N=>x.addSectionToLoop(N));break;case s.db.LINETYPE.PAR_END:P=x.endLoop(),await C.drawLoop(r,P,"par",o),x.bumpVerticalPos(P.stopy-x.getVerticalPos()),x.models.addLoop(P);break;case s.db.LINETYPE.AUTONUMBER:q=y.message.start||q,X=y.message.step||X,y.message.visible?s.db.enableSequenceNumbers():s.db.disableSequenceNumbers();break;case s.db.LINETYPE.CRITICAL_START:rt(D,y,o.boxMargin,o.boxMargin+o.boxTextMargin,N=>x.newLoop(N));break;case s.db.LINETYPE.CRITICAL_OPTION:rt(D,y,o.boxMargin+o.boxTextMargin,o.boxMargin,N=>x.addSectionToLoop(N));break;case s.db.LINETYPE.CRITICAL_END:P=x.endLoop(),await C.drawLoop(r,P,"critical",o),x.bumpVerticalPos(P.stopy-x.getVerticalPos()),x.models.addLoop(P);break;case s.db.LINETYPE.BREAK_START:rt(D,y,o.boxMargin,o.boxMargin+o.boxTextMargin,N=>x.newLoop(N));break;case s.db.LINETYPE.BREAK_END:P=x.endLoop(),await C.drawLoop(r,P,"break",o),x.bumpVerticalPos(P.stopy-x.getVerticalPos()),x.models.addLoop(P);break;default:try{at=y.msgModel,at.starty=x.getVerticalPos(),at.sequenceIndex=q,at.sequenceVisible=s.db.showSequenceNumbers();const N=await ie(r,at);ce(y,at,N,H,g,E,f),tt.push({messageModel:at,lineStartY:N}),x.models.addMessage(at)}catch(N){G.error("error while drawing message",N)}}[s.db.LINETYPE.SOLID_OPEN,s.db.LINETYPE.DOTTED_OPEN,s.db.LINETYPE.SOLID,s.db.LINETYPE.DOTTED,s.db.LINETYPE.SOLID_CROSS,s.db.LINETYPE.DOTTED_CROSS,s.db.LINETYPE.SOLID_POINT,s.db.LINETYPE.DOTTED_POINT,s.db.LINETYPE.BIDIRECTIONAL_SOLID,s.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(y.type)&&(q=q+X),H++}G.debug("createdActors",E),G.debug("destroyedActors",f),await Vt(r,g,m,!1);for(const y of tt)await rs(r,y.messageModel,y.lineStartY,s);o.mirrorActors&&await Vt(r,g,m,!0),z.forEach(y=>C.drawBackgroundRect(r,y)),se(r,g,m,o);for(const y of x.models.boxes)y.height=x.getVerticalPos()-y.y,x.insert(y.x,y.y,y.x+y.width,y.height),y.startx=y.x,y.starty=y.y,y.stopx=y.startx+y.width,y.stopy=y.starty+y.height,y.stroke="rgb(0,0,0, 0.5)",C.drawBox(r,y,o);O&&x.bumpVerticalPos(o.boxMargin);const W=ne(r,g,m,h),{bounds:M}=x.getBounds();M.startx===void 0&&(M.startx=0),M.starty===void 0&&(M.starty=0),M.stopx===void 0&&(M.stopx=0),M.stopy===void 0&&(M.stopy=0);let J=M.stopy-M.starty;J<W.maxHeight&&(J=W.maxHeight);let K=J+2*o.diagramMarginY;o.mirrorActors&&(K=K-o.boxMargin+o.bottomMarginAdj);let Z=M.stopx-M.startx;Z<W.maxWidth&&(Z=W.maxWidth);const et=Z+2*o.diagramMarginX;k&&r.append("text").text(k).attr("x",(M.stopx-M.startx)/2-2*o.diagramMarginX).attr("y",-25),we(r,K,et,o.useMaxWidth);const v=k?40:0;r.attr("viewBox",M.startx-o.diagramMarginX+" -"+(o.diagramMarginY+v)+" "+et+" "+(K+v)),G.debug("models:",x.models)},"draw");async function le(e,t,c){const s={};for(const a of t)if(e.get(a.to)&&e.get(a.from)){const i=e.get(a.to);if(a.placement===c.db.PLACEMENT.LEFTOF&&!i.prevActor||a.placement===c.db.PLACEMENT.RIGHTOF&&!i.nextActor)continue;const n=a.placement!==void 0,d=!n,h=n?xt(o):ft(o),r=a.wrap?Y.wrapLabel(a.message,o.width-2*o.wrapPadding,h):a.message,E=(ot(r)?await bt(a.message,$()):Y.calculateTextDimensions(r,h)).width+2*o.wrapPadding;d&&a.from===i.nextActor?s[a.to]=L.getMax(s[a.to]||0,E):d&&a.from===i.prevActor?s[a.from]=L.getMax(s[a.from]||0,E):d&&a.from===a.to?(s[a.from]=L.getMax(s[a.from]||0,E/2),s[a.to]=L.getMax(s[a.to]||0,E/2)):a.placement===c.db.PLACEMENT.RIGHTOF?s[a.from]=L.getMax(s[a.from]||0,E):a.placement===c.db.PLACEMENT.LEFTOF?s[i.prevActor]=L.getMax(s[i.prevActor]||0,E):a.placement===c.db.PLACEMENT.OVER&&(i.prevActor&&(s[i.prevActor]=L.getMax(s[i.prevActor]||0,E/2)),i.nextActor&&(s[a.from]=L.getMax(s[a.from]||0,E/2)))}return G.debug("maxMessageWidthPerActor:",s),s}p(le,"getMaxMessageWidthPerActor");var os=p(function(e){let t=0;const c=Bt(o);for(const s in e.links){const i=Y.calculateTextDimensions(s,c).width+2*o.wrapPadding+2*o.boxMargin;t<i&&(t=i)}return t},"getRequiredPopupWidth");async function de(e,t,c){let s=0;for(const i of e.keys()){const n=e.get(i);n.wrap&&(n.description=Y.wrapLabel(n.description,o.width-2*o.wrapPadding,Bt(o)));const d=ot(n.description)?await bt(n.description,$()):Y.calculateTextDimensions(n.description,Bt(o));n.width=n.wrap?o.width:L.getMax(o.width,d.width+2*o.wrapPadding),n.height=n.wrap?L.getMax(d.height,o.height):o.height,s=L.getMax(s,n.height)}for(const i in t){const n=e.get(i);if(!n)continue;const d=e.get(n.nextActor);if(!d){const E=t[i]+o.actorMargin-n.width/2;n.margin=L.getMax(E,o.actorMargin);continue}const r=t[i]+o.actorMargin-n.width/2-d.width/2;n.margin=L.getMax(r,o.actorMargin)}let a=0;return c.forEach(i=>{const n=ft(o);let d=i.actorKeys.reduce((g,E)=>g+=e.get(E).width+(e.get(E).margin||0),0);d-=2*o.boxTextMargin,i.wrap&&(i.name=Y.wrapLabel(i.name,d-2*o.wrapPadding,n));const h=Y.calculateTextDimensions(i.name,n);a=L.getMax(h.height,a);const r=L.getMax(d,h.width+2*o.wrapPadding);if(i.margin=o.boxTextMargin,d<r){const g=(r-d)/2;i.margin+=g}}),c.forEach(i=>i.textMaxHeight=a),L.getMax(s,o.height)}p(de,"calculateActorMargins");var cs=p(async function(e,t,c){const s=t.get(e.from),a=t.get(e.to),i=s.x,n=a.x,d=e.wrap&&e.message;let h=ot(e.message)?await bt(e.message,$()):Y.calculateTextDimensions(d?Y.wrapLabel(e.message,o.width,xt(o)):e.message,xt(o));const r={width:d?o.width:L.getMax(o.width,h.width+2*o.noteMargin),height:0,startx:s.x,stopx:0,starty:0,stopy:0,message:e.message};return e.placement===c.db.PLACEMENT.RIGHTOF?(r.width=d?L.getMax(o.width,h.width):L.getMax(s.width/2+a.width/2,h.width+2*o.noteMargin),r.startx=i+(s.width+o.actorMargin)/2):e.placement===c.db.PLACEMENT.LEFTOF?(r.width=d?L.getMax(o.width,h.width+2*o.noteMargin):L.getMax(s.width/2+a.width/2,h.width+2*o.noteMargin),r.startx=i-r.width+(s.width-o.actorMargin)/2):e.to===e.from?(h=Y.calculateTextDimensions(d?Y.wrapLabel(e.message,L.getMax(o.width,s.width),xt(o)):e.message,xt(o)),r.width=d?L.getMax(o.width,s.width):L.getMax(s.width,o.width,h.width+2*o.noteMargin),r.startx=i+(s.width-r.width)/2):(r.width=Math.abs(i+s.width/2-(n+a.width/2))+o.actorMargin,r.startx=i<n?i+s.width/2-o.actorMargin/2:n+a.width/2-o.actorMargin/2),d&&(r.message=Y.wrapLabel(e.message,r.width-2*o.wrapPadding,xt(o))),G.debug(`NM:[${r.startx},${r.stopx},${r.starty},${r.stopy}:${r.width},${r.height}=${e.message}]`),r},"buildNoteModel"),ls=p(function(e,t,c){if(![c.db.LINETYPE.SOLID_OPEN,c.db.LINETYPE.DOTTED_OPEN,c.db.LINETYPE.SOLID,c.db.LINETYPE.DOTTED,c.db.LINETYPE.SOLID_CROSS,c.db.LINETYPE.DOTTED_CROSS,c.db.LINETYPE.SOLID_POINT,c.db.LINETYPE.DOTTED_POINT,c.db.LINETYPE.BIDIRECTIONAL_SOLID,c.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(e.type))return{};const[s,a]=Jt(e.from,t),[i,n]=Jt(e.to,t),d=s<=i;let h=d?a:s,r=d?i:n;const g=Math.abs(i-n)>2,E=p(w=>d?-w:w,"adjustValue");e.from===e.to?r=h:(e.activate&&!g&&(r+=E(o.activationWidth/2-1)),[c.db.LINETYPE.SOLID_OPEN,c.db.LINETYPE.DOTTED_OPEN].includes(e.type)||(r+=E(3)),[c.db.LINETYPE.BIDIRECTIONAL_SOLID,c.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(e.type)&&(h-=E(3)));const f=[s,a,i,n],T=Math.abs(h-r);e.wrap&&e.message&&(e.message=Y.wrapLabel(e.message,L.getMax(T+2*o.wrapPadding,o.width),ft(o)));const m=Y.calculateTextDimensions(e.message,ft(o));return{width:L.getMax(e.wrap?0:m.width+2*o.wrapPadding,T+2*o.wrapPadding,o.width),height:0,startx:h,stopx:r,starty:0,stopy:0,message:e.message,type:e.type,wrap:e.wrap,fromBounds:Math.min.apply(null,f),toBounds:Math.max.apply(null,f)}},"buildMessageModel"),ds=p(async function(e,t,c,s){const a={},i=[];let n,d,h;for(const r of e){switch(r.type){case s.db.LINETYPE.LOOP_START:case s.db.LINETYPE.ALT_START:case s.db.LINETYPE.OPT_START:case s.db.LINETYPE.PAR_START:case s.db.LINETYPE.PAR_OVER_START:case s.db.LINETYPE.CRITICAL_START:case s.db.LINETYPE.BREAK_START:i.push({id:r.id,msg:r.message,from:Number.MAX_SAFE_INTEGER,to:Number.MIN_SAFE_INTEGER,width:0});break;case s.db.LINETYPE.ALT_ELSE:case s.db.LINETYPE.PAR_AND:case s.db.LINETYPE.CRITICAL_OPTION:r.message&&(n=i.pop(),a[n.id]=n,a[r.id]=n,i.push(n));break;case s.db.LINETYPE.LOOP_END:case s.db.LINETYPE.ALT_END:case s.db.LINETYPE.OPT_END:case s.db.LINETYPE.PAR_END:case s.db.LINETYPE.CRITICAL_END:case s.db.LINETYPE.BREAK_END:n=i.pop(),a[n.id]=n;break;case s.db.LINETYPE.ACTIVE_START:{const E=t.get(r.from?r.from:r.to.actor),f=Nt(r.from?r.from:r.to.actor).length,T=E.x+E.width/2+(f-1)*o.activationWidth/2,m={startx:T,stopx:T+o.activationWidth,actor:r.from,enabled:!0};x.activations.push(m)}break;case s.db.LINETYPE.ACTIVE_END:{const E=x.activations.map(f=>f.actor).lastIndexOf(r.from);x.activations.splice(E,1).splice(0,1)}break}r.placement!==void 0?(d=await cs(r,t,s),r.noteModel=d,i.forEach(E=>{n=E,n.from=L.getMin(n.from,d.startx),n.to=L.getMax(n.to,d.startx+d.width),n.width=L.getMax(n.width,Math.abs(n.from-n.to))-o.labelBoxWidth})):(h=ls(r,t,s),r.msgModel=h,h.startx&&h.stopx&&i.length>0&&i.forEach(E=>{if(n=E,h.startx===h.stopx){const f=t.get(r.from),T=t.get(r.to);n.from=L.getMin(f.x-h.width/2,f.x-f.width/2,n.from),n.to=L.getMax(T.x+h.width/2,T.x+f.width/2,n.to),n.width=L.getMax(n.width,Math.abs(n.to-n.from))-o.labelBoxWidth}else n.from=L.getMin(h.startx,n.from),n.to=L.getMax(h.stopx,n.to),n.width=L.getMax(n.width,h.width)-o.labelBoxWidth}))}return x.activations=[],G.debug("Loop type widths:",a),a},"calculateLoopBounds"),hs={bounds:x,drawActors:Vt,drawActorsPopup:ne,setConf:oe,draw:ns},bs={parser:Se,get db(){return new Ce},renderer:hs,styles:Be,init:p(e=>{e.sequence||(e.sequence={}),e.wrap&&(e.sequence.wrap=e.wrap,me({sequence:{wrap:e.wrap}}))},"init")};export{bs as diagram};
