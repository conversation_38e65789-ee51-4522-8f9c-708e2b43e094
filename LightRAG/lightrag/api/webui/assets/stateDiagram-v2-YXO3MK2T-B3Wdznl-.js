import{s as r,b as e,a,S as s}from"./chunk-AEK57VVT-BPVBpkMU.js";import{_ as i}from"./mermaid-vendor-S2u3NfNd.js";import"./chunk-RZ5BOZE2-DAdu8FE8.js";import"./feature-graph-DGPXw7qg.js";import"./react-vendor-DEwriMA6.js";import"./graph-vendor-B-X5JegA.js";import"./ui-vendor-CeCm8EER.js";import"./utils-vendor-BysuhMZA.js";var b={parser:a,get db(){return new s(2)},renderer:e,styles:r,init:i(t=>{t.state||(t.state={}),t.state.arrowMarkerAbsolute=t.arrowMarkerAbsolute},"init")};export{b as diagram};
