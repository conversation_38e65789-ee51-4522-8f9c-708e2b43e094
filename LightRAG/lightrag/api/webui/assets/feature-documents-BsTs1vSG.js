import{j as i,_ as ee,d as Qa}from"./ui-vendor-CeCm8EER.js";import{r as c,g as Sa,R as Za}from"./react-vendor-DEwriMA6.js";import{c as O,C as Le,F as et,a as Be,b as Fa,u as le,s as at,d as q,U as Ue,S as tt,e as Oa,B as I,X as it,f as nt,g as X,D as Ve,h as Aa,i as Xe,j as Qe,k as Ze,l as ea,m as ot,n as lt,E as rt,T as ct,I as pt,o as st,p as dt,A as mt,q as ut,r as ft,t as xt,v as Se,w as Fe,x as vt,y as gt,z as da,G as ma,R as ht,H as bt,J as yt,K as Oe,L as Ae}from"./feature-graph-DGPXw7qg.js";const Ta=c.forwardRef(({className:e,...a},n)=>i.jsx("div",{className:"relative w-full overflow-auto",children:i.jsx("table",{ref:n,className:O("w-full caption-bottom text-sm",e),...a})}));Ta.displayName="Table";const _a=c.forwardRef(({className:e,...a},n)=>i.jsx("thead",{ref:n,className:O("[&_tr]:border-b",e),...a}));_a.displayName="TableHeader";const Ra=c.forwardRef(({className:e,...a},n)=>i.jsx("tbody",{ref:n,className:O("[&_tr:last-child]:border-0",e),...a}));Ra.displayName="TableBody";const wt=c.forwardRef(({className:e,...a},n)=>i.jsx("tfoot",{ref:n,className:O("bg-muted/50 border-t font-medium [&>tr]:last:border-b-0",e),...a}));wt.displayName="TableFooter";const $e=c.forwardRef(({className:e,...a},n)=>i.jsx("tr",{ref:n,className:O("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...a}));$e.displayName="TableRow";const J=c.forwardRef(({className:e,...a},n)=>i.jsx("th",{ref:n,className:O("text-muted-foreground h-10 px-2 text-left align-middle font-medium [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...a}));J.displayName="TableHead";const V=c.forwardRef(({className:e,...a},n)=>i.jsx("td",{ref:n,className:O("p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...a}));V.displayName="TableCell";const jt=c.forwardRef(({className:e,...a},n)=>i.jsx("caption",{ref:n,className:O("text-muted-foreground mt-4 text-sm",e),...a}));jt.displayName="TableCaption";function kt({title:e,description:a,icon:n=et,action:t,className:o,...l}){return i.jsxs(Le,{className:O("flex w-full flex-col items-center justify-center space-y-6 bg-transparent p-16",o),...l,children:[i.jsx("div",{className:"mr-4 shrink-0 rounded-full border border-dashed p-4",children:i.jsx(n,{className:"text-muted-foreground size-8","aria-hidden":"true"})}),i.jsxs("div",{className:"flex flex-col items-center gap-1.5 text-center",children:[i.jsx(Be,{children:e}),a?i.jsx(Fa,{children:a}):null]}),t||null]})}var Te={exports:{}},_e,ua;function Dt(){if(ua)return _e;ua=1;var e="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return _e=e,_e}var Re,fa;function zt(){if(fa)return Re;fa=1;var e=Dt();function a(){}function n(){}return n.resetWarningCache=a,Re=function(){function t(d,r,h,b,v,A){if(A!==e){var f=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw f.name="Invariant Violation",f}}t.isRequired=t;function o(){return t}var l={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:o,element:t,elementType:t,instanceOf:o,node:t,objectOf:o,oneOf:o,oneOfType:o,shape:o,exact:o,checkPropTypes:n,resetWarningCache:a};return l.PropTypes=l,l},Re}var xa;function Pt(){return xa||(xa=1,Te.exports=zt()()),Te.exports}var Nt=Pt();const z=Sa(Nt),Et=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function oe(e,a,n){const t=Ct(e),{webkitRelativePath:o}=e,l=typeof a=="string"?a:typeof o=="string"&&o.length>0?o:`./${e.name}`;return typeof t.path!="string"&&va(t,"path",l),va(t,"relativePath",l),t}function Ct(e){const{name:a}=e;if(a&&a.lastIndexOf(".")!==-1&&!e.type){const t=a.split(".").pop().toLowerCase(),o=Et.get(t);o&&Object.defineProperty(e,"type",{value:o,writable:!1,configurable:!1,enumerable:!0})}return e}function va(e,a,n){Object.defineProperty(e,a,{value:n,writable:!1,configurable:!1,enumerable:!0})}const St=[".DS_Store","Thumbs.db"];function Ft(e){return ee(this,void 0,void 0,function*(){return he(e)&&Ot(e.dataTransfer)?Rt(e.dataTransfer,e.type):At(e)?Tt(e):Array.isArray(e)&&e.every(a=>"getFile"in a&&typeof a.getFile=="function")?_t(e):[]})}function Ot(e){return he(e)}function At(e){return he(e)&&he(e.target)}function he(e){return typeof e=="object"&&e!==null}function Tt(e){return He(e.target.files).map(a=>oe(a))}function _t(e){return ee(this,void 0,void 0,function*(){return(yield Promise.all(e.map(n=>n.getFile()))).map(n=>oe(n))})}function Rt(e,a){return ee(this,void 0,void 0,function*(){if(e.items){const n=He(e.items).filter(o=>o.kind==="file");if(a!=="drop")return n;const t=yield Promise.all(n.map(Mt));return ga(Ma(t))}return ga(He(e.files).map(n=>oe(n)))})}function ga(e){return e.filter(a=>St.indexOf(a.name)===-1)}function He(e){if(e===null)return[];const a=[];for(let n=0;n<e.length;n++){const t=e[n];a.push(t)}return a}function Mt(e){if(typeof e.webkitGetAsEntry!="function")return ha(e);const a=e.webkitGetAsEntry();return a&&a.isDirectory?qa(a):ha(e,a)}function Ma(e){return e.reduce((a,n)=>[...a,...Array.isArray(n)?Ma(n):[n]],[])}function ha(e,a){return ee(this,void 0,void 0,function*(){var n;if(globalThis.isSecureContext&&typeof e.getAsFileSystemHandle=="function"){const l=yield e.getAsFileSystemHandle();if(l===null)throw new Error(`${e} is not a File`);if(l!==void 0){const d=yield l.getFile();return d.handle=l,oe(d)}}const t=e.getAsFile();if(!t)throw new Error(`${e} is not a File`);return oe(t,(n=a==null?void 0:a.fullPath)!==null&&n!==void 0?n:void 0)})}function qt(e){return ee(this,void 0,void 0,function*(){return e.isDirectory?qa(e):It(e)})}function qa(e){const a=e.createReader();return new Promise((n,t)=>{const o=[];function l(){a.readEntries(d=>ee(this,void 0,void 0,function*(){if(d.length){const r=Promise.all(d.map(qt));o.push(r),l()}else try{const r=yield Promise.all(o);n(r)}catch(r){t(r)}}),d=>{t(d)})}l()})}function It(e){return ee(this,void 0,void 0,function*(){return new Promise((a,n)=>{e.file(t=>{const o=oe(t,e.fullPath);a(o)},t=>{n(t)})})})}var ve={},ba;function Lt(){return ba||(ba=1,ve.__esModule=!0,ve.default=function(e,a){if(e&&a){var n=Array.isArray(a)?a:a.split(",");if(n.length===0)return!0;var t=e.name||"",o=(e.type||"").toLowerCase(),l=o.replace(/\/.*$/,"");return n.some(function(d){var r=d.trim().toLowerCase();return r.charAt(0)==="."?t.toLowerCase().endsWith(r):r.endsWith("/*")?l===r.replace(/\/.*$/,""):o===r})}return!0}),ve}var Bt=Lt();const Me=Sa(Bt);function ya(e){return Ht(e)||$t(e)||La(e)||Ut()}function Ut(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function $t(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Ht(e){if(Array.isArray(e))return Ke(e)}function wa(e,a){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);a&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,t)}return n}function ja(e){for(var a=1;a<arguments.length;a++){var n=arguments[a]!=null?arguments[a]:{};a%2?wa(Object(n),!0).forEach(function(t){Ia(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):wa(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Ia(e,a,n){return a in e?Object.defineProperty(e,a,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[a]=n,e}function ce(e,a){return Gt(e)||Wt(e,a)||La(e,a)||Kt()}function Kt(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function La(e,a){if(e){if(typeof e=="string")return Ke(e,a);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ke(e,a)}}function Ke(e,a){(a==null||a>e.length)&&(a=e.length);for(var n=0,t=new Array(a);n<a;n++)t[n]=e[n];return t}function Wt(e,a){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var t=[],o=!0,l=!1,d,r;try{for(n=n.call(e);!(o=(d=n.next()).done)&&(t.push(d.value),!(a&&t.length===a));o=!0);}catch(h){l=!0,r=h}finally{try{!o&&n.return!=null&&n.return()}finally{if(l)throw r}}return t}}function Gt(e){if(Array.isArray(e))return e}var Yt=typeof Me=="function"?Me:Me.default,Jt="file-invalid-type",Vt="file-too-large",Xt="file-too-small",Qt="too-many-files",Zt=function(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",n=a.split(","),t=n.length>1?"one of ".concat(n.join(", ")):n[0];return{code:Jt,message:"File type must be ".concat(t)}},ka=function(a){return{code:Vt,message:"File is larger than ".concat(a," ").concat(a===1?"byte":"bytes")}},Da=function(a){return{code:Xt,message:"File is smaller than ".concat(a," ").concat(a===1?"byte":"bytes")}},ei={code:Qt,message:"Too many files"};function Ba(e,a){var n=e.type==="application/x-moz-file"||Yt(e,a);return[n,n?null:Zt(a)]}function Ua(e,a,n){if(Z(e.size))if(Z(a)&&Z(n)){if(e.size>n)return[!1,ka(n)];if(e.size<a)return[!1,Da(a)]}else{if(Z(a)&&e.size<a)return[!1,Da(a)];if(Z(n)&&e.size>n)return[!1,ka(n)]}return[!0,null]}function Z(e){return e!=null}function ai(e){var a=e.files,n=e.accept,t=e.minSize,o=e.maxSize,l=e.multiple,d=e.maxFiles,r=e.validator;return!l&&a.length>1||l&&d>=1&&a.length>d?!1:a.every(function(h){var b=Ba(h,n),v=ce(b,1),A=v[0],f=Ua(h,t,o),C=ce(f,1),P=C[0],M=r?r(h):null;return A&&P&&!M})}function be(e){return typeof e.isPropagationStopped=="function"?e.isPropagationStopped():typeof e.cancelBubble<"u"?e.cancelBubble:!1}function ge(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,function(a){return a==="Files"||a==="application/x-moz-file"}):!!e.target&&!!e.target.files}function za(e){e.preventDefault()}function ti(e){return e.indexOf("MSIE")!==-1||e.indexOf("Trident/")!==-1}function ii(e){return e.indexOf("Edge/")!==-1}function ni(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:window.navigator.userAgent;return ti(e)||ii(e)}function K(){for(var e=arguments.length,a=new Array(e),n=0;n<e;n++)a[n]=arguments[n];return function(t){for(var o=arguments.length,l=new Array(o>1?o-1:0),d=1;d<o;d++)l[d-1]=arguments[d];return a.some(function(r){return!be(t)&&r&&r.apply(void 0,[t].concat(l)),be(t)})}}function oi(){return"showOpenFilePicker"in window}function li(e){if(Z(e)){var a=Object.entries(e).filter(function(n){var t=ce(n,2),o=t[0],l=t[1],d=!0;return $a(o)||(console.warn('Skipped "'.concat(o,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),d=!1),(!Array.isArray(l)||!l.every(Ha))&&(console.warn('Skipped "'.concat(o,'" because an invalid file extension was provided.')),d=!1),d}).reduce(function(n,t){var o=ce(t,2),l=o[0],d=o[1];return ja(ja({},n),{},Ia({},l,d))},{});return[{description:"Files",accept:a}]}return e}function ri(e){if(Z(e))return Object.entries(e).reduce(function(a,n){var t=ce(n,2),o=t[0],l=t[1];return[].concat(ya(a),[o],ya(l))},[]).filter(function(a){return $a(a)||Ha(a)}).join(",")}function ci(e){return e instanceof DOMException&&(e.name==="AbortError"||e.code===e.ABORT_ERR)}function pi(e){return e instanceof DOMException&&(e.name==="SecurityError"||e.code===e.SECURITY_ERR)}function $a(e){return e==="audio/*"||e==="video/*"||e==="image/*"||e==="text/*"||e==="application/*"||/\w+\/[-+.\w]+/g.test(e)}function Ha(e){return/^.*\.[\w]+$/.test(e)}var si=["children"],di=["open"],mi=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],ui=["refKey","onChange","onClick"];function fi(e){return gi(e)||vi(e)||Ka(e)||xi()}function xi(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function vi(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function gi(e){if(Array.isArray(e))return We(e)}function qe(e,a){return yi(e)||bi(e,a)||Ka(e,a)||hi()}function hi(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ka(e,a){if(e){if(typeof e=="string")return We(e,a);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return We(e,a)}}function We(e,a){(a==null||a>e.length)&&(a=e.length);for(var n=0,t=new Array(a);n<a;n++)t[n]=e[n];return t}function bi(e,a){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var t=[],o=!0,l=!1,d,r;try{for(n=n.call(e);!(o=(d=n.next()).done)&&(t.push(d.value),!(a&&t.length===a));o=!0);}catch(h){l=!0,r=h}finally{try{!o&&n.return!=null&&n.return()}finally{if(l)throw r}}return t}}function yi(e){if(Array.isArray(e))return e}function Pa(e,a){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);a&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,t)}return n}function S(e){for(var a=1;a<arguments.length;a++){var n=arguments[a]!=null?arguments[a]:{};a%2?Pa(Object(n),!0).forEach(function(t){Ge(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Pa(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Ge(e,a,n){return a in e?Object.defineProperty(e,a,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[a]=n,e}function ye(e,a){if(e==null)return{};var n=wi(e,a),t,o;if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(o=0;o<l.length;o++)t=l[o],!(a.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(n[t]=e[t])}return n}function wi(e,a){if(e==null)return{};var n={},t=Object.keys(e),o,l;for(l=0;l<t.length;l++)o=t[l],!(a.indexOf(o)>=0)&&(n[o]=e[o]);return n}var we=c.forwardRef(function(e,a){var n=e.children,t=ye(e,si),o=ji(t),l=o.open,d=ye(o,di);return c.useImperativeHandle(a,function(){return{open:l}},[l]),Za.createElement(c.Fragment,null,n(S(S({},d),{},{open:l})))});we.displayName="Dropzone";var Wa={disabled:!1,getFilesFromEvent:Ft,maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};we.defaultProps=Wa;we.propTypes={children:z.func,accept:z.objectOf(z.arrayOf(z.string)),multiple:z.bool,preventDropOnDocument:z.bool,noClick:z.bool,noKeyboard:z.bool,noDrag:z.bool,noDragEventsBubbling:z.bool,minSize:z.number,maxSize:z.number,maxFiles:z.number,disabled:z.bool,getFilesFromEvent:z.func,onFileDialogCancel:z.func,onFileDialogOpen:z.func,useFsAccessApi:z.bool,autoFocus:z.bool,onDragEnter:z.func,onDragLeave:z.func,onDragOver:z.func,onDrop:z.func,onDropAccepted:z.func,onDropRejected:z.func,onError:z.func,validator:z.func};var Ye={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function ji(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},a=S(S({},Wa),e),n=a.accept,t=a.disabled,o=a.getFilesFromEvent,l=a.maxSize,d=a.minSize,r=a.multiple,h=a.maxFiles,b=a.onDragEnter,v=a.onDragLeave,A=a.onDragOver,f=a.onDrop,C=a.onDropAccepted,P=a.onDropRejected,M=a.onFileDialogCancel,x=a.onFileDialogOpen,N=a.useFsAccessApi,B=a.autoFocus,H=a.preventDropOnDocument,W=a.noClick,m=a.noKeyboard,g=a.noDrag,k=a.noDragEventsBubbling,E=a.onError,U=a.validator,p=c.useMemo(function(){return ri(n)},[n]),F=c.useMemo(function(){return li(n)},[n]),y=c.useMemo(function(){return typeof x=="function"?x:Na},[x]),D=c.useMemo(function(){return typeof M=="function"?M:Na},[M]),w=c.useRef(null),j=c.useRef(null),L=c.useReducer(ki,Ye),G=qe(L,2),Y=G[0],_=G[1],pe=Y.isFocused,aa=Y.isFileDialogActive,se=c.useRef(typeof window<"u"&&window.isSecureContext&&N&&oi()),ta=function(){!se.current&&aa&&setTimeout(function(){if(j.current){var u=j.current.files;u.length||(_({type:"closeDialog"}),D())}},300)};c.useEffect(function(){return window.addEventListener("focus",ta,!1),function(){window.removeEventListener("focus",ta,!1)}},[j,aa,D,se]);var ae=c.useRef([]),ia=function(u){w.current&&w.current.contains(u.target)||(u.preventDefault(),ae.current=[])};c.useEffect(function(){return H&&(document.addEventListener("dragover",za,!1),document.addEventListener("drop",ia,!1)),function(){H&&(document.removeEventListener("dragover",za),document.removeEventListener("drop",ia))}},[w,H]),c.useEffect(function(){return!t&&B&&w.current&&w.current.focus(),function(){}},[w,B,t]);var Q=c.useCallback(function(s){E?E(s):console.error(s)},[E]),na=c.useCallback(function(s){s.preventDefault(),s.persist(),fe(s),ae.current=[].concat(fi(ae.current),[s.target]),ge(s)&&Promise.resolve(o(s)).then(function(u){if(!(be(s)&&!k)){var T=u.length,R=T>0&&ai({files:u,accept:p,minSize:d,maxSize:l,multiple:r,maxFiles:h,validator:U}),$=T>0&&!R;_({isDragAccept:R,isDragReject:$,isDragActive:!0,type:"setDraggedFiles"}),b&&b(s)}}).catch(function(u){return Q(u)})},[o,b,Q,k,p,d,l,r,h,U]),oa=c.useCallback(function(s){s.preventDefault(),s.persist(),fe(s);var u=ge(s);if(u&&s.dataTransfer)try{s.dataTransfer.dropEffect="copy"}catch{}return u&&A&&A(s),!1},[A,k]),la=c.useCallback(function(s){s.preventDefault(),s.persist(),fe(s);var u=ae.current.filter(function(R){return w.current&&w.current.contains(R)}),T=u.indexOf(s.target);T!==-1&&u.splice(T,1),ae.current=u,!(u.length>0)&&(_({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),ge(s)&&v&&v(s))},[w,v,k]),de=c.useCallback(function(s,u){var T=[],R=[];s.forEach(function($){var re=Ba($,p),ne=qe(re,2),ke=ne[0],De=ne[1],ze=Ua($,d,l),xe=qe(ze,2),Pe=xe[0],Ne=xe[1],Ee=U?U($):null;if(ke&&Pe&&!Ee)T.push($);else{var Ce=[De,Ne];Ee&&(Ce=Ce.concat(Ee)),R.push({file:$,errors:Ce.filter(function(Xa){return Xa})})}}),(!r&&T.length>1||r&&h>=1&&T.length>h)&&(T.forEach(function($){R.push({file:$,errors:[ei]})}),T.splice(0)),_({acceptedFiles:T,fileRejections:R,isDragReject:R.length>0,type:"setFiles"}),f&&f(T,R,u),R.length>0&&P&&P(R,u),T.length>0&&C&&C(T,u)},[_,r,p,d,l,h,f,C,P,U]),me=c.useCallback(function(s){s.preventDefault(),s.persist(),fe(s),ae.current=[],ge(s)&&Promise.resolve(o(s)).then(function(u){be(s)&&!k||de(u,s)}).catch(function(u){return Q(u)}),_({type:"reset"})},[o,de,Q,k]),te=c.useCallback(function(){if(se.current){_({type:"openDialog"}),y();var s={multiple:r,types:F};window.showOpenFilePicker(s).then(function(u){return o(u)}).then(function(u){de(u,null),_({type:"closeDialog"})}).catch(function(u){ci(u)?(D(u),_({type:"closeDialog"})):pi(u)?(se.current=!1,j.current?(j.current.value=null,j.current.click()):Q(new Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):Q(u)});return}j.current&&(_({type:"openDialog"}),y(),j.current.value=null,j.current.click())},[_,y,D,N,de,Q,F,r]),ra=c.useCallback(function(s){!w.current||!w.current.isEqualNode(s.target)||(s.key===" "||s.key==="Enter"||s.keyCode===32||s.keyCode===13)&&(s.preventDefault(),te())},[w,te]),ca=c.useCallback(function(){_({type:"focus"})},[]),pa=c.useCallback(function(){_({type:"blur"})},[]),sa=c.useCallback(function(){W||(ni()?setTimeout(te,0):te())},[W,te]),ie=function(u){return t?null:u},je=function(u){return m?null:ie(u)},ue=function(u){return g?null:ie(u)},fe=function(u){k&&u.stopPropagation()},Ya=c.useMemo(function(){return function(){var s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},u=s.refKey,T=u===void 0?"ref":u,R=s.role,$=s.onKeyDown,re=s.onFocus,ne=s.onBlur,ke=s.onClick,De=s.onDragEnter,ze=s.onDragOver,xe=s.onDragLeave,Pe=s.onDrop,Ne=ye(s,mi);return S(S(Ge({onKeyDown:je(K($,ra)),onFocus:je(K(re,ca)),onBlur:je(K(ne,pa)),onClick:ie(K(ke,sa)),onDragEnter:ue(K(De,na)),onDragOver:ue(K(ze,oa)),onDragLeave:ue(K(xe,la)),onDrop:ue(K(Pe,me)),role:typeof R=="string"&&R!==""?R:"presentation"},T,w),!t&&!m?{tabIndex:0}:{}),Ne)}},[w,ra,ca,pa,sa,na,oa,la,me,m,g,t]),Ja=c.useCallback(function(s){s.stopPropagation()},[]),Va=c.useMemo(function(){return function(){var s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},u=s.refKey,T=u===void 0?"ref":u,R=s.onChange,$=s.onClick,re=ye(s,ui),ne=Ge({accept:p,multiple:r,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:ie(K(R,me)),onClick:ie(K($,Ja)),tabIndex:-1},T,j);return S(S({},ne),re)}},[j,n,r,me,t]);return S(S({},Y),{},{isFocused:pe&&!t,getRootProps:Ya,getInputProps:Va,rootRef:w,inputRef:j,open:ie(te)})}function ki(e,a){switch(a.type){case"focus":return S(S({},e),{},{isFocused:!0});case"blur":return S(S({},e),{},{isFocused:!1});case"openDialog":return S(S({},Ye),{},{isFileDialogActive:!0});case"closeDialog":return S(S({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":return S(S({},e),{},{isDragActive:a.isDragActive,isDragAccept:a.isDragAccept,isDragReject:a.isDragReject});case"setFiles":return S(S({},e),{},{acceptedFiles:a.acceptedFiles,fileRejections:a.fileRejections,isDragReject:a.isDragReject});case"reset":return S({},Ye);default:return e}}function Na(){}function Je(e,a={}){const{decimals:n=0,sizeType:t="normal"}=a,o=["Bytes","KB","MB","GB","TB"],l=["Bytes","KiB","MiB","GiB","TiB"];if(e===0)return"0 Byte";const d=Math.floor(Math.log(e)/Math.log(1024));return`${(e/Math.pow(1024,d)).toFixed(n)} ${t==="accurate"?l[d]??"Bytes":o[d]??"Bytes"}`}function Di(e){const{t:a}=le(),{value:n,onValueChange:t,onUpload:o,onReject:l,progresses:d,fileErrors:r,accept:h=at,maxSize:b=1024*1024*200,maxFileCount:v=1,multiple:A=!1,disabled:f=!1,description:C,className:P,...M}=e,[x,N]=Qa({prop:n,onChange:t}),B=c.useCallback((m,g)=>{const k=((x==null?void 0:x.length)??0)+m.length+g.length;if(!A&&v===1&&m.length+g.length>1){q.error(a("documentPanel.uploadDocuments.fileUploader.singleFileLimit"));return}if(k>v){q.error(a("documentPanel.uploadDocuments.fileUploader.maxFilesLimit",{count:v}));return}g.length>0&&(l?l(g):g.forEach(({file:y})=>{q.error(a("documentPanel.uploadDocuments.fileUploader.fileRejected",{name:y.name}))}));const E=m.map(y=>Object.assign(y,{preview:URL.createObjectURL(y)})),U=g.map(({file:y})=>Object.assign(y,{preview:URL.createObjectURL(y),rejected:!0})),p=[...E,...U],F=x?[...x,...p]:p;if(N(F),o&&m.length>0){const y=m.filter(D=>{var G;if(!D.name)return!1;const w=`.${((G=D.name.split(".").pop())==null?void 0:G.toLowerCase())||""}`,j=Object.entries(h||{}).some(([Y,_])=>D.type===Y||Array.isArray(_)&&_.includes(w)),L=D.size<=b;return j&&L});y.length>0&&o(y)}},[x,v,A,o,l,N,a,h,b]);function H(m){if(!x)return;const g=x.filter((k,E)=>E!==m);N(g),t==null||t(g)}c.useEffect(()=>()=>{x&&x.forEach(m=>{Ga(m)&&URL.revokeObjectURL(m.preview)})},[]);const W=f||((x==null?void 0:x.length)??0)>=v;return i.jsxs("div",{className:"relative flex flex-col gap-6 overflow-hidden",children:[i.jsx(we,{onDrop:B,noClick:!1,noKeyboard:!1,maxSize:b,maxFiles:v,multiple:v>1||A,disabled:W,validator:m=>{var E;if(!m.name)return{code:"invalid-file-name",message:a("documentPanel.uploadDocuments.fileUploader.invalidFileName",{fallback:"Invalid file name"})};const g=`.${((E=m.name.split(".").pop())==null?void 0:E.toLowerCase())||""}`;return Object.entries(h||{}).some(([U,p])=>m.type===U||Array.isArray(p)&&p.includes(g))?m.size>b?{code:"file-too-large",message:a("documentPanel.uploadDocuments.fileUploader.fileTooLarge",{maxSize:Je(b)})}:null:{code:"file-invalid-type",message:a("documentPanel.uploadDocuments.fileUploader.unsupportedType")}},children:({getRootProps:m,getInputProps:g,isDragActive:k})=>i.jsxs("div",{...m(),className:O("group border-muted-foreground/25 hover:bg-muted/25 relative grid h-52 w-full cursor-pointer place-items-center rounded-lg border-2 border-dashed px-5 py-2.5 text-center transition","ring-offset-background focus-visible:ring-ring focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none",k&&"border-muted-foreground/50",W&&"pointer-events-none opacity-60",P),...M,children:[i.jsx("input",{...g()}),k?i.jsxs("div",{className:"flex flex-col items-center justify-center gap-4 sm:px-5",children:[i.jsx("div",{className:"rounded-full border border-dashed p-3",children:i.jsx(Ue,{className:"text-muted-foreground size-7","aria-hidden":"true"})}),i.jsx("p",{className:"text-muted-foreground font-medium",children:a("documentPanel.uploadDocuments.fileUploader.dropHere")})]}):i.jsxs("div",{className:"flex flex-col items-center justify-center gap-4 sm:px-5",children:[i.jsx("div",{className:"rounded-full border border-dashed p-3",children:i.jsx(Ue,{className:"text-muted-foreground size-7","aria-hidden":"true"})}),i.jsxs("div",{className:"flex flex-col gap-px",children:[i.jsx("p",{className:"text-muted-foreground font-medium",children:a("documentPanel.uploadDocuments.fileUploader.dragAndDrop")}),C?i.jsx("p",{className:"text-muted-foreground/70 text-sm",children:C}):i.jsxs("p",{className:"text-muted-foreground/70 text-sm",children:[a("documentPanel.uploadDocuments.fileUploader.uploadDescription",{count:v,isMultiple:v===1/0,maxSize:Je(b)}),a("documentPanel.uploadDocuments.fileTypes")]})]})]})]})}),x!=null&&x.length?i.jsx(tt,{className:"h-fit w-full px-3",children:i.jsx("div",{className:"flex max-h-48 flex-col gap-4",children:x==null?void 0:x.map((m,g)=>i.jsx(zi,{file:m,onRemove:()=>H(g),progress:d==null?void 0:d[m.name],error:r==null?void 0:r[m.name]},g))})}):null]})}function Ea({value:e,error:a}){return i.jsx("div",{className:"relative h-2 w-full",children:i.jsx("div",{className:"h-full w-full overflow-hidden rounded-full bg-secondary",children:i.jsx("div",{className:O("h-full transition-all",a?"bg-red-400":"bg-primary"),style:{width:`${e}%`}})})})}function zi({file:e,progress:a,error:n,onRemove:t}){const{t:o}=le();return i.jsxs("div",{className:"relative flex items-center gap-2.5",children:[i.jsxs("div",{className:"flex flex-1 gap-2.5",children:[n?i.jsx(Oa,{className:"text-red-400 size-10","aria-hidden":"true"}):Ga(e)?i.jsx(Pi,{file:e}):null,i.jsxs("div",{className:"flex w-full flex-col gap-2",children:[i.jsxs("div",{className:"flex flex-col gap-px",children:[i.jsx("p",{className:"text-foreground/80 line-clamp-1 text-sm font-medium",children:e.name}),i.jsx("p",{className:"text-muted-foreground text-xs",children:Je(e.size)})]}),n?i.jsxs("div",{className:"text-red-400 text-sm",children:[i.jsx("div",{className:"relative mb-2",children:i.jsx(Ea,{value:100,error:!0})}),i.jsx("p",{children:n})]}):a?i.jsx(Ea,{value:a}):null]})]}),i.jsx("div",{className:"flex items-center gap-2",children:i.jsxs(I,{type:"button",variant:"outline",size:"icon",className:"size-7",onClick:t,children:[i.jsx(it,{className:"size-4","aria-hidden":"true"}),i.jsx("span",{className:"sr-only",children:o("documentPanel.uploadDocuments.fileUploader.removeFile")})]})})]})}function Ga(e){return"preview"in e&&typeof e.preview=="string"}function Pi({file:e}){return e.type.startsWith("image/")?i.jsx("div",{className:"aspect-square shrink-0 rounded-md object-cover"}):i.jsx(Oa,{className:"text-muted-foreground size-10","aria-hidden":"true"})}function Ni({onDocumentsUploaded:e}){const{t:a}=le(),[n,t]=c.useState(!1),[o,l]=c.useState(!1),[d,r]=c.useState({}),[h,b]=c.useState({}),v=c.useCallback(f=>{f.forEach(({file:C,errors:P})=>{var x;let M=((x=P[0])==null?void 0:x.message)||a("documentPanel.uploadDocuments.fileUploader.fileRejected",{name:C.name});M.includes("file-invalid-type")&&(M=a("documentPanel.uploadDocuments.fileUploader.unsupportedType")),r(N=>({...N,[C.name]:100})),b(N=>({...N,[C.name]:M}))})},[r,b,a]),A=c.useCallback(async f=>{var M,x;l(!0);let C=!1;b(N=>{const B={...N};return f.forEach(H=>{delete B[H.name]}),B});const P=q.loading(a("documentPanel.uploadDocuments.batch.uploading"));try{const N={},B=new Intl.Collator(["zh-CN","en"],{sensitivity:"accent",numeric:!0}),H=[...f].sort((m,g)=>B.compare(m.name,g.name));for(const m of H)try{r(k=>({...k,[m.name]:0}));const g=await nt(m,k=>{console.debug(a("documentPanel.uploadDocuments.single.uploading",{name:m.name,percent:k})),r(E=>({...E,[m.name]:k}))});g.status==="duplicated"?(N[m.name]=a("documentPanel.uploadDocuments.fileUploader.duplicateFile"),b(k=>({...k,[m.name]:a("documentPanel.uploadDocuments.fileUploader.duplicateFile")}))):g.status!=="success"?(N[m.name]=g.message,b(k=>({...k,[m.name]:g.message}))):C=!0}catch(g){console.error(`Upload failed for ${m.name}:`,g);let k=X(g);if(g&&typeof g=="object"&&"response"in g){const E=g;((M=E.response)==null?void 0:M.status)===400&&(k=((x=E.response.data)==null?void 0:x.detail)||k),r(U=>({...U,[m.name]:100}))}N[m.name]=k,b(E=>({...E,[m.name]:k}))}Object.keys(N).length>0?q.error(a("documentPanel.uploadDocuments.batch.error"),{id:P}):q.success(a("documentPanel.uploadDocuments.batch.success"),{id:P}),C&&e&&e().catch(m=>{console.error("Error refreshing documents:",m)})}catch(N){console.error("Unexpected error during upload:",N),q.error(a("documentPanel.uploadDocuments.generalError",{error:X(N)}),{id:P})}finally{l(!1)}},[l,r,b,a,e]);return i.jsxs(Ve,{open:n,onOpenChange:f=>{o||(f||(r({}),b({})),t(f))},children:[i.jsx(Aa,{asChild:!0,children:i.jsxs(I,{variant:"default",side:"bottom",tooltip:a("documentPanel.uploadDocuments.tooltip"),size:"sm",children:[i.jsx(Ue,{})," ",a("documentPanel.uploadDocuments.button")]})}),i.jsxs(Xe,{className:"sm:max-w-xl",onCloseAutoFocus:f=>f.preventDefault(),children:[i.jsxs(Qe,{children:[i.jsx(Ze,{children:a("documentPanel.uploadDocuments.title")}),i.jsx(ea,{children:a("documentPanel.uploadDocuments.description")})]}),i.jsx(Di,{maxFileCount:1/0,maxSize:200*1024*1024,description:a("documentPanel.uploadDocuments.fileTypes"),onUpload:A,onReject:v,progresses:d,fileErrors:h,disabled:o})]})]})}const Ca=({htmlFor:e,className:a,children:n,...t})=>i.jsx("label",{htmlFor:e,className:a,...t,children:n});function Ei({onDocumentsCleared:e}){const{t:a}=le(),[n,t]=c.useState(!1),[o,l]=c.useState(""),[d,r]=c.useState(!1),h=o.toLowerCase()==="yes";c.useEffect(()=>{n||(l(""),r(!1))},[n]);const b=c.useCallback(async()=>{if(h)try{const v=await ot();if(v.status!=="success"){q.error(a("documentPanel.clearDocuments.failed",{message:v.message})),l("");return}if(q.success(a("documentPanel.clearDocuments.success")),d)try{await lt(),q.success(a("documentPanel.clearDocuments.cacheCleared"))}catch(A){q.error(a("documentPanel.clearDocuments.cacheClearFailed",{error:X(A)}))}e&&e().catch(console.error),t(!1)}catch(v){q.error(a("documentPanel.clearDocuments.error",{error:X(v)})),l("")}},[h,d,t,a,e]);return i.jsxs(Ve,{open:n,onOpenChange:t,children:[i.jsx(Aa,{asChild:!0,children:i.jsxs(I,{variant:"outline",side:"bottom",tooltip:a("documentPanel.clearDocuments.tooltip"),size:"sm",children:[i.jsx(rt,{})," ",a("documentPanel.clearDocuments.button")]})}),i.jsxs(Xe,{className:"sm:max-w-xl",onCloseAutoFocus:v=>v.preventDefault(),children:[i.jsxs(Qe,{children:[i.jsxs(Ze,{className:"flex items-center gap-2 text-red-500 dark:text-red-400 font-bold",children:[i.jsx(ct,{className:"h-5 w-5"}),a("documentPanel.clearDocuments.title")]}),i.jsx(ea,{className:"pt-2",children:a("documentPanel.clearDocuments.description")})]}),i.jsx("div",{className:"text-red-500 dark:text-red-400 font-semibold mb-4",children:a("documentPanel.clearDocuments.warning")}),i.jsx("div",{className:"mb-4",children:a("documentPanel.clearDocuments.confirm")}),i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{className:"space-y-2",children:[i.jsx(Ca,{htmlFor:"confirm-text",className:"text-sm font-medium",children:a("documentPanel.clearDocuments.confirmPrompt")}),i.jsx(pt,{id:"confirm-text",value:o,onChange:v=>l(v.target.value),placeholder:a("documentPanel.clearDocuments.confirmPlaceholder"),className:"w-full"})]}),i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx(st,{id:"clear-cache",checked:d,onCheckedChange:v=>r(v===!0)}),i.jsx(Ca,{htmlFor:"clear-cache",className:"text-sm font-medium cursor-pointer",children:a("documentPanel.clearDocuments.clearCache")})]})]}),i.jsxs(dt,{children:[i.jsx(I,{variant:"outline",onClick:()=>t(!1),children:a("common.cancel")}),i.jsx(I,{variant:"destructive",onClick:b,disabled:!h,children:a("documentPanel.clearDocuments.confirmButton")})]})]})]})}function Ci({open:e,onOpenChange:a}){var A;const{t:n}=le(),[t,o]=c.useState(null),[l,d]=c.useState("center"),[r,h]=c.useState(!1),b=c.useRef(null);c.useEffect(()=>{e&&(d("center"),h(!1))},[e]),c.useEffect(()=>{const f=b.current;!f||r||(f.scrollTop=f.scrollHeight)},[t==null?void 0:t.history_messages,r]);const v=()=>{const f=b.current;if(!f)return;const C=Math.abs(f.scrollHeight-f.scrollTop-f.clientHeight)<1;h(!C)};return c.useEffect(()=>{if(!e)return;const f=async()=>{try{const P=await xt();o(P)}catch(P){q.error(n("documentPanel.pipelineStatus.errors.fetchFailed",{error:X(P)}))}};f();const C=setInterval(f,2e3);return()=>clearInterval(C)},[e,n]),i.jsx(Ve,{open:e,onOpenChange:a,children:i.jsxs(Xe,{className:O("sm:max-w-[800px] transition-all duration-200 fixed",l==="left"&&"!left-[25%] !translate-x-[-50%] !mx-4",l==="center"&&"!left-1/2 !-translate-x-1/2",l==="right"&&"!left-[75%] !translate-x-[-50%] !mx-4"),children:[i.jsx(ea,{className:"sr-only",children:t!=null&&t.job_name?`${n("documentPanel.pipelineStatus.jobName")}: ${t.job_name}, ${n("documentPanel.pipelineStatus.progress")}: ${t.cur_batch}/${t.batchs}`:n("documentPanel.pipelineStatus.noActiveJob")}),i.jsxs(Qe,{className:"flex flex-row items-center",children:[i.jsx(Ze,{className:"flex-1",children:n("documentPanel.pipelineStatus.title")}),i.jsxs("div",{className:"flex items-center gap-2 mr-8",children:[i.jsx(I,{variant:"ghost",size:"icon",className:O("h-6 w-6",l==="left"&&"bg-zinc-200 text-zinc-800 hover:bg-zinc-300 dark:bg-zinc-700 dark:text-zinc-200 dark:hover:bg-zinc-600"),onClick:()=>d("left"),children:i.jsx(mt,{className:"h-4 w-4"})}),i.jsx(I,{variant:"ghost",size:"icon",className:O("h-6 w-6",l==="center"&&"bg-zinc-200 text-zinc-800 hover:bg-zinc-300 dark:bg-zinc-700 dark:text-zinc-200 dark:hover:bg-zinc-600"),onClick:()=>d("center"),children:i.jsx(ut,{className:"h-4 w-4"})}),i.jsx(I,{variant:"ghost",size:"icon",className:O("h-6 w-6",l==="right"&&"bg-zinc-200 text-zinc-800 hover:bg-zinc-300 dark:bg-zinc-700 dark:text-zinc-200 dark:hover:bg-zinc-600"),onClick:()=>d("right"),children:i.jsx(ft,{className:"h-4 w-4"})})]})]}),i.jsxs("div",{className:"space-y-4 pt-4",children:[i.jsxs("div",{className:"flex items-center gap-4",children:[i.jsxs("div",{className:"flex items-center gap-2",children:[i.jsxs("div",{className:"text-sm font-medium",children:[n("documentPanel.pipelineStatus.busy"),":"]}),i.jsx("div",{className:`h-2 w-2 rounded-full ${t!=null&&t.busy?"bg-green-500":"bg-gray-300"}`})]}),i.jsxs("div",{className:"flex items-center gap-2",children:[i.jsxs("div",{className:"text-sm font-medium",children:[n("documentPanel.pipelineStatus.requestPending"),":"]}),i.jsx("div",{className:`h-2 w-2 rounded-full ${t!=null&&t.request_pending?"bg-green-500":"bg-gray-300"}`})]})]}),i.jsxs("div",{className:"rounded-md border p-3 space-y-2",children:[i.jsxs("div",{children:[n("documentPanel.pipelineStatus.jobName"),": ",(t==null?void 0:t.job_name)||"-"]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsxs("span",{children:[n("documentPanel.pipelineStatus.startTime"),": ",t!=null&&t.job_start?new Date(t.job_start).toLocaleString(void 0,{year:"numeric",month:"numeric",day:"numeric",hour:"numeric",minute:"numeric",second:"numeric"}):"-"]}),i.jsxs("span",{children:[n("documentPanel.pipelineStatus.progress"),": ",t?`${t.cur_batch}/${t.batchs} ${n("documentPanel.pipelineStatus.unit")}`:"-"]})]})]}),i.jsxs("div",{className:"space-y-2",children:[i.jsxs("div",{className:"text-sm font-medium",children:[n("documentPanel.pipelineStatus.latestMessage"),":"]}),i.jsx("div",{className:"font-mono text-xs rounded-md bg-zinc-800 text-zinc-100 p-3 whitespace-pre-wrap break-words",children:(t==null?void 0:t.latest_message)||"-"})]}),i.jsxs("div",{className:"space-y-2",children:[i.jsxs("div",{className:"text-sm font-medium",children:[n("documentPanel.pipelineStatus.historyMessages"),":"]}),i.jsx("div",{ref:b,onScroll:v,className:"font-mono text-xs rounded-md bg-zinc-800 text-zinc-100 p-3 overflow-y-auto min-h-[7.5em] max-h-[40vh]",children:(A=t==null?void 0:t.history_messages)!=null&&A.length?t.history_messages.map((f,C)=>i.jsx("div",{className:"whitespace-pre-wrap break-words",children:f},C)):"-"})]})]})]})})}const Ie=(e,a=20)=>{if(!e.file_path||typeof e.file_path!="string"||e.file_path.trim()==="")return e.id;const n=e.file_path.split("/"),t=n[n.length-1];return!t||t.trim()===""?e.id:t.length>a?t.slice(0,a)+"...":t},Si=`
/* Tooltip styles */
.tooltip-container {
  position: relative;
  overflow: visible !important;
}

.tooltip {
  position: fixed; /* Use fixed positioning to escape overflow constraints */
  z-index: 9999; /* Ensure tooltip appears above all other elements */
  max-width: 600px;
  white-space: normal;
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  background-color: rgba(0, 0, 0, 0.95);
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  pointer-events: none; /* Prevent tooltip from interfering with mouse events */
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.15s, visibility 0.15s;
}

.tooltip.visible {
  opacity: 1;
  visibility: visible;
}

.dark .tooltip {
  background-color: rgba(255, 255, 255, 0.95);
  color: black;
}

/* Position tooltip helper class */
.tooltip-helper {
  position: absolute;
  visibility: hidden;
  pointer-events: none;
  top: 0;
  left: 0;
  width: 100%;
  height: 0;
}

@keyframes pulse {
  0% {
    background-color: rgb(255 0 0 / 0.1);
    border-color: rgb(255 0 0 / 0.2);
  }
  50% {
    background-color: rgb(255 0 0 / 0.2);
    border-color: rgb(255 0 0 / 0.4);
  }
  100% {
    background-color: rgb(255 0 0 / 0.1);
    border-color: rgb(255 0 0 / 0.2);
  }
}

.dark .pipeline-busy {
  animation: dark-pulse 2s infinite;
}

@keyframes dark-pulse {
  0% {
    background-color: rgb(255 0 0 / 0.2);
    border-color: rgb(255 0 0 / 0.4);
  }
  50% {
    background-color: rgb(255 0 0 / 0.3);
    border-color: rgb(255 0 0 / 0.6);
  }
  100% {
    background-color: rgb(255 0 0 / 0.2);
    border-color: rgb(255 0 0 / 0.4);
  }
}

.pipeline-busy {
  animation: pulse 2s infinite;
  border: 1px solid;
}
`;function Ti(){const e=c.useRef(!0);c.useEffect(()=>{e.current=!0;const p=()=>{e.current=!1};return window.addEventListener("beforeunload",p),()=>{e.current=!1,window.removeEventListener("beforeunload",p)}},[]);const[a,n]=c.useState(!1),{t,i18n:o}=le(),l=Se.use.health(),d=Se.use.pipelineBusy(),[r,h]=c.useState(null),b=Fe.use.currentTab(),v=Fe.use.showFileName(),A=Fe.use.setShowFileName(),[f,C]=c.useState("updated_at"),[P,M]=c.useState("desc"),[x,N]=c.useState("all"),B=p=>{f===p?M(F=>F==="asc"?"desc":"asc"):(C(p),M("desc"))},H=c.useCallback(p=>[...p].sort((F,y)=>{let D,w;f==="id"&&v?(D=Ie(F),w=Ie(y)):f==="id"?(D=F.id,w=y.id):(D=new Date(F[f]).getTime(),w=new Date(y[f]).getTime());const j=P==="asc"?1:-1;return typeof D=="string"&&typeof w=="string"?j*D.localeCompare(w):j*(D>w?1:D<w?-1:0)}),[f,P,v]),W=c.useMemo(()=>{if(!r)return null;const p=[];return x==="all"?Object.entries(r.statuses).forEach(([F,y])=>{y.forEach(D=>{p.push({...D,status:F})})}):(r.statuses[x]||[]).forEach(y=>{p.push({...y,status:x})}),f&&P?H(p):p},[r,f,P,x,H]),m=c.useMemo(()=>{if(!r)return{all:0};const p={all:0};return Object.entries(r.statuses).forEach(([F,y])=>{p[F]=y.length,p.all+=y.length}),p},[r]),g=c.useRef({processed:0,processing:0,pending:0,failed:0});c.useEffect(()=>{const p=document.createElement("style");return p.textContent=Si,document.head.appendChild(p),()=>{document.head.removeChild(p)}},[]);const k=c.useRef(null);c.useEffect(()=>{if(!r)return;const p=()=>{document.querySelectorAll(".tooltip-container").forEach(w=>{const j=w.querySelector(".tooltip");if(!j||!j.classList.contains("visible"))return;const L=w.getBoundingClientRect();j.style.left=`${L.left}px`,j.style.top=`${L.top-5}px`,j.style.transform="translateY(-100%)"})},F=D=>{const j=D.target.closest(".tooltip-container");if(!j)return;const L=j.querySelector(".tooltip");L&&(L.classList.add("visible"),p())},y=D=>{const j=D.target.closest(".tooltip-container");if(!j)return;const L=j.querySelector(".tooltip");L&&L.classList.remove("visible")};return document.addEventListener("mouseover",F),document.addEventListener("mouseout",y),()=>{document.removeEventListener("mouseover",F),document.removeEventListener("mouseout",y)}},[r]);const E=c.useCallback(async()=>{try{if(!e.current)return;const p=await vt();if(!e.current)return;e.current&&(p&&p.statuses&&Object.values(p.statuses).reduce((y,D)=>y+D.length,0)>0?h(p):h(null))}catch(p){e.current&&q.error(t("documentPanel.documentManager.errors.loadFailed",{error:X(p)}))}},[h,t]);c.useEffect(()=>{b==="documents"&&E()},[b,E]);const U=c.useCallback(async()=>{try{if(!e.current)return;const{status:p}=await gt();if(!e.current)return;q.message(p)}catch(p){e.current&&q.error(t("documentPanel.documentManager.errors.scanFailed",{error:X(p)}))}},[t]);return c.useEffect(()=>{if(b!=="documents"||!l)return;const p=setInterval(async()=>{try{e.current&&await E()}catch(F){e.current&&q.error(t("documentPanel.documentManager.errors.scanProgressFailed",{error:X(F)}))}},5e3);return()=>{clearInterval(p)}},[l,E,t,b]),c.useEffect(()=>{var y,D,w,j,L,G,Y,_;if(!r)return;const p={processed:((D=(y=r==null?void 0:r.statuses)==null?void 0:y.processed)==null?void 0:D.length)||0,processing:((j=(w=r==null?void 0:r.statuses)==null?void 0:w.processing)==null?void 0:j.length)||0,pending:((G=(L=r==null?void 0:r.statuses)==null?void 0:L.pending)==null?void 0:G.length)||0,failed:((_=(Y=r==null?void 0:r.statuses)==null?void 0:Y.failed)==null?void 0:_.length)||0};Object.keys(p).some(pe=>p[pe]!==g.current[pe])&&e.current&&Se.getState().check(),g.current=p},[r]),c.useEffect(()=>{},[f,P]),i.jsxs(Le,{className:"!rounded-none !overflow-hidden flex flex-col h-full min-h-0",children:[i.jsx(da,{className:"py-2 px-6",children:i.jsx(Be,{className:"text-lg",children:t("documentPanel.documentManager.title")})}),i.jsxs(ma,{className:"flex-1 flex flex-col min-h-0 overflow-auto",children:[i.jsxs("div",{className:"flex gap-2 mb-2",children:[i.jsxs("div",{className:"flex gap-2",children:[i.jsxs(I,{variant:"outline",onClick:U,side:"bottom",tooltip:t("documentPanel.documentManager.scanTooltip"),size:"sm",children:[i.jsx(ht,{})," ",t("documentPanel.documentManager.scanButton")]}),i.jsxs(I,{variant:"outline",onClick:()=>n(!0),side:"bottom",tooltip:t("documentPanel.documentManager.pipelineStatusTooltip"),size:"sm",className:O(d&&"pipeline-busy"),children:[i.jsx(bt,{})," ",t("documentPanel.documentManager.pipelineStatusButton")]})]}),i.jsx("div",{className:"flex-1"}),i.jsx(Ei,{onDocumentsCleared:E}),i.jsx(Ni,{onDocumentsUploaded:E}),i.jsx(Ci,{open:a,onOpenChange:n})]}),i.jsxs(Le,{className:"flex-1 flex flex-col border rounded-md min-h-0 mb-2",children:[i.jsxs(da,{className:"flex-none py-2 px-4",children:[i.jsxs("div",{className:"flex justify-between items-center",children:[i.jsx(Be,{children:t("documentPanel.documentManager.uploadedTitle")}),i.jsxs("div",{className:"flex items-center gap-2",children:[i.jsx(yt,{className:"h-4 w-4"}),i.jsxs("div",{className:"flex gap-1",dir:o.dir(),children:[i.jsxs(I,{size:"sm",variant:x==="all"?"secondary":"outline",onClick:()=>N("all"),className:O(x==="all"&&"bg-gray-100 dark:bg-gray-900 font-medium border border-gray-400 dark:border-gray-500 shadow-sm"),children:[t("documentPanel.documentManager.status.all")," (",m.all,")"]}),i.jsxs(I,{size:"sm",variant:x==="processed"?"secondary":"outline",onClick:()=>N("processed"),className:O(m.processed>0?"text-green-600":"text-gray-500",x==="processed"&&"bg-green-100 dark:bg-green-900/30 font-medium border border-green-400 dark:border-green-600 shadow-sm"),children:[t("documentPanel.documentManager.status.completed")," (",m.processed||0,")"]}),i.jsxs(I,{size:"sm",variant:x==="processing"?"secondary":"outline",onClick:()=>N("processing"),className:O(m.processing>0?"text-blue-600":"text-gray-500",x==="processing"&&"bg-blue-100 dark:bg-blue-900/30 font-medium border border-blue-400 dark:border-blue-600 shadow-sm"),children:[t("documentPanel.documentManager.status.processing")," (",m.processing||0,")"]}),i.jsxs(I,{size:"sm",variant:x==="pending"?"secondary":"outline",onClick:()=>N("pending"),className:O(m.pending>0?"text-yellow-600":"text-gray-500",x==="pending"&&"bg-yellow-100 dark:bg-yellow-900/30 font-medium border border-yellow-400 dark:border-yellow-600 shadow-sm"),children:[t("documentPanel.documentManager.status.pending")," (",m.pending||0,")"]}),i.jsxs(I,{size:"sm",variant:x==="failed"?"secondary":"outline",onClick:()=>N("failed"),className:O(m.failed>0?"text-red-600":"text-gray-500",x==="failed"&&"bg-red-100 dark:bg-red-900/30 font-medium border border-red-400 dark:border-red-600 shadow-sm"),children:[t("documentPanel.documentManager.status.failed")," (",m.failed||0,")"]})]})]}),i.jsxs("div",{className:"flex items-center gap-2",children:[i.jsx("label",{htmlFor:"toggle-filename-btn",className:"text-sm text-gray-500",children:t("documentPanel.documentManager.fileNameLabel")}),i.jsx(I,{id:"toggle-filename-btn",variant:"outline",size:"sm",onClick:()=>A(!v),className:"border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800",children:t(v?"documentPanel.documentManager.hideButton":"documentPanel.documentManager.showButton")})]})]}),i.jsx(Fa,{"aria-hidden":"true",className:"hidden",children:t("documentPanel.documentManager.uploadedDescription")})]}),i.jsxs(ma,{className:"flex-1 relative p-0",ref:k,children:[!r&&i.jsx("div",{className:"absolute inset-0 p-0",children:i.jsx(kt,{title:t("documentPanel.documentManager.emptyTitle"),description:t("documentPanel.documentManager.emptyDescription")})}),r&&i.jsx("div",{className:"absolute inset-0 flex flex-col p-0",children:i.jsx("div",{className:"absolute inset-[-1px] flex flex-col p-0 border rounded-md border-gray-200 dark:border-gray-700 overflow-hidden",children:i.jsxs(Ta,{className:"w-full",children:[i.jsx(_a,{className:"sticky top-0 bg-background z-10 shadow-sm",children:i.jsxs($e,{className:"border-b bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/75 shadow-[inset_0_-1px_0_rgba(0,0,0,0.1)]",children:[i.jsx(J,{onClick:()=>B("id"),className:"cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-800 select-none",children:i.jsxs("div",{className:"flex items-center",children:[t("documentPanel.documentManager.columns.id"),f==="id"&&i.jsx("span",{className:"ml-1",children:P==="asc"?i.jsx(Oe,{size:14}):i.jsx(Ae,{size:14})})]})}),i.jsx(J,{children:t("documentPanel.documentManager.columns.summary")}),i.jsx(J,{children:t("documentPanel.documentManager.columns.status")}),i.jsx(J,{children:t("documentPanel.documentManager.columns.length")}),i.jsx(J,{children:t("documentPanel.documentManager.columns.chunks")}),i.jsx(J,{onClick:()=>B("created_at"),className:"cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-800 select-none",children:i.jsxs("div",{className:"flex items-center",children:[t("documentPanel.documentManager.columns.created"),f==="created_at"&&i.jsx("span",{className:"ml-1",children:P==="asc"?i.jsx(Oe,{size:14}):i.jsx(Ae,{size:14})})]})}),i.jsx(J,{onClick:()=>B("updated_at"),className:"cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-800 select-none",children:i.jsxs("div",{className:"flex items-center",children:[t("documentPanel.documentManager.columns.updated"),f==="updated_at"&&i.jsx("span",{className:"ml-1",children:P==="asc"?i.jsx(Oe,{size:14}):i.jsx(Ae,{size:14})})]})})]})}),i.jsx(Ra,{className:"text-sm overflow-auto",children:W&&W.map(p=>i.jsxs($e,{children:[i.jsx(V,{className:"truncate font-mono overflow-visible max-w-[250px]",children:v?i.jsxs(i.Fragment,{children:[i.jsxs("div",{className:"group relative overflow-visible tooltip-container",children:[i.jsx("div",{className:"truncate",children:Ie(p,30)}),i.jsx("div",{className:"invisible group-hover:visible tooltip",children:p.file_path})]}),i.jsx("div",{className:"text-xs text-gray-500",children:p.id})]}):i.jsxs("div",{className:"group relative overflow-visible tooltip-container",children:[i.jsx("div",{className:"truncate",children:p.id}),i.jsx("div",{className:"invisible group-hover:visible tooltip",children:p.file_path})]})}),i.jsx(V,{className:"max-w-xs min-w-45 truncate overflow-visible",children:i.jsxs("div",{className:"group relative overflow-visible tooltip-container",children:[i.jsx("div",{className:"truncate",children:p.content_summary}),i.jsx("div",{className:"invisible group-hover:visible tooltip",children:p.content_summary})]})}),i.jsxs(V,{children:[p.status==="processed"&&i.jsx("span",{className:"text-green-600",children:t("documentPanel.documentManager.status.completed")}),p.status==="processing"&&i.jsx("span",{className:"text-blue-600",children:t("documentPanel.documentManager.status.processing")}),p.status==="pending"&&i.jsx("span",{className:"text-yellow-600",children:t("documentPanel.documentManager.status.pending")}),p.status==="failed"&&i.jsx("span",{className:"text-red-600",children:t("documentPanel.documentManager.status.failed")}),p.error&&i.jsx("span",{className:"ml-2 text-red-500",title:p.error,children:"⚠️"})]}),i.jsx(V,{children:p.content_length??"-"}),i.jsx(V,{children:p.chunks_count??"-"}),i.jsx(V,{className:"truncate",children:new Date(p.created_at).toLocaleString()}),i.jsx(V,{className:"truncate",children:new Date(p.updated_at).toLocaleString()})]},p.id))})]})})})]})]})]})]})}export{Ti as D};
