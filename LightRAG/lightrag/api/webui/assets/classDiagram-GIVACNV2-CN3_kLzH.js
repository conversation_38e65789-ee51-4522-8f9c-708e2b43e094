import{s as a,c as s,a as e,C as t}from"./chunk-A2AXSNBT-C3_hWPtY.js";import{_ as i}from"./mermaid-vendor-S2u3NfNd.js";import"./chunk-RZ5BOZE2-DAdu8FE8.js";import"./feature-graph-DGPXw7qg.js";import"./react-vendor-DEwriMA6.js";import"./graph-vendor-B-X5JegA.js";import"./ui-vendor-CeCm8EER.js";import"./utils-vendor-BysuhMZA.js";var f={parser:e,get db(){return new t},renderer:s,styles:a,init:i(r=>{r.class||(r.class={}),r.class.arrowMarkerAbsolute=r.arrowMarkerAbsolute},"init")};export{f as diagram};
