import{p as Zt,c as Jt,d as en,v as tn,S as rn}from"./markdown-vendor-DmIvJdn7.js";import"./ui-vendor-CeCm8EER.js";import"./react-vendor-DEwriMA6.js";class xe{constructor(e,t,a){this.normal=t,this.property=e,a&&(this.space=a)}}xe.prototype.normal={};xe.prototype.property={};xe.prototype.space=void 0;function Wr(r,e){const t={},a={};for(const n of r)Object.assign(t,n.property),Object.assign(a,n.normal);return new xe(t,a,e)}function ge(r){return r.toLowerCase()}class m0{constructor(e,t){this.attribute=t,this.property=e}}m0.prototype.attribute="";m0.prototype.booleanish=!1;m0.prototype.boolean=!1;m0.prototype.commaOrSpaceSeparated=!1;m0.prototype.commaSeparated=!1;m0.prototype.defined=!1;m0.prototype.mustUseProperty=!1;m0.prototype.number=!1;m0.prototype.overloadedBoolean=!1;m0.prototype.property="";m0.prototype.spaceSeparated=!1;m0.prototype.space=void 0;let an=0;const P=_0(),r0=_0(),bt=_0(),z=_0(),j=_0(),oe=_0(),f0=_0();function _0(){return 2**++an}const yt=Object.freeze(Object.defineProperty({__proto__:null,boolean:P,booleanish:r0,commaOrSpaceSeparated:f0,commaSeparated:oe,number:z,overloadedBoolean:bt,spaceSeparated:j},Symbol.toStringTag,{value:"Module"})),et=Object.keys(yt);class Dt extends m0{constructor(e,t,a,n){let l=-1;if(super(e,t),Qt(this,"space",n),typeof a=="number")for(;++l<et.length;){const o=et[l];Qt(this,et[l],(a&yt[o])===yt[o])}}}Dt.prototype.defined=!0;function Qt(r,e,t){t&&(r[e]=t)}function ce(r){const e={},t={};for(const[a,n]of Object.entries(r.properties)){const l=new Dt(a,r.transform(r.attributes||{},a),n,r.space);r.mustUseProperty&&r.mustUseProperty.includes(a)&&(l.mustUseProperty=!0),e[a]=l,t[ge(a)]=a,t[ge(l.attribute)]=a}return new xe(e,t,r.space)}const Xr=ce({properties:{ariaActiveDescendant:null,ariaAtomic:r0,ariaAutoComplete:null,ariaBusy:r0,ariaChecked:r0,ariaColCount:z,ariaColIndex:z,ariaColSpan:z,ariaControls:j,ariaCurrent:null,ariaDescribedBy:j,ariaDetails:null,ariaDisabled:r0,ariaDropEffect:j,ariaErrorMessage:null,ariaExpanded:r0,ariaFlowTo:j,ariaGrabbed:r0,ariaHasPopup:null,ariaHidden:r0,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:j,ariaLevel:z,ariaLive:null,ariaModal:r0,ariaMultiLine:r0,ariaMultiSelectable:r0,ariaOrientation:null,ariaOwns:j,ariaPlaceholder:null,ariaPosInSet:z,ariaPressed:r0,ariaReadOnly:r0,ariaRelevant:null,ariaRequired:r0,ariaRoleDescription:j,ariaRowCount:z,ariaRowIndex:z,ariaRowSpan:z,ariaSelected:r0,ariaSetSize:z,ariaSort:null,ariaValueMax:z,ariaValueMin:z,ariaValueNow:z,ariaValueText:null,role:null},transform(r,e){return e==="role"?e:"aria-"+e.slice(4).toLowerCase()}});function $r(r,e){return e in r?r[e]:e}function jr(r,e){return $r(r,e.toLowerCase())}const nn=ce({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:oe,acceptCharset:j,accessKey:j,action:null,allow:null,allowFullScreen:P,allowPaymentRequest:P,allowUserMedia:P,alt:null,as:null,async:P,autoCapitalize:null,autoComplete:j,autoFocus:P,autoPlay:P,blocking:j,capture:null,charSet:null,checked:P,cite:null,className:j,cols:z,colSpan:null,content:null,contentEditable:r0,controls:P,controlsList:j,coords:z|oe,crossOrigin:null,data:null,dateTime:null,decoding:null,default:P,defer:P,dir:null,dirName:null,disabled:P,download:bt,draggable:r0,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:P,formTarget:null,headers:j,height:z,hidden:bt,high:z,href:null,hrefLang:null,htmlFor:j,httpEquiv:j,id:null,imageSizes:null,imageSrcSet:null,inert:P,inputMode:null,integrity:null,is:null,isMap:P,itemId:null,itemProp:j,itemRef:j,itemScope:P,itemType:j,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:P,low:z,manifest:null,max:null,maxLength:z,media:null,method:null,min:null,minLength:z,multiple:P,muted:P,name:null,nonce:null,noModule:P,noValidate:P,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:P,optimum:z,pattern:null,ping:j,placeholder:null,playsInline:P,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:P,referrerPolicy:null,rel:j,required:P,reversed:P,rows:z,rowSpan:z,sandbox:j,scope:null,scoped:P,seamless:P,selected:P,shadowRootClonable:P,shadowRootDelegatesFocus:P,shadowRootMode:null,shape:null,size:z,sizes:null,slot:null,span:z,spellCheck:r0,src:null,srcDoc:null,srcLang:null,srcSet:null,start:z,step:null,style:null,tabIndex:z,target:null,title:null,translate:null,type:null,typeMustMatch:P,useMap:null,value:r0,width:z,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:j,axis:null,background:null,bgColor:null,border:z,borderColor:null,bottomMargin:z,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:P,declare:P,event:null,face:null,frame:null,frameBorder:null,hSpace:z,leftMargin:z,link:null,longDesc:null,lowSrc:null,marginHeight:z,marginWidth:z,noResize:P,noHref:P,noShade:P,noWrap:P,object:null,profile:null,prompt:null,rev:null,rightMargin:z,rules:null,scheme:null,scrolling:r0,standby:null,summary:null,text:null,topMargin:z,valueType:null,version:null,vAlign:null,vLink:null,vSpace:z,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:P,disableRemotePlayback:P,prefix:null,property:null,results:z,security:null,unselectable:null},space:"html",transform:jr}),ln=ce({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:f0,accentHeight:z,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:z,amplitude:z,arabicForm:null,ascent:z,attributeName:null,attributeType:null,azimuth:z,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:z,by:null,calcMode:null,capHeight:z,className:j,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:z,diffuseConstant:z,direction:null,display:null,dur:null,divisor:z,dominantBaseline:null,download:P,dx:null,dy:null,edgeMode:null,editable:null,elevation:z,enableBackground:null,end:null,event:null,exponent:z,externalResourcesRequired:null,fill:null,fillOpacity:z,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:oe,g2:oe,glyphName:oe,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:z,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:z,horizOriginX:z,horizOriginY:z,id:null,ideographic:z,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:z,k:z,k1:z,k2:z,k3:z,k4:z,kernelMatrix:f0,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:z,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:z,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:z,overlineThickness:z,paintOrder:null,panose1:null,path:null,pathLength:z,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:j,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:z,pointsAtY:z,pointsAtZ:z,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:f0,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:f0,rev:f0,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:f0,requiredFeatures:f0,requiredFonts:f0,requiredFormats:f0,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:z,specularExponent:z,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:z,strikethroughThickness:z,string:null,stroke:null,strokeDashArray:f0,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:z,strokeOpacity:z,strokeWidth:null,style:null,surfaceScale:z,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:f0,tabIndex:z,tableValues:null,target:null,targetX:z,targetY:z,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:f0,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:z,underlineThickness:z,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:z,values:null,vAlphabetic:z,vMathematical:z,vectorEffect:null,vHanging:z,vIdeographic:z,version:null,vertAdvY:z,vertOriginX:z,vertOriginY:z,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:z,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:$r}),Kr=ce({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform(r,e){return"xlink:"+e.slice(5).toLowerCase()}}),Zr=ce({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:jr}),Jr=ce({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform(r,e){return"xml:"+e.slice(3).toLowerCase()}}),sn=/[A-Z]/g,_t=/-[a-z]/g,on=/^data[-\w.:]+$/i;function un(r,e){const t=ge(e);let a=e,n=m0;if(t in r.normal)return r.property[r.normal[t]];if(t.length>4&&t.slice(0,4)==="data"&&on.test(e)){if(e.charAt(4)==="-"){const l=e.slice(5).replace(_t,cn);a="data"+l.charAt(0).toUpperCase()+l.slice(1)}else{const l=e.slice(4);if(!_t.test(l)){let o=l.replace(sn,hn);o.charAt(0)!=="-"&&(o="-"+o),e="data"+o}}n=Dt}return new n(a,e)}function hn(r){return"-"+r.toLowerCase()}function cn(r){return r.charAt(1).toUpperCase()}const mn=Wr([Xr,nn,Kr,Zr,Jr],"html"),dn=Wr([Xr,ln,Kr,Zr,Jr],"svg"),er=/[#.]/g;function fn(r,e){const t=r||"",a={};let n=0,l,o;for(;n<t.length;){er.lastIndex=n;const h=er.exec(t),c=t.slice(n,h?h.index:t.length);c&&(l?l==="#"?a.id=c:Array.isArray(a.className)?a.className.push(c):a.className=[c]:o=c,n+=c.length),h&&(l=h[0],n++)}return{type:"element",tagName:o||e||"div",properties:a,children:[]}}function Qr(r,e,t){const a=t?bn(t):void 0;function n(l,o,...h){let c;if(l==null){c={type:"root",children:[]};const p=o;h.unshift(p)}else{c=fn(l,e);const p=c.tagName.toLowerCase(),g=a?a.get(p):void 0;if(c.tagName=g||p,pn(o))h.unshift(o);else for(const[b,x]of Object.entries(o))vn(r,c.properties,b,x)}for(const p of h)xt(c.children,p);return c.type==="element"&&c.tagName==="template"&&(c.content={type:"root",children:c.children},c.children=[]),c}return n}function pn(r){if(r===null||typeof r!="object"||Array.isArray(r))return!0;if(typeof r.type!="string")return!1;const e=r,t=Object.keys(r);for(const a of t){const n=e[a];if(n&&typeof n=="object"){if(!Array.isArray(n))return!0;const l=n;for(const o of l)if(typeof o!="number"&&typeof o!="string")return!0}}return!!("children"in r&&Array.isArray(r.children))}function vn(r,e,t,a){const n=un(r,t);let l;if(a!=null){if(typeof a=="number"){if(Number.isNaN(a))return;l=a}else typeof a=="boolean"?l=a:typeof a=="string"?n.spaceSeparated?l=Zt(a):n.commaSeparated?l=Jt(a):n.commaOrSpaceSeparated?l=Zt(Jt(a).join(" ")):l=tr(n,n.property,a):Array.isArray(a)?l=[...a]:l=n.property==="style"?gn(a):String(a);if(Array.isArray(l)){const o=[];for(const h of l)o.push(tr(n,n.property,h));l=o}n.property==="className"&&Array.isArray(e.className)&&(l=e.className.concat(l)),e[n.property]=l}}function xt(r,e){if(e!=null)if(typeof e=="number"||typeof e=="string")r.push({type:"text",value:String(e)});else if(Array.isArray(e))for(const t of e)xt(r,t);else if(typeof e=="object"&&"type"in e)e.type==="root"?xt(r,e.children):r.push(e);else throw new Error("Expected node, nodes, or string, got `"+e+"`")}function tr(r,e,t){if(typeof t=="string"){if(r.number&&t&&!Number.isNaN(Number(t)))return Number(t);if((r.boolean||r.overloadedBoolean)&&(t===""||ge(t)===ge(e)))return!0}return t}function gn(r){const e=[];for(const[t,a]of Object.entries(r))e.push([t,a].join(": "));return e.join("; ")}function bn(r){const e=new Map;for(const t of r)e.set(t.toLowerCase(),t);return e}const yn=["altGlyph","altGlyphDef","altGlyphItem","animateColor","animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","glyphRef","linearGradient","radialGradient","solidColor","textArea","textPath"],xn=Qr(mn,"div"),wn=Qr(dn,"g",yn),tt={html:"http://www.w3.org/1999/xhtml",svg:"http://www.w3.org/2000/svg"};function kn(r,e){return _r(r,{})||{type:"root",children:[]}}function _r(r,e){const t=Sn(r,e);return t&&e.afterTransform&&e.afterTransform(r,t),t}function Sn(r,e){switch(r.nodeType){case 1:return Tn(r,e);case 3:return zn(r);case 8:return An(r);case 9:return rr(r,e);case 10:return Mn();case 11:return rr(r,e);default:return}}function rr(r,e){return{type:"root",children:ea(r,e)}}function Mn(){return{type:"doctype"}}function zn(r){return{type:"text",value:r.nodeValue||""}}function An(r){return{type:"comment",value:r.nodeValue||""}}function Tn(r,e){const t=r.namespaceURI,a=t===tt.svg?wn:xn,n=t===tt.html?r.tagName.toLowerCase():r.tagName,l=t===tt.html&&n==="template"?r.content:r,o=r.getAttributeNames(),h={};let c=-1;for(;++c<o.length;)h[o[c]]=r.getAttribute(o[c])||"";return a(n,h,ea(l,e))}function ea(r,e){const t=r.childNodes,a=[];let n=-1;for(;++n<t.length;){const l=_r(t[n],e);l!==void 0&&a.push(l)}return a}new DOMParser;function Cn(r,e){const t=Dn(r);return kn(t)}function Dn(r){const e=document.createElement("template");return e.innerHTML=r,e.content}const ar=function(r,e,t){const a=en(t);if(!r||!r.type||!r.children)throw new Error("Expected parent node");if(typeof e=="number"){if(e<0||e===Number.POSITIVE_INFINITY)throw new Error("Expected positive finite number as index")}else if(e=r.children.indexOf(e),e<0)throw new Error("Expected child node or index");for(;++e<r.children.length;)if(a(r.children[e],e,r))return r.children[e]},ee=function(r){if(r==null)return En;if(typeof r=="string")return Nn(r);if(typeof r=="object")return Bn(r);if(typeof r=="function")return Bt(r);throw new Error("Expected function, string, or array as `test`")};function Bn(r){const e=[];let t=-1;for(;++t<r.length;)e[t]=ee(r[t]);return Bt(a);function a(...n){let l=-1;for(;++l<e.length;)if(e[l].apply(this,n))return!0;return!1}}function Nn(r){return Bt(e);function e(t){return t.tagName===r}}function Bt(r){return e;function e(t,a,n){return!!(qn(t)&&r.call(this,t,typeof a=="number"?a:void 0,n||void 0))}}function En(r){return!!(r&&typeof r=="object"&&"type"in r&&r.type==="element"&&"tagName"in r&&typeof r.tagName=="string")}function qn(r){return r!==null&&typeof r=="object"&&"type"in r&&"tagName"in r}const nr=/\n/g,lr=/[\t ]+/g,wt=ee("br"),ir=ee(Vn),Rn=ee("p"),sr=ee("tr"),On=ee(["datalist","head","noembed","noframes","noscript","rp","script","style","template","title",Hn,Un]),ta=ee(["address","article","aside","blockquote","body","caption","center","dd","dialog","dir","dl","dt","div","figure","figcaption","footer","form,","h1","h2","h3","h4","h5","h6","header","hgroup","hr","html","legend","li","listing","main","menu","nav","ol","p","plaintext","pre","section","ul","xmp"]);function Ln(r,e){const t=e||{},a="children"in r?r.children:[],n=ta(r),l=na(r,{whitespace:t.whitespace||"normal"}),o=[];(r.type==="text"||r.type==="comment")&&o.push(...aa(r,{breakBefore:!0,breakAfter:!0}));let h=-1;for(;++h<a.length;)o.push(...ra(a[h],r,{whitespace:l,breakBefore:h?void 0:n,breakAfter:h<a.length-1?wt(a[h+1]):n}));const c=[];let p;for(h=-1;++h<o.length;){const g=o[h];typeof g=="number"?p!==void 0&&g>p&&(p=g):g&&(p!==void 0&&p>-1&&c.push(`
`.repeat(p)||" "),p=-1,c.push(g))}return c.join("")}function ra(r,e,t){return r.type==="element"?In(r,e,t):r.type==="text"?t.whitespace==="normal"?aa(r,t):Fn(r):[]}function In(r,e,t){const a=na(r,t),n=r.children||[];let l=-1,o=[];if(On(r))return o;let h,c;for(wt(r)||sr(r)&&ar(e,r,sr)?c=`
`:Rn(r)?(h=2,c=2):ta(r)&&(h=1,c=1);++l<n.length;)o=o.concat(ra(n[l],r,{whitespace:a,breakBefore:l?void 0:h,breakAfter:l<n.length-1?wt(n[l+1]):c}));return ir(r)&&ar(e,r,ir)&&o.push("	"),h&&o.unshift(h),c&&o.push(c),o}function aa(r,e){const t=String(r.value),a=[],n=[];let l=0;for(;l<=t.length;){nr.lastIndex=l;const c=nr.exec(t),p=c&&"index"in c?c.index:t.length;a.push(Pn(t.slice(l,p).replace(/[\u061C\u200E\u200F\u202A-\u202E\u2066-\u2069]/g,""),l===0?e.breakBefore:!0,p===t.length?e.breakAfter:!0)),l=p+1}let o=-1,h;for(;++o<a.length;)a[o].charCodeAt(a[o].length-1)===8203||o<a.length-1&&a[o+1].charCodeAt(0)===8203?(n.push(a[o]),h=void 0):a[o]?(typeof h=="number"&&n.push(h),n.push(a[o]),h=0):(o===0||o===a.length-1)&&n.push(0);return n}function Fn(r){return[String(r.value)]}function Pn(r,e,t){const a=[];let n=0,l;for(;n<r.length;){lr.lastIndex=n;const o=lr.exec(r);l=o?o.index:r.length,!n&&!l&&o&&!e&&a.push(""),n!==l&&a.push(r.slice(n,l)),n=o?l+o[0].length:l}return n!==l&&!t&&a.push(""),a.join(" ")}function na(r,e){if(r.type==="element"){const t=r.properties||{};switch(r.tagName){case"listing":case"plaintext":case"xmp":return"pre";case"nobr":return"nowrap";case"pre":return t.wrap?"pre-wrap":"pre";case"td":case"th":return t.noWrap?"nowrap":e.whitespace;case"textarea":return"pre-wrap"}}return e.whitespace}function Hn(r){return!!(r.properties||{}).hidden}function Vn(r){return r.tagName==="td"||r.tagName==="th"}function Un(r){return r.tagName==="dialog"&&!(r.properties||{}).open}class p0{constructor(e,t,a){this.lexer=void 0,this.start=void 0,this.end=void 0,this.lexer=e,this.start=t,this.end=a}static range(e,t){return t?!e||!e.loc||!t.loc||e.loc.lexer!==t.loc.lexer?null:new p0(e.loc.lexer,e.loc.start,t.loc.end):e&&e.loc}}class x0{constructor(e,t){this.text=void 0,this.loc=void 0,this.noexpand=void 0,this.treatAsRelax=void 0,this.text=e,this.loc=t}range(e,t){return new x0(t,p0.range(this,e))}}class A{constructor(e,t){this.name=void 0,this.position=void 0,this.length=void 0,this.rawMessage=void 0;var a="KaTeX parse error: "+e,n,l,o=t&&t.loc;if(o&&o.start<=o.end){var h=o.lexer.input;n=o.start,l=o.end,n===h.length?a+=" at end of input: ":a+=" at position "+(n+1)+": ";var c=h.slice(n,l).replace(/[^]/g,"$&̲"),p;n>15?p="…"+h.slice(n-15,n):p=h.slice(0,n);var g;l+15<h.length?g=h.slice(l,l+15)+"…":g=h.slice(l),a+=p+c+g}var b=new Error(a);return b.name="ParseError",b.__proto__=A.prototype,b.position=n,n!=null&&l!=null&&(b.length=l-n),b.rawMessage=e,b}}A.prototype.__proto__=Error.prototype;var Gn=function(e,t){return e.indexOf(t)!==-1},Yn=function(e,t){return e===void 0?t:e},Wn=/([A-Z])/g,Xn=function(e){return e.replace(Wn,"-$1").toLowerCase()},$n={"&":"&amp;",">":"&gt;","<":"&lt;",'"':"&quot;","'":"&#x27;"},jn=/[&><"']/g;function Kn(r){return String(r).replace(jn,e=>$n[e])}var la=function r(e){return e.type==="ordgroup"||e.type==="color"?e.body.length===1?r(e.body[0]):e:e.type==="font"?r(e.body):e},Zn=function(e){var t=la(e);return t.type==="mathord"||t.type==="textord"||t.type==="atom"},Jn=function(e){if(!e)throw new Error("Expected non-null, but got "+String(e));return e},Qn=function(e){var t=/^[\x00-\x20]*([^\\/#?]*?)(:|&#0*58|&#x0*3a|&colon)/i.exec(e);return t?t[2]!==":"||!/^[a-zA-Z][a-zA-Z0-9+\-.]*$/.test(t[1])?null:t[1].toLowerCase():"_relative"},q={contains:Gn,deflt:Yn,escape:Kn,hyphenate:Xn,getBaseElem:la,isCharacterBox:Zn,protocolFromUrl:Qn},rt={displayMode:{type:"boolean",description:"Render math in display mode, which puts the math in display style (so \\int and \\sum are large, for example), and centers the math on the page on its own line.",cli:"-d, --display-mode"},output:{type:{enum:["htmlAndMathml","html","mathml"]},description:"Determines the markup language of the output.",cli:"-F, --format <type>"},leqno:{type:"boolean",description:"Render display math in leqno style (left-justified tags)."},fleqn:{type:"boolean",description:"Render display math flush left."},throwOnError:{type:"boolean",default:!0,cli:"-t, --no-throw-on-error",cliDescription:"Render errors (in the color given by --error-color) instead of throwing a ParseError exception when encountering an error."},errorColor:{type:"string",default:"#cc0000",cli:"-c, --error-color <color>",cliDescription:"A color string given in the format 'rgb' or 'rrggbb' (no #). This option determines the color of errors rendered by the -t option.",cliProcessor:r=>"#"+r},macros:{type:"object",cli:"-m, --macro <def>",cliDescription:"Define custom macro of the form '\\foo:expansion' (use multiple -m arguments for multiple macros).",cliDefault:[],cliProcessor:(r,e)=>(e.push(r),e)},minRuleThickness:{type:"number",description:"Specifies a minimum thickness, in ems, for fraction lines, `\\sqrt` top lines, `{array}` vertical lines, `\\hline`, `\\hdashline`, `\\underline`, `\\overline`, and the borders of `\\fbox`, `\\boxed`, and `\\fcolorbox`.",processor:r=>Math.max(0,r),cli:"--min-rule-thickness <size>",cliProcessor:parseFloat},colorIsTextColor:{type:"boolean",description:"Makes \\color behave like LaTeX's 2-argument \\textcolor, instead of LaTeX's one-argument \\color mode change.",cli:"-b, --color-is-text-color"},strict:{type:[{enum:["warn","ignore","error"]},"boolean","function"],description:"Turn on strict / LaTeX faithfulness mode, which throws an error if the input uses features that are not supported by LaTeX.",cli:"-S, --strict",cliDefault:!1},trust:{type:["boolean","function"],description:"Trust the input, enabling all HTML features such as \\url.",cli:"-T, --trust"},maxSize:{type:"number",default:1/0,description:"If non-zero, all user-specified sizes, e.g. in \\rule{500em}{500em}, will be capped to maxSize ems. Otherwise, elements and spaces can be arbitrarily large",processor:r=>Math.max(0,r),cli:"-s, --max-size <n>",cliProcessor:parseInt},maxExpand:{type:"number",default:1e3,description:"Limit the number of macro expansions to the specified number, to prevent e.g. infinite macro loops. If set to Infinity, the macro expander will try to fully expand as in LaTeX.",processor:r=>Math.max(0,r),cli:"-e, --max-expand <n>",cliProcessor:r=>r==="Infinity"?1/0:parseInt(r)},globalGroup:{type:"boolean",cli:!1}};function _n(r){if(r.default)return r.default;var e=r.type,t=Array.isArray(e)?e[0]:e;if(typeof t!="string")return t.enum[0];switch(t){case"boolean":return!1;case"string":return"";case"number":return 0;case"object":return{}}}class e1{constructor(e){this.displayMode=void 0,this.output=void 0,this.leqno=void 0,this.fleqn=void 0,this.throwOnError=void 0,this.errorColor=void 0,this.macros=void 0,this.minRuleThickness=void 0,this.colorIsTextColor=void 0,this.strict=void 0,this.trust=void 0,this.maxSize=void 0,this.maxExpand=void 0,this.globalGroup=void 0,e=e||{};for(var t in rt)if(rt.hasOwnProperty(t)){var a=rt[t];this[t]=e[t]!==void 0?a.processor?a.processor(e[t]):e[t]:_n(a)}}reportNonstrict(e,t,a){var n=this.strict;if(typeof n=="function"&&(n=n(e,t,a)),!(!n||n==="ignore")){if(n===!0||n==="error")throw new A("LaTeX-incompatible input and strict mode is set to 'error': "+(t+" ["+e+"]"),a);n==="warn"?typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+(t+" ["+e+"]")):typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+n+"': "+t+" ["+e+"]"))}}useStrictBehavior(e,t,a){var n=this.strict;if(typeof n=="function")try{n=n(e,t,a)}catch{n="error"}return!n||n==="ignore"?!1:n===!0||n==="error"?!0:n==="warn"?(typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+(t+" ["+e+"]")),!1):(typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+n+"': "+t+" ["+e+"]")),!1)}isTrusted(e){if(e.url&&!e.protocol){var t=q.protocolFromUrl(e.url);if(t==null)return!1;e.protocol=t}var a=typeof this.trust=="function"?this.trust(e):this.trust;return!!a}}class U0{constructor(e,t,a){this.id=void 0,this.size=void 0,this.cramped=void 0,this.id=e,this.size=t,this.cramped=a}sup(){return z0[t1[this.id]]}sub(){return z0[r1[this.id]]}fracNum(){return z0[a1[this.id]]}fracDen(){return z0[n1[this.id]]}cramp(){return z0[l1[this.id]]}text(){return z0[i1[this.id]]}isTight(){return this.size>=2}}var Nt=0,Ie=1,ue=2,R0=3,be=4,y0=5,he=6,o0=7,z0=[new U0(Nt,0,!1),new U0(Ie,0,!0),new U0(ue,1,!1),new U0(R0,1,!0),new U0(be,2,!1),new U0(y0,2,!0),new U0(he,3,!1),new U0(o0,3,!0)],t1=[be,y0,be,y0,he,o0,he,o0],r1=[y0,y0,y0,y0,o0,o0,o0,o0],a1=[ue,R0,be,y0,he,o0,he,o0],n1=[R0,R0,y0,y0,o0,o0,o0,o0],l1=[Ie,Ie,R0,R0,y0,y0,o0,o0],i1=[Nt,Ie,ue,R0,ue,R0,ue,R0],O={DISPLAY:z0[Nt],TEXT:z0[ue],SCRIPT:z0[be],SCRIPTSCRIPT:z0[he]},kt=[{name:"latin",blocks:[[256,591],[768,879]]},{name:"cyrillic",blocks:[[1024,1279]]},{name:"armenian",blocks:[[1328,1423]]},{name:"brahmic",blocks:[[2304,4255]]},{name:"georgian",blocks:[[4256,4351]]},{name:"cjk",blocks:[[12288,12543],[19968,40879],[65280,65376]]},{name:"hangul",blocks:[[44032,55215]]}];function s1(r){for(var e=0;e<kt.length;e++)for(var t=kt[e],a=0;a<t.blocks.length;a++){var n=t.blocks[a];if(r>=n[0]&&r<=n[1])return t.name}return null}var Le=[];kt.forEach(r=>r.blocks.forEach(e=>Le.push(...e)));function ia(r){for(var e=0;e<Le.length;e+=2)if(r>=Le[e]&&r<=Le[e+1])return!0;return!1}var se=80,o1=function(e,t){return"M95,"+(622+e+t)+`
c-2.7,0,-7.17,-2.7,-13.5,-8c-5.8,-5.3,-9.5,-10,-9.5,-14
c0,-2,0.3,-3.3,1,-4c1.3,-2.7,23.83,-20.7,67.5,-54
c44.2,-33.3,65.8,-50.3,66.5,-51c1.3,-1.3,3,-2,5,-2c4.7,0,8.7,3.3,12,10
s173,378,173,378c0.7,0,35.3,-71,104,-213c68.7,-142,137.5,-285,206.5,-429
c69,-144,104.5,-217.7,106.5,-221
l`+e/2.075+" -"+e+`
c5.3,-9.3,12,-14,20,-14
H400000v`+(40+e)+`H845.2724
s-225.272,467,-225.272,467s-235,486,-235,486c-2.7,4.7,-9,7,-19,7
c-6,0,-10,-1,-12,-3s-194,-422,-194,-422s-65,47,-65,47z
M`+(834+e)+" "+t+"h400000v"+(40+e)+"h-400000z"},u1=function(e,t){return"M263,"+(601+e+t)+`c0.7,0,18,39.7,52,119
c34,79.3,68.167,158.7,102.5,238c34.3,79.3,51.8,119.3,52.5,120
c340,-704.7,510.7,-1060.3,512,-1067
l`+e/2.084+" -"+e+`
c4.7,-7.3,11,-11,19,-11
H40000v`+(40+e)+`H1012.3
s-271.3,567,-271.3,567c-38.7,80.7,-84,175,-136,283c-52,108,-89.167,185.3,-111.5,232
c-22.3,46.7,-33.8,70.3,-34.5,71c-4.7,4.7,-12.3,7,-23,7s-12,-1,-12,-1
s-109,-253,-109,-253c-72.7,-168,-109.3,-252,-110,-252c-10.7,8,-22,16.7,-34,26
c-22,17.3,-33.3,26,-34,26s-26,-26,-26,-26s76,-59,76,-59s76,-60,76,-60z
M`+(1001+e)+" "+t+"h400000v"+(40+e)+"h-400000z"},h1=function(e,t){return"M983 "+(10+e+t)+`
l`+e/3.13+" -"+e+`
c4,-6.7,10,-10,18,-10 H400000v`+(40+e)+`
H1013.1s-83.4,268,-264.1,840c-180.7,572,-277,876.3,-289,913c-4.7,4.7,-12.7,7,-24,7
s-12,0,-12,0c-1.3,-3.3,-3.7,-11.7,-7,-25c-35.3,-125.3,-106.7,-373.3,-214,-744
c-10,12,-21,25,-33,39s-32,39,-32,39c-6,-5.3,-15,-14,-27,-26s25,-30,25,-30
c26.7,-32.7,52,-63,76,-91s52,-60,52,-60s208,722,208,722
c56,-175.3,126.3,-397.3,211,-666c84.7,-268.7,153.8,-488.2,207.5,-658.5
c53.7,-170.3,84.5,-266.8,92.5,-289.5z
M`+(1001+e)+" "+t+"h400000v"+(40+e)+"h-400000z"},c1=function(e,t){return"M424,"+(2398+e+t)+`
c-1.3,-0.7,-38.5,-172,-111.5,-514c-73,-342,-109.8,-513.3,-110.5,-514
c0,-2,-10.7,14.3,-32,49c-4.7,7.3,-9.8,15.7,-15.5,25c-5.7,9.3,-9.8,16,-12.5,20
s-5,7,-5,7c-4,-3.3,-8.3,-7.7,-13,-13s-13,-13,-13,-13s76,-122,76,-122s77,-121,77,-121
s209,968,209,968c0,-2,84.7,-361.7,254,-1079c169.3,-717.3,254.7,-1077.7,256,-1081
l`+e/4.223+" -"+e+`c4,-6.7,10,-10,18,-10 H400000
v`+(40+e)+`H1014.6
s-87.3,378.7,-272.6,1166c-185.3,787.3,-279.3,1182.3,-282,1185
c-2,6,-10,9,-24,9
c-8,0,-12,-0.7,-12,-2z M`+(1001+e)+" "+t+`
h400000v`+(40+e)+"h-400000z"},m1=function(e,t){return"M473,"+(2713+e+t)+`
c339.3,-1799.3,509.3,-2700,510,-2702 l`+e/5.298+" -"+e+`
c3.3,-7.3,9.3,-11,18,-11 H400000v`+(40+e)+`H1017.7
s-90.5,478,-276.2,1466c-185.7,988,-279.5,1483,-281.5,1485c-2,6,-10,9,-24,9
c-8,0,-12,-0.7,-12,-2c0,-1.3,-5.3,-32,-16,-92c-50.7,-293.3,-119.7,-693.3,-207,-1200
c0,-1.3,-5.3,8.7,-16,30c-10.7,21.3,-21.3,42.7,-32,64s-16,33,-16,33s-26,-26,-26,-26
s76,-153,76,-153s77,-151,77,-151c0.7,0.7,35.7,202,105,604c67.3,400.7,102,602.7,104,
606zM`+(1001+e)+" "+t+"h400000v"+(40+e)+"H1017.7z"},d1=function(e){var t=e/2;return"M400000 "+e+" H0 L"+t+" 0 l65 45 L145 "+(e-80)+" H400000z"},f1=function(e,t,a){var n=a-54-t-e;return"M702 "+(e+t)+"H400000"+(40+e)+`
H742v`+n+`l-4 4-4 4c-.667.7 -2 1.5-4 2.5s-4.167 1.833-6.5 2.5-5.5 1-9.5 1
h-12l-28-84c-16.667-52-96.667 -294.333-240-727l-212 -643 -85 170
c-4-3.333-8.333-7.667-13 -13l-13-13l77-155 77-156c66 199.333 139 419.667
219 661 l218 661zM702 `+t+"H400000v"+(40+e)+"H742z"},p1=function(e,t,a){t=1e3*t;var n="";switch(e){case"sqrtMain":n=o1(t,se);break;case"sqrtSize1":n=u1(t,se);break;case"sqrtSize2":n=h1(t,se);break;case"sqrtSize3":n=c1(t,se);break;case"sqrtSize4":n=m1(t,se);break;case"sqrtTall":n=f1(t,se,a)}return n},v1=function(e,t){switch(e){case"⎜":return"M291 0 H417 V"+t+" H291z M291 0 H417 V"+t+" H291z";case"∣":return"M145 0 H188 V"+t+" H145z M145 0 H188 V"+t+" H145z";case"∥":return"M145 0 H188 V"+t+" H145z M145 0 H188 V"+t+" H145z"+("M367 0 H410 V"+t+" H367z M367 0 H410 V"+t+" H367z");case"⎟":return"M457 0 H583 V"+t+" H457z M457 0 H583 V"+t+" H457z";case"⎢":return"M319 0 H403 V"+t+" H319z M319 0 H403 V"+t+" H319z";case"⎥":return"M263 0 H347 V"+t+" H263z M263 0 H347 V"+t+" H263z";case"⎪":return"M384 0 H504 V"+t+" H384z M384 0 H504 V"+t+" H384z";case"⏐":return"M312 0 H355 V"+t+" H312z M312 0 H355 V"+t+" H312z";case"‖":return"M257 0 H300 V"+t+" H257z M257 0 H300 V"+t+" H257z"+("M478 0 H521 V"+t+" H478z M478 0 H521 V"+t+" H478z");default:return""}},or={doubleleftarrow:`M262 157
l10-10c34-36 62.7-77 86-123 3.3-8 5-13.3 5-16 0-5.3-6.7-8-20-8-7.3
 0-12.2.5-14.5 1.5-2.3 1-4.8 4.5-7.5 10.5-49.3 97.3-121.7 169.3-217 216-28
 14-57.3 25-88 33-6.7 2-11 3.8-13 5.5-2 1.7-3 4.2-3 7.5s1 5.8 3 7.5
c2 1.7 6.3 3.5 13 5.5 68 17.3 128.2 47.8 180.5 91.5 52.3 43.7 93.8 96.2 124.5
 157.5 9.3 8 15.3 12.3 18 13h6c12-.7 18-4 18-10 0-2-1.7-7-5-15-23.3-46-52-87
-86-123l-10-10h399738v-40H218c328 0 0 0 0 0l-10-8c-26.7-20-65.7-43-117-69 2.7
-2 6-3.7 10-5 36.7-16 72.3-37.3 107-64l10-8h399782v-40z
m8 0v40h399730v-40zm0 194v40h399730v-40z`,doublerightarrow:`M399738 392l
-10 10c-34 36-62.7 77-86 123-3.3 8-5 13.3-5 16 0 5.3 6.7 8 20 8 7.3 0 12.2-.5
 14.5-1.5 2.3-1 4.8-4.5 7.5-10.5 49.3-97.3 121.7-169.3 217-216 28-14 57.3-25 88
-33 6.7-2 11-3.8 13-5.5 2-1.7 3-4.2 3-7.5s-1-5.8-3-7.5c-2-1.7-6.3-3.5-13-5.5-68
-17.3-128.2-47.8-180.5-91.5-52.3-43.7-93.8-96.2-124.5-157.5-9.3-8-15.3-12.3-18
-13h-6c-12 .7-18 4-18 10 0 2 1.7 7 5 15 23.3 46 52 87 86 123l10 10H0v40h399782
c-328 0 0 0 0 0l10 8c26.7 20 65.7 43 117 69-2.7 2-6 3.7-10 5-36.7 16-72.3 37.3
-107 64l-10 8H0v40zM0 157v40h399730v-40zm0 194v40h399730v-40z`,leftarrow:`M400000 241H110l3-3c68.7-52.7 113.7-120
 135-202 4-14.7 6-23 6-25 0-7.3-7-11-21-11-8 0-13.2.8-15.5 2.5-2.3 1.7-4.2 5.8
-5.5 12.5-1.3 4.7-2.7 10.3-4 17-12 48.7-34.8 92-68.5 130S65.3 228.3 18 247
c-10 4-16 7.7-18 11 0 8.7 6 14.3 18 17 47.3 18.7 87.8 47 121.5 85S196 441.3 208
 490c.7 2 1.3 5 2 9s1.2 6.7 1.5 8c.3 1.3 1 3.3 2 6s2.2 4.5 3.5 5.5c1.3 1 3.3
 1.8 6 2.5s6 1 10 1c14 0 21-3.7 21-11 0-2-2-10.3-6-25-20-79.3-65-146.7-135-202
 l-3-3h399890zM100 241v40h399900v-40z`,leftbrace:`M6 548l-6-6v-35l6-11c56-104 135.3-181.3 238-232 57.3-28.7 117
-45 179-50h399577v120H403c-43.3 7-81 15-113 26-100.7 33-179.7 91-237 174-2.7
 5-6 9-10 13-.7 1-7.3 1-20 1H6z`,leftbraceunder:`M0 6l6-6h17c12.688 0 19.313.3 20 1 4 4 7.313 8.3 10 13
 35.313 51.3 80.813 93.8 136.5 127.5 55.688 33.7 117.188 55.8 184.5 66.5.688
 0 2 .3 4 1 18.688 2.7 76 4.3 172 5h399450v120H429l-6-1c-124.688-8-235-61.7
-331-161C60.687 138.7 32.312 99.3 7 54L0 41V6z`,leftgroup:`M400000 80
H435C64 80 168.3 229.4 21 260c-5.9 1.2-18 0-18 0-2 0-3-1-3-3v-38C76 61 257 0
 435 0h399565z`,leftgroupunder:`M400000 262
H435C64 262 168.3 112.6 21 82c-5.9-1.2-18 0-18 0-2 0-3 1-3 3v38c76 158 257 219
 435 219h399565z`,leftharpoon:`M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3
-3.3 10.2-9.5 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5
-18.3 3-21-1.3-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7
-196 228-6.7 4.7-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40z`,leftharpoonplus:`M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3-3.3 10.2-9.5
 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5-18.3 3-21-1.3
-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7-196 228-6.7 4.7
-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40zM0 435v40h400000v-40z
m0 0v40h400000v-40z`,leftharpoondown:`M7 241c-4 4-6.333 8.667-7 14 0 5.333.667 9 2 11s5.333
 5.333 12 10c90.667 54 156 130 196 228 3.333 10.667 6.333 16.333 9 17 2 .667 5
 1 9 1h5c10.667 0 16.667-2 18-6 2-2.667 1-9.667-3-21-32-87.333-82.667-157.667
-152-211l-3-3h399907v-40zM93 281 H400000 v-40L7 241z`,leftharpoondownplus:`M7 435c-4 4-6.3 8.7-7 14 0 5.3.7 9 2 11s5.3 5.3 12
 10c90.7 54 156 130 196 228 3.3 10.7 6.3 16.3 9 17 2 .7 5 1 9 1h5c10.7 0 16.7
-2 18-6 2-2.7 1-9.7-3-21-32-87.3-82.7-157.7-152-211l-3-3h399907v-40H7zm93 0
v40h399900v-40zM0 241v40h399900v-40zm0 0v40h399900v-40z`,lefthook:`M400000 281 H103s-33-11.2-61-33.5S0 197.3 0 164s14.2-61.2 42.5
-83.5C70.8 58.2 104 47 142 47 c16.7 0 25 6.7 25 20 0 12-8.7 18.7-26 20-40 3.3
-68.7 15.7-86 37-10 12-15 25.3-15 40 0 22.7 9.8 40.7 29.5 54 19.7 13.3 43.5 21
 71.5 23h399859zM103 281v-40h399897v40z`,leftlinesegment:`M40 281 V428 H0 V94 H40 V241 H400000 v40z
M40 281 V428 H0 V94 H40 V241 H400000 v40z`,leftmapsto:`M40 281 V448H0V74H40V241H400000v40z
M40 281 V448H0V74H40V241H400000v40z`,leftToFrom:`M0 147h400000v40H0zm0 214c68 40 115.7 95.7 143 167h22c15.3 0 23
-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69-70-101l-7-8h399905v-40H95l7-8
c28.7-32 52-65.7 70-101 10.7-23.3 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 265.3
 68 321 0 361zm0-174v-40h399900v40zm100 154v40h399900v-40z`,longequal:`M0 50 h400000 v40H0z m0 194h40000v40H0z
M0 50 h400000 v40H0z m0 194h40000v40H0z`,midbrace:`M200428 334
c-100.7-8.3-195.3-44-280-108-55.3-42-101.7-93-139-153l-9-14c-2.7 4-5.7 8.7-9 14
-53.3 86.7-123.7 153-211 199-66.7 36-137.3 56.3-212 62H0V214h199568c178.3-11.7
 311.7-78.3 403-201 6-8 9.7-12 11-12 .7-.7 6.7-1 18-1s17.3.3 18 1c1.3 0 5 4 11
 12 44.7 59.3 101.3 106.3 170 141s145.3 54.3 229 60h199572v120z`,midbraceunder:`M199572 214
c100.7 8.3 195.3 44 280 108 55.3 42 101.7 93 139 153l9 14c2.7-4 5.7-8.7 9-14
 53.3-86.7 123.7-153 211-199 66.7-36 137.3-56.3 212-62h199568v120H200432c-178.3
 11.7-311.7 78.3-403 201-6 8-9.7 12-11 12-.7.7-6.7 1-18 1s-17.3-.3-18-1c-1.3 0
-5-4-11-12-44.7-59.3-101.3-106.3-170-141s-145.3-54.3-229-60H0V214z`,oiintSize1:`M512.6 71.6c272.6 0 320.3 106.8 320.3 178.2 0 70.8-47.7 177.6
-320.3 177.6S193.1 320.6 193.1 249.8c0-71.4 46.9-178.2 319.5-178.2z
m368.1 178.2c0-86.4-60.9-215.4-368.1-215.4-306.4 0-367.3 129-367.3 215.4 0 85.8
60.9 214.8 367.3 214.8 307.2 0 368.1-129 368.1-214.8z`,oiintSize2:`M757.8 100.1c384.7 0 451.1 137.6 451.1 230 0 91.3-66.4 228.8
-451.1 228.8-386.3 0-452.7-137.5-452.7-228.8 0-92.4 66.4-230 452.7-230z
m502.4 230c0-111.2-82.4-277.2-502.4-277.2s-504 166-504 277.2
c0 110 84 276 504 276s502.4-166 502.4-276z`,oiiintSize1:`M681.4 71.6c408.9 0 480.5 106.8 480.5 178.2 0 70.8-71.6 177.6
-480.5 177.6S202.1 320.6 202.1 249.8c0-71.4 70.5-178.2 479.3-178.2z
m525.8 178.2c0-86.4-86.8-215.4-525.7-215.4-437.9 0-524.7 129-524.7 215.4 0
85.8 86.8 214.8 524.7 214.8 438.9 0 525.7-129 525.7-214.8z`,oiiintSize2:`M1021.2 53c603.6 0 707.8 165.8 707.8 277.2 0 110-104.2 275.8
-707.8 275.8-606 0-710.2-165.8-710.2-275.8C311 218.8 415.2 53 1021.2 53z
m770.4 277.1c0-131.2-126.4-327.6-770.5-327.6S248.4 198.9 248.4 330.1
c0 130 128.8 326.4 772.7 326.4s770.5-196.4 770.5-326.4z`,rightarrow:`M0 241v40h399891c-47.3 35.3-84 78-110 128
-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20
 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7
 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85
-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5
-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67
 151.7 139 205zm0 0v40h399900v-40z`,rightbrace:`M400000 542l
-6 6h-17c-12.7 0-19.3-.3-20-1-4-4-7.3-8.3-10-13-35.3-51.3-80.8-93.8-136.5-127.5
s-117.2-55.8-184.5-66.5c-.7 0-2-.3-4-1-18.7-2.7-76-4.3-172-5H0V214h399571l6 1
c124.7 8 235 61.7 331 161 31.3 33.3 59.7 72.7 85 118l7 13v35z`,rightbraceunder:`M399994 0l6 6v35l-6 11c-56 104-135.3 181.3-238 232-57.3
 28.7-117 45-179 50H-300V214h399897c43.3-7 81-15 113-26 100.7-33 179.7-91 237
-174 2.7-5 6-9 10-13 .7-1 7.3-1 20-1h17z`,rightgroup:`M0 80h399565c371 0 266.7 149.4 414 180 5.9 1.2 18 0 18 0 2 0
 3-1 3-3v-38c-76-158-257-219-435-219H0z`,rightgroupunder:`M0 262h399565c371 0 266.7-149.4 414-180 5.9-1.2 18 0 18
 0 2 0 3 1 3 3v38c-76 158-257 219-435 219H0z`,rightharpoon:`M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3
-3.7-15.3-11-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2
-10.7 0-16.7 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58
 69.2 92 94.5zm0 0v40h399900v-40z`,rightharpoonplus:`M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3-3.7-15.3-11
-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2-10.7 0-16.7
 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58 69.2 92 94.5z
m0 0v40h399900v-40z m100 194v40h399900v-40zm0 0v40h399900v-40z`,rightharpoondown:`M399747 511c0 7.3 6.7 11 20 11 8 0 13-.8 15-2.5s4.7-6.8
 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3 8.5-5.8 9.5
-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3-64.7 57-92 95
-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 241v40h399900v-40z`,rightharpoondownplus:`M399747 705c0 7.3 6.7 11 20 11 8 0 13-.8
 15-2.5s4.7-6.8 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3
 8.5-5.8 9.5-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3
-64.7 57-92 95-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 435v40h399900v-40z
m0-194v40h400000v-40zm0 0v40h400000v-40z`,righthook:`M399859 241c-764 0 0 0 0 0 40-3.3 68.7-15.7 86-37 10-12 15-25.3
 15-40 0-22.7-9.8-40.7-29.5-54-19.7-13.3-43.5-21-71.5-23-17.3-1.3-26-8-26-20 0
-13.3 8.7-20 26-20 38 0 71 11.2 99 33.5 0 0 7 5.6 21 16.7 14 11.2 21 33.5 21
 66.8s-14 61.2-42 83.5c-28 22.3-61 33.5-99 33.5L0 241z M0 281v-40h399859v40z`,rightlinesegment:`M399960 241 V94 h40 V428 h-40 V281 H0 v-40z
M399960 241 V94 h40 V428 h-40 V281 H0 v-40z`,rightToFrom:`M400000 167c-70.7-42-118-97.7-142-167h-23c-15.3 0-23 .3-23
 1 0 1.3 5.3 13.7 16 37 18 35.3 41.3 69 70 101l7 8H0v40h399905l-7 8c-28.7 32
-52 65.7-70 101-10.7 23.3-16 35.7-16 37 0 .7 7.7 1 23 1h23c24-69.3 71.3-125 142
-167z M100 147v40h399900v-40zM0 341v40h399900v-40z`,twoheadleftarrow:`M0 167c68 40
 115.7 95.7 143 167h22c15.3 0 23-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69
-70-101l-7-8h125l9 7c50.7 39.3 85 86 103 140h46c0-4.7-6.3-18.7-19-42-18-35.3
-40-67.3-66-96l-9-9h399716v-40H284l9-9c26-28.7 48-60.7 66-96 12.7-23.333 19
-37.333 19-42h-46c-18 54-52.3 100.7-103 140l-9 7H95l7-8c28.7-32 52-65.7 70-101
 10.7-23.333 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 71.3 68 127 0 167z`,twoheadrightarrow:`M400000 167
c-68-40-115.7-95.7-143-167h-22c-15.3 0-23 .3-23 1 0 1.3 5.3 13.7 16 37 18 35.3
 41.3 69 70 101l7 8h-125l-9-7c-50.7-39.3-85-86-103-140h-46c0 4.7 6.3 18.7 19 42
 18 35.3 40 67.3 66 96l9 9H0v40h399716l-9 9c-26 28.7-48 60.7-66 96-12.7 23.333
-19 37.333-19 42h46c18-54 52.3-100.7 103-140l9-7h125l-7 8c-28.7 32-52 65.7-70
 101-10.7 23.333-16 35.7-16 37 0 .7 7.7 1 23 1h22c27.3-71.3 75-127 143-167z`,tilde1:`M200 55.538c-77 0-168 73.953-177 73.953-3 0-7
-2.175-9-5.437L2 97c-1-2-2-4-2-6 0-4 2-7 5-9l20-12C116 12 171 0 207 0c86 0
 114 68 191 68 78 0 168-68 177-68 4 0 7 2 9 5l12 19c1 2.175 2 4.35 2 6.525 0
 4.35-2 7.613-5 9.788l-19 13.05c-92 63.077-116.937 75.308-183 76.128
-68.267.847-113-73.952-191-73.952z`,tilde2:`M344 55.266c-142 0-300.638 81.316-311.5 86.418
-8.01 3.762-22.5 10.91-23.5 5.562L1 120c-1-2-1-3-1-4 0-5 3-9 8-10l18.4-9C160.9
 31.9 283 0 358 0c148 0 188 122 331 122s314-97 326-97c4 0 8 2 10 7l7 21.114
c1 2.14 1 3.21 1 4.28 0 5.347-3 9.626-7 10.696l-22.3 12.622C852.6 158.372 751
 181.476 676 181.476c-149 0-189-126.21-332-126.21z`,tilde3:`M786 59C457 59 32 175.242 13 175.242c-6 0-10-3.457
-11-10.37L.15 138c-1-7 3-12 10-13l19.2-6.4C378.4 40.7 634.3 0 804.3 0c337 0
 411.8 157 746.8 157 328 0 754-112 773-112 5 0 10 3 11 9l1 14.075c1 8.066-.697
 16.595-6.697 17.492l-21.052 7.31c-367.9 98.146-609.15 122.696-778.15 122.696
 -338 0-409-156.573-744-156.573z`,tilde4:`M786 58C457 58 32 177.487 13 177.487c-6 0-10-3.345
-11-10.035L.15 143c-1-7 3-12 10-13l22-6.7C381.2 35 637.15 0 807.15 0c337 0 409
 177 744 177 328 0 754-127 773-127 5 0 10 3 11 9l1 14.794c1 7.805-3 13.38-9
 14.495l-20.7 5.574c-366.85 99.79-607.3 139.372-776.3 139.372-338 0-409
 -175.236-744-175.236z`,vec:`M377 20c0-5.333 1.833-10 5.5-14S391 0 397 0c4.667 0 8.667 1.667 12 5
3.333 2.667 6.667 9 10 19 6.667 24.667 20.333 43.667 41 57 7.333 4.667 11
10.667 11 18 0 6-1 10-3 12s-6.667 5-14 9c-28.667 14.667-53.667 35.667-75 63
-1.333 1.333-3.167 3.5-5.5 6.5s-4 4.833-5 5.5c-1 .667-2.5 1.333-4.5 2s-4.333 1
-7 1c-4.667 0-9.167-1.833-13.5-5.5S337 184 337 178c0-12.667 15.667-32.333 47-59
H213l-171-1c-8.667-6-13-12.333-13-19 0-4.667 4.333-11.333 13-20h359
c-16-25.333-24-45-24-59z`,widehat1:`M529 0h5l519 115c5 1 9 5 9 10 0 1-1 2-1 3l-4 22
c-1 5-5 9-11 9h-2L532 67 19 159h-2c-5 0-9-4-11-9l-5-22c-1-6 2-12 8-13z`,widehat2:`M1181 0h2l1171 176c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 220h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widehat3:`M1181 0h2l1171 236c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 280h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widehat4:`M1181 0h2l1171 296c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 340h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widecheck1:`M529,159h5l519,-115c5,-1,9,-5,9,-10c0,-1,-1,-2,-1,-3l-4,-22c-1,
-5,-5,-9,-11,-9h-2l-512,92l-513,-92h-2c-5,0,-9,4,-11,9l-5,22c-1,6,2,12,8,13z`,widecheck2:`M1181,220h2l1171,-176c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,153l-1167,-153h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,widecheck3:`M1181,280h2l1171,-236c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,213l-1167,-213h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,widecheck4:`M1181,340h2l1171,-296c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,273l-1167,-273h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,baraboveleftarrow:`M400000 620h-399890l3 -3c68.7 -52.7 113.7 -120 135 -202
c4 -14.7 6 -23 6 -25c0 -7.3 -7 -11 -21 -11c-8 0 -13.2 0.8 -15.5 2.5
c-2.3 1.7 -4.2 5.8 -5.5 12.5c-1.3 4.7 -2.7 10.3 -4 17c-12 48.7 -34.8 92 -68.5 130
s-74.2 66.3 -121.5 85c-10 4 -16 7.7 -18 11c0 8.7 6 14.3 18 17c47.3 18.7 87.8 47
121.5 85s56.5 81.3 68.5 130c0.7 2 1.3 5 2 9s1.2 6.7 1.5 8c0.3 1.3 1 3.3 2 6
s2.2 4.5 3.5 5.5c1.3 1 3.3 1.8 6 2.5s6 1 10 1c14 0 21 -3.7 21 -11
c0 -2 -2 -10.3 -6 -25c-20 -79.3 -65 -146.7 -135 -202l-3 -3h399890z
M100 620v40h399900v-40z M0 241v40h399900v-40zM0 241v40h399900v-40z`,rightarrowabovebar:`M0 241v40h399891c-47.3 35.3-84 78-110 128-16.7 32
-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20 11 8 0
13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7 39
-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85-40.5
-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5
-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67
151.7 139 205zm96 379h399894v40H0zm0 0h399904v40H0z`,baraboveshortleftharpoon:`M507,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11
c1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17
c2,0.7,5,1,9,1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21
c-32,-87.3,-82.7,-157.7,-152,-211c0,0,-3,-3,-3,-3l399351,0l0,-40
c-398570,0,-399437,0,-399437,0z M593 435 v40 H399500 v-40z
M0 281 v-40 H399908 v40z M0 281 v-40 H399908 v40z`,rightharpoonaboveshortbar:`M0,241 l0,40c399126,0,399993,0,399993,0
c4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,
-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6
c-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z
M0 241 v40 H399908 v-40z M0 475 v-40 H399500 v40z M0 475 v-40 H399500 v40z`,shortbaraboveleftharpoon:`M7,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11
c1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17c2,0.7,5,1,9,
1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21c-32,-87.3,-82.7,-157.7,
-152,-211c0,0,-3,-3,-3,-3l399907,0l0,-40c-399126,0,-399993,0,-399993,0z
M93 435 v40 H400000 v-40z M500 241 v40 H400000 v-40z M500 241 v40 H400000 v-40z`,shortrightharpoonabovebar:`M53,241l0,40c398570,0,399437,0,399437,0
c4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,
-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6
c-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z
M500 241 v40 H399408 v-40z M500 435 v40 H400000 v-40z`},g1=function(e,t){switch(e){case"lbrack":return"M403 1759 V84 H666 V0 H319 V1759 v"+t+` v1759 h347 v-84
H403z M403 1759 V0 H319 V1759 v`+t+" v1759 h84z";case"rbrack":return"M347 1759 V0 H0 V84 H263 V1759 v"+t+` v1759 H0 v84 H347z
M347 1759 V0 H263 V1759 v`+t+" v1759 h84z";case"vert":return"M145 15 v585 v"+t+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-t+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M188 15 H145 v585 v`+t+" v585 h43z";case"doublevert":return"M145 15 v585 v"+t+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-t+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M188 15 H145 v585 v`+t+` v585 h43z
M367 15 v585 v`+t+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-t+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M410 15 H367 v585 v`+t+" v585 h43z";case"lfloor":return"M319 602 V0 H403 V602 v"+t+` v1715 h263 v84 H319z
MM319 602 V0 H403 V602 v`+t+" v1715 H319z";case"rfloor":return"M319 602 V0 H403 V602 v"+t+` v1799 H0 v-84 H319z
MM319 602 V0 H403 V602 v`+t+" v1715 H319z";case"lceil":return"M403 1759 V84 H666 V0 H319 V1759 v"+t+` v602 h84z
M403 1759 V0 H319 V1759 v`+t+" v602 h84z";case"rceil":return"M347 1759 V0 H0 V84 H263 V1759 v"+t+` v602 h84z
M347 1759 V0 h-84 V1759 v`+t+" v602 h84z";case"lparen":return`M863,9c0,-2,-2,-5,-6,-9c0,0,-17,0,-17,0c-12.7,0,-19.3,0.3,-20,1
c-5.3,5.3,-10.3,11,-15,17c-242.7,294.7,-395.3,682,-458,1162c-21.3,163.3,-33.3,349,
-36,557 l0,`+(t+84)+`c0.2,6,0,26,0,60c2,159.3,10,310.7,24,454c53.3,528,210,
949.7,470,1265c4.7,6,9.7,11.7,15,17c0.7,0.7,7,1,19,1c0,0,18,0,18,0c4,-4,6,-7,6,-9
c0,-2.7,-3.3,-8.7,-10,-18c-135.3,-192.7,-235.5,-414.3,-300.5,-665c-65,-250.7,-102.5,
-544.7,-112.5,-882c-2,-104,-3,-167,-3,-189
l0,-`+(t+92)+`c0,-162.7,5.7,-314,17,-454c20.7,-272,63.7,-513,129,-723c65.3,
-210,155.3,-396.3,270,-559c6.7,-9.3,10,-15.3,10,-18z`;case"rparen":return`M76,0c-16.7,0,-25,3,-25,9c0,2,2,6.3,6,13c21.3,28.7,42.3,60.3,
63,95c96.7,156.7,172.8,332.5,228.5,527.5c55.7,195,92.8,416.5,111.5,664.5
c11.3,139.3,17,290.7,17,454c0,28,1.7,43,3.3,45l0,`+(t+9)+`
c-3,4,-3.3,16.7,-3.3,38c0,162,-5.7,313.7,-17,455c-18.7,248,-55.8,469.3,-111.5,664
c-55.7,194.7,-131.8,370.3,-228.5,527c-20.7,34.7,-41.7,66.3,-63,95c-2,3.3,-4,7,-6,11
c0,7.3,5.7,11,17,11c0,0,11,0,11,0c9.3,0,14.3,-0.3,15,-1c5.3,-5.3,10.3,-11,15,-17
c242.7,-294.7,395.3,-681.7,458,-1161c21.3,-164.7,33.3,-350.7,36,-558
l0,-`+(t+144)+`c-2,-159.3,-10,-310.7,-24,-454c-53.3,-528,-210,-949.7,
-470,-1265c-4.7,-6,-9.7,-11.7,-15,-17c-0.7,-0.7,-6.7,-1,-18,-1z`;default:throw new Error("Unknown stretchy delimiter.")}};class we{constructor(e){this.children=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.children=e,this.classes=[],this.height=0,this.depth=0,this.maxFontSize=0,this.style={}}hasClass(e){return q.contains(this.classes,e)}toNode(){for(var e=document.createDocumentFragment(),t=0;t<this.children.length;t++)e.appendChild(this.children[t].toNode());return e}toMarkup(){for(var e="",t=0;t<this.children.length;t++)e+=this.children[t].toMarkup();return e}toText(){var e=t=>t.toText();return this.children.map(e).join("")}}var q0={"AMS-Regular":{32:[0,0,0,0,.25],65:[0,.68889,0,0,.72222],66:[0,.68889,0,0,.66667],67:[0,.68889,0,0,.72222],68:[0,.68889,0,0,.72222],69:[0,.68889,0,0,.66667],70:[0,.68889,0,0,.61111],71:[0,.68889,0,0,.77778],72:[0,.68889,0,0,.77778],73:[0,.68889,0,0,.38889],74:[.16667,.68889,0,0,.5],75:[0,.68889,0,0,.77778],76:[0,.68889,0,0,.66667],77:[0,.68889,0,0,.94445],78:[0,.68889,0,0,.72222],79:[.16667,.68889,0,0,.77778],80:[0,.68889,0,0,.61111],81:[.16667,.68889,0,0,.77778],82:[0,.68889,0,0,.72222],83:[0,.68889,0,0,.55556],84:[0,.68889,0,0,.66667],85:[0,.68889,0,0,.72222],86:[0,.68889,0,0,.72222],87:[0,.68889,0,0,1],88:[0,.68889,0,0,.72222],89:[0,.68889,0,0,.72222],90:[0,.68889,0,0,.66667],107:[0,.68889,0,0,.55556],160:[0,0,0,0,.25],165:[0,.675,.025,0,.75],174:[.15559,.69224,0,0,.94666],240:[0,.68889,0,0,.55556],295:[0,.68889,0,0,.54028],710:[0,.825,0,0,2.33334],732:[0,.9,0,0,2.33334],770:[0,.825,0,0,2.33334],771:[0,.9,0,0,2.33334],989:[.08167,.58167,0,0,.77778],1008:[0,.43056,.04028,0,.66667],8245:[0,.54986,0,0,.275],8463:[0,.68889,0,0,.54028],8487:[0,.68889,0,0,.72222],8498:[0,.68889,0,0,.55556],8502:[0,.68889,0,0,.66667],8503:[0,.68889,0,0,.44445],8504:[0,.68889,0,0,.66667],8513:[0,.68889,0,0,.63889],8592:[-.03598,.46402,0,0,.5],8594:[-.03598,.46402,0,0,.5],8602:[-.13313,.36687,0,0,1],8603:[-.13313,.36687,0,0,1],8606:[.01354,.52239,0,0,1],8608:[.01354,.52239,0,0,1],8610:[.01354,.52239,0,0,1.11111],8611:[.01354,.52239,0,0,1.11111],8619:[0,.54986,0,0,1],8620:[0,.54986,0,0,1],8621:[-.13313,.37788,0,0,1.38889],8622:[-.13313,.36687,0,0,1],8624:[0,.69224,0,0,.5],8625:[0,.69224,0,0,.5],8630:[0,.43056,0,0,1],8631:[0,.43056,0,0,1],8634:[.08198,.58198,0,0,.77778],8635:[.08198,.58198,0,0,.77778],8638:[.19444,.69224,0,0,.41667],8639:[.19444,.69224,0,0,.41667],8642:[.19444,.69224,0,0,.41667],8643:[.19444,.69224,0,0,.41667],8644:[.1808,.675,0,0,1],8646:[.1808,.675,0,0,1],8647:[.1808,.675,0,0,1],8648:[.19444,.69224,0,0,.83334],8649:[.1808,.675,0,0,1],8650:[.19444,.69224,0,0,.83334],8651:[.01354,.52239,0,0,1],8652:[.01354,.52239,0,0,1],8653:[-.13313,.36687,0,0,1],8654:[-.13313,.36687,0,0,1],8655:[-.13313,.36687,0,0,1],8666:[.13667,.63667,0,0,1],8667:[.13667,.63667,0,0,1],8669:[-.13313,.37788,0,0,1],8672:[-.064,.437,0,0,1.334],8674:[-.064,.437,0,0,1.334],8705:[0,.825,0,0,.5],8708:[0,.68889,0,0,.55556],8709:[.08167,.58167,0,0,.77778],8717:[0,.43056,0,0,.42917],8722:[-.03598,.46402,0,0,.5],8724:[.08198,.69224,0,0,.77778],8726:[.08167,.58167,0,0,.77778],8733:[0,.69224,0,0,.77778],8736:[0,.69224,0,0,.72222],8737:[0,.69224,0,0,.72222],8738:[.03517,.52239,0,0,.72222],8739:[.08167,.58167,0,0,.22222],8740:[.25142,.74111,0,0,.27778],8741:[.08167,.58167,0,0,.38889],8742:[.25142,.74111,0,0,.5],8756:[0,.69224,0,0,.66667],8757:[0,.69224,0,0,.66667],8764:[-.13313,.36687,0,0,.77778],8765:[-.13313,.37788,0,0,.77778],8769:[-.13313,.36687,0,0,.77778],8770:[-.03625,.46375,0,0,.77778],8774:[.30274,.79383,0,0,.77778],8776:[-.01688,.48312,0,0,.77778],8778:[.08167,.58167,0,0,.77778],8782:[.06062,.54986,0,0,.77778],8783:[.06062,.54986,0,0,.77778],8785:[.08198,.58198,0,0,.77778],8786:[.08198,.58198,0,0,.77778],8787:[.08198,.58198,0,0,.77778],8790:[0,.69224,0,0,.77778],8791:[.22958,.72958,0,0,.77778],8796:[.08198,.91667,0,0,.77778],8806:[.25583,.75583,0,0,.77778],8807:[.25583,.75583,0,0,.77778],8808:[.25142,.75726,0,0,.77778],8809:[.25142,.75726,0,0,.77778],8812:[.25583,.75583,0,0,.5],8814:[.20576,.70576,0,0,.77778],8815:[.20576,.70576,0,0,.77778],8816:[.30274,.79383,0,0,.77778],8817:[.30274,.79383,0,0,.77778],8818:[.22958,.72958,0,0,.77778],8819:[.22958,.72958,0,0,.77778],8822:[.1808,.675,0,0,.77778],8823:[.1808,.675,0,0,.77778],8828:[.13667,.63667,0,0,.77778],8829:[.13667,.63667,0,0,.77778],8830:[.22958,.72958,0,0,.77778],8831:[.22958,.72958,0,0,.77778],8832:[.20576,.70576,0,0,.77778],8833:[.20576,.70576,0,0,.77778],8840:[.30274,.79383,0,0,.77778],8841:[.30274,.79383,0,0,.77778],8842:[.13597,.63597,0,0,.77778],8843:[.13597,.63597,0,0,.77778],8847:[.03517,.54986,0,0,.77778],8848:[.03517,.54986,0,0,.77778],8858:[.08198,.58198,0,0,.77778],8859:[.08198,.58198,0,0,.77778],8861:[.08198,.58198,0,0,.77778],8862:[0,.675,0,0,.77778],8863:[0,.675,0,0,.77778],8864:[0,.675,0,0,.77778],8865:[0,.675,0,0,.77778],8872:[0,.69224,0,0,.61111],8873:[0,.69224,0,0,.72222],8874:[0,.69224,0,0,.88889],8876:[0,.68889,0,0,.61111],8877:[0,.68889,0,0,.61111],8878:[0,.68889,0,0,.72222],8879:[0,.68889,0,0,.72222],8882:[.03517,.54986,0,0,.77778],8883:[.03517,.54986,0,0,.77778],8884:[.13667,.63667,0,0,.77778],8885:[.13667,.63667,0,0,.77778],8888:[0,.54986,0,0,1.11111],8890:[.19444,.43056,0,0,.55556],8891:[.19444,.69224,0,0,.61111],8892:[.19444,.69224,0,0,.61111],8901:[0,.54986,0,0,.27778],8903:[.08167,.58167,0,0,.77778],8905:[.08167,.58167,0,0,.77778],8906:[.08167,.58167,0,0,.77778],8907:[0,.69224,0,0,.77778],8908:[0,.69224,0,0,.77778],8909:[-.03598,.46402,0,0,.77778],8910:[0,.54986,0,0,.76042],8911:[0,.54986,0,0,.76042],8912:[.03517,.54986,0,0,.77778],8913:[.03517,.54986,0,0,.77778],8914:[0,.54986,0,0,.66667],8915:[0,.54986,0,0,.66667],8916:[0,.69224,0,0,.66667],8918:[.0391,.5391,0,0,.77778],8919:[.0391,.5391,0,0,.77778],8920:[.03517,.54986,0,0,1.33334],8921:[.03517,.54986,0,0,1.33334],8922:[.38569,.88569,0,0,.77778],8923:[.38569,.88569,0,0,.77778],8926:[.13667,.63667,0,0,.77778],8927:[.13667,.63667,0,0,.77778],8928:[.30274,.79383,0,0,.77778],8929:[.30274,.79383,0,0,.77778],8934:[.23222,.74111,0,0,.77778],8935:[.23222,.74111,0,0,.77778],8936:[.23222,.74111,0,0,.77778],8937:[.23222,.74111,0,0,.77778],8938:[.20576,.70576,0,0,.77778],8939:[.20576,.70576,0,0,.77778],8940:[.30274,.79383,0,0,.77778],8941:[.30274,.79383,0,0,.77778],8994:[.19444,.69224,0,0,.77778],8995:[.19444,.69224,0,0,.77778],9416:[.15559,.69224,0,0,.90222],9484:[0,.69224,0,0,.5],9488:[0,.69224,0,0,.5],9492:[0,.37788,0,0,.5],9496:[0,.37788,0,0,.5],9585:[.19444,.68889,0,0,.88889],9586:[.19444,.74111,0,0,.88889],9632:[0,.675,0,0,.77778],9633:[0,.675,0,0,.77778],9650:[0,.54986,0,0,.72222],9651:[0,.54986,0,0,.72222],9654:[.03517,.54986,0,0,.77778],9660:[0,.54986,0,0,.72222],9661:[0,.54986,0,0,.72222],9664:[.03517,.54986,0,0,.77778],9674:[.11111,.69224,0,0,.66667],9733:[.19444,.69224,0,0,.94445],10003:[0,.69224,0,0,.83334],10016:[0,.69224,0,0,.83334],10731:[.11111,.69224,0,0,.66667],10846:[.19444,.75583,0,0,.61111],10877:[.13667,.63667,0,0,.77778],10878:[.13667,.63667,0,0,.77778],10885:[.25583,.75583,0,0,.77778],10886:[.25583,.75583,0,0,.77778],10887:[.13597,.63597,0,0,.77778],10888:[.13597,.63597,0,0,.77778],10889:[.26167,.75726,0,0,.77778],10890:[.26167,.75726,0,0,.77778],10891:[.48256,.98256,0,0,.77778],10892:[.48256,.98256,0,0,.77778],10901:[.13667,.63667,0,0,.77778],10902:[.13667,.63667,0,0,.77778],10933:[.25142,.75726,0,0,.77778],10934:[.25142,.75726,0,0,.77778],10935:[.26167,.75726,0,0,.77778],10936:[.26167,.75726,0,0,.77778],10937:[.26167,.75726,0,0,.77778],10938:[.26167,.75726,0,0,.77778],10949:[.25583,.75583,0,0,.77778],10950:[.25583,.75583,0,0,.77778],10955:[.28481,.79383,0,0,.77778],10956:[.28481,.79383,0,0,.77778],57350:[.08167,.58167,0,0,.22222],57351:[.08167,.58167,0,0,.38889],57352:[.08167,.58167,0,0,.77778],57353:[0,.43056,.04028,0,.66667],57356:[.25142,.75726,0,0,.77778],57357:[.25142,.75726,0,0,.77778],57358:[.41951,.91951,0,0,.77778],57359:[.30274,.79383,0,0,.77778],57360:[.30274,.79383,0,0,.77778],57361:[.41951,.91951,0,0,.77778],57366:[.25142,.75726,0,0,.77778],57367:[.25142,.75726,0,0,.77778],57368:[.25142,.75726,0,0,.77778],57369:[.25142,.75726,0,0,.77778],57370:[.13597,.63597,0,0,.77778],57371:[.13597,.63597,0,0,.77778]},"Caligraphic-Regular":{32:[0,0,0,0,.25],65:[0,.68333,0,.19445,.79847],66:[0,.68333,.03041,.13889,.65681],67:[0,.68333,.05834,.13889,.52653],68:[0,.68333,.02778,.08334,.77139],69:[0,.68333,.08944,.11111,.52778],70:[0,.68333,.09931,.11111,.71875],71:[.09722,.68333,.0593,.11111,.59487],72:[0,.68333,.00965,.11111,.84452],73:[0,.68333,.07382,0,.54452],74:[.09722,.68333,.18472,.16667,.67778],75:[0,.68333,.01445,.05556,.76195],76:[0,.68333,0,.13889,.68972],77:[0,.68333,0,.13889,1.2009],78:[0,.68333,.14736,.08334,.82049],79:[0,.68333,.02778,.11111,.79611],80:[0,.68333,.08222,.08334,.69556],81:[.09722,.68333,0,.11111,.81667],82:[0,.68333,0,.08334,.8475],83:[0,.68333,.075,.13889,.60556],84:[0,.68333,.25417,0,.54464],85:[0,.68333,.09931,.08334,.62583],86:[0,.68333,.08222,0,.61278],87:[0,.68333,.08222,.08334,.98778],88:[0,.68333,.14643,.13889,.7133],89:[.09722,.68333,.08222,.08334,.66834],90:[0,.68333,.07944,.13889,.72473],160:[0,0,0,0,.25]},"Fraktur-Regular":{32:[0,0,0,0,.25],33:[0,.69141,0,0,.29574],34:[0,.69141,0,0,.21471],38:[0,.69141,0,0,.73786],39:[0,.69141,0,0,.21201],40:[.24982,.74947,0,0,.38865],41:[.24982,.74947,0,0,.38865],42:[0,.62119,0,0,.27764],43:[.08319,.58283,0,0,.75623],44:[0,.10803,0,0,.27764],45:[.08319,.58283,0,0,.75623],46:[0,.10803,0,0,.27764],47:[.24982,.74947,0,0,.50181],48:[0,.47534,0,0,.50181],49:[0,.47534,0,0,.50181],50:[0,.47534,0,0,.50181],51:[.18906,.47534,0,0,.50181],52:[.18906,.47534,0,0,.50181],53:[.18906,.47534,0,0,.50181],54:[0,.69141,0,0,.50181],55:[.18906,.47534,0,0,.50181],56:[0,.69141,0,0,.50181],57:[.18906,.47534,0,0,.50181],58:[0,.47534,0,0,.21606],59:[.12604,.47534,0,0,.21606],61:[-.13099,.36866,0,0,.75623],63:[0,.69141,0,0,.36245],65:[0,.69141,0,0,.7176],66:[0,.69141,0,0,.88397],67:[0,.69141,0,0,.61254],68:[0,.69141,0,0,.83158],69:[0,.69141,0,0,.66278],70:[.12604,.69141,0,0,.61119],71:[0,.69141,0,0,.78539],72:[.06302,.69141,0,0,.7203],73:[0,.69141,0,0,.55448],74:[.12604,.69141,0,0,.55231],75:[0,.69141,0,0,.66845],76:[0,.69141,0,0,.66602],77:[0,.69141,0,0,1.04953],78:[0,.69141,0,0,.83212],79:[0,.69141,0,0,.82699],80:[.18906,.69141,0,0,.82753],81:[.03781,.69141,0,0,.82699],82:[0,.69141,0,0,.82807],83:[0,.69141,0,0,.82861],84:[0,.69141,0,0,.66899],85:[0,.69141,0,0,.64576],86:[0,.69141,0,0,.83131],87:[0,.69141,0,0,1.04602],88:[0,.69141,0,0,.71922],89:[.18906,.69141,0,0,.83293],90:[.12604,.69141,0,0,.60201],91:[.24982,.74947,0,0,.27764],93:[.24982,.74947,0,0,.27764],94:[0,.69141,0,0,.49965],97:[0,.47534,0,0,.50046],98:[0,.69141,0,0,.51315],99:[0,.47534,0,0,.38946],100:[0,.62119,0,0,.49857],101:[0,.47534,0,0,.40053],102:[.18906,.69141,0,0,.32626],103:[.18906,.47534,0,0,.5037],104:[.18906,.69141,0,0,.52126],105:[0,.69141,0,0,.27899],106:[0,.69141,0,0,.28088],107:[0,.69141,0,0,.38946],108:[0,.69141,0,0,.27953],109:[0,.47534,0,0,.76676],110:[0,.47534,0,0,.52666],111:[0,.47534,0,0,.48885],112:[.18906,.52396,0,0,.50046],113:[.18906,.47534,0,0,.48912],114:[0,.47534,0,0,.38919],115:[0,.47534,0,0,.44266],116:[0,.62119,0,0,.33301],117:[0,.47534,0,0,.5172],118:[0,.52396,0,0,.5118],119:[0,.52396,0,0,.77351],120:[.18906,.47534,0,0,.38865],121:[.18906,.47534,0,0,.49884],122:[.18906,.47534,0,0,.39054],160:[0,0,0,0,.25],8216:[0,.69141,0,0,.21471],8217:[0,.69141,0,0,.21471],58112:[0,.62119,0,0,.49749],58113:[0,.62119,0,0,.4983],58114:[.18906,.69141,0,0,.33328],58115:[.18906,.69141,0,0,.32923],58116:[.18906,.47534,0,0,.50343],58117:[0,.69141,0,0,.33301],58118:[0,.62119,0,0,.33409],58119:[0,.47534,0,0,.50073]},"Main-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.35],34:[0,.69444,0,0,.60278],35:[.19444,.69444,0,0,.95833],36:[.05556,.75,0,0,.575],37:[.05556,.75,0,0,.95833],38:[0,.69444,0,0,.89444],39:[0,.69444,0,0,.31944],40:[.25,.75,0,0,.44722],41:[.25,.75,0,0,.44722],42:[0,.75,0,0,.575],43:[.13333,.63333,0,0,.89444],44:[.19444,.15556,0,0,.31944],45:[0,.44444,0,0,.38333],46:[0,.15556,0,0,.31944],47:[.25,.75,0,0,.575],48:[0,.64444,0,0,.575],49:[0,.64444,0,0,.575],50:[0,.64444,0,0,.575],51:[0,.64444,0,0,.575],52:[0,.64444,0,0,.575],53:[0,.64444,0,0,.575],54:[0,.64444,0,0,.575],55:[0,.64444,0,0,.575],56:[0,.64444,0,0,.575],57:[0,.64444,0,0,.575],58:[0,.44444,0,0,.31944],59:[.19444,.44444,0,0,.31944],60:[.08556,.58556,0,0,.89444],61:[-.10889,.39111,0,0,.89444],62:[.08556,.58556,0,0,.89444],63:[0,.69444,0,0,.54305],64:[0,.69444,0,0,.89444],65:[0,.68611,0,0,.86944],66:[0,.68611,0,0,.81805],67:[0,.68611,0,0,.83055],68:[0,.68611,0,0,.88194],69:[0,.68611,0,0,.75555],70:[0,.68611,0,0,.72361],71:[0,.68611,0,0,.90416],72:[0,.68611,0,0,.9],73:[0,.68611,0,0,.43611],74:[0,.68611,0,0,.59444],75:[0,.68611,0,0,.90138],76:[0,.68611,0,0,.69166],77:[0,.68611,0,0,1.09166],78:[0,.68611,0,0,.9],79:[0,.68611,0,0,.86388],80:[0,.68611,0,0,.78611],81:[.19444,.68611,0,0,.86388],82:[0,.68611,0,0,.8625],83:[0,.68611,0,0,.63889],84:[0,.68611,0,0,.8],85:[0,.68611,0,0,.88472],86:[0,.68611,.01597,0,.86944],87:[0,.68611,.01597,0,1.18888],88:[0,.68611,0,0,.86944],89:[0,.68611,.02875,0,.86944],90:[0,.68611,0,0,.70277],91:[.25,.75,0,0,.31944],92:[.25,.75,0,0,.575],93:[.25,.75,0,0,.31944],94:[0,.69444,0,0,.575],95:[.31,.13444,.03194,0,.575],97:[0,.44444,0,0,.55902],98:[0,.69444,0,0,.63889],99:[0,.44444,0,0,.51111],100:[0,.69444,0,0,.63889],101:[0,.44444,0,0,.52708],102:[0,.69444,.10903,0,.35139],103:[.19444,.44444,.01597,0,.575],104:[0,.69444,0,0,.63889],105:[0,.69444,0,0,.31944],106:[.19444,.69444,0,0,.35139],107:[0,.69444,0,0,.60694],108:[0,.69444,0,0,.31944],109:[0,.44444,0,0,.95833],110:[0,.44444,0,0,.63889],111:[0,.44444,0,0,.575],112:[.19444,.44444,0,0,.63889],113:[.19444,.44444,0,0,.60694],114:[0,.44444,0,0,.47361],115:[0,.44444,0,0,.45361],116:[0,.63492,0,0,.44722],117:[0,.44444,0,0,.63889],118:[0,.44444,.01597,0,.60694],119:[0,.44444,.01597,0,.83055],120:[0,.44444,0,0,.60694],121:[.19444,.44444,.01597,0,.60694],122:[0,.44444,0,0,.51111],123:[.25,.75,0,0,.575],124:[.25,.75,0,0,.31944],125:[.25,.75,0,0,.575],126:[.35,.34444,0,0,.575],160:[0,0,0,0,.25],163:[0,.69444,0,0,.86853],168:[0,.69444,0,0,.575],172:[0,.44444,0,0,.76666],176:[0,.69444,0,0,.86944],177:[.13333,.63333,0,0,.89444],184:[.17014,0,0,0,.51111],198:[0,.68611,0,0,1.04166],215:[.13333,.63333,0,0,.89444],216:[.04861,.73472,0,0,.89444],223:[0,.69444,0,0,.59722],230:[0,.44444,0,0,.83055],247:[.13333,.63333,0,0,.89444],248:[.09722,.54167,0,0,.575],305:[0,.44444,0,0,.31944],338:[0,.68611,0,0,1.16944],339:[0,.44444,0,0,.89444],567:[.19444,.44444,0,0,.35139],710:[0,.69444,0,0,.575],711:[0,.63194,0,0,.575],713:[0,.59611,0,0,.575],714:[0,.69444,0,0,.575],715:[0,.69444,0,0,.575],728:[0,.69444,0,0,.575],729:[0,.69444,0,0,.31944],730:[0,.69444,0,0,.86944],732:[0,.69444,0,0,.575],733:[0,.69444,0,0,.575],915:[0,.68611,0,0,.69166],916:[0,.68611,0,0,.95833],920:[0,.68611,0,0,.89444],923:[0,.68611,0,0,.80555],926:[0,.68611,0,0,.76666],928:[0,.68611,0,0,.9],931:[0,.68611,0,0,.83055],933:[0,.68611,0,0,.89444],934:[0,.68611,0,0,.83055],936:[0,.68611,0,0,.89444],937:[0,.68611,0,0,.83055],8211:[0,.44444,.03194,0,.575],8212:[0,.44444,.03194,0,1.14999],8216:[0,.69444,0,0,.31944],8217:[0,.69444,0,0,.31944],8220:[0,.69444,0,0,.60278],8221:[0,.69444,0,0,.60278],8224:[.19444,.69444,0,0,.51111],8225:[.19444,.69444,0,0,.51111],8242:[0,.55556,0,0,.34444],8407:[0,.72444,.15486,0,.575],8463:[0,.69444,0,0,.66759],8465:[0,.69444,0,0,.83055],8467:[0,.69444,0,0,.47361],8472:[.19444,.44444,0,0,.74027],8476:[0,.69444,0,0,.83055],8501:[0,.69444,0,0,.70277],8592:[-.10889,.39111,0,0,1.14999],8593:[.19444,.69444,0,0,.575],8594:[-.10889,.39111,0,0,1.14999],8595:[.19444,.69444,0,0,.575],8596:[-.10889,.39111,0,0,1.14999],8597:[.25,.75,0,0,.575],8598:[.19444,.69444,0,0,1.14999],8599:[.19444,.69444,0,0,1.14999],8600:[.19444,.69444,0,0,1.14999],8601:[.19444,.69444,0,0,1.14999],8636:[-.10889,.39111,0,0,1.14999],8637:[-.10889,.39111,0,0,1.14999],8640:[-.10889,.39111,0,0,1.14999],8641:[-.10889,.39111,0,0,1.14999],8656:[-.10889,.39111,0,0,1.14999],8657:[.19444,.69444,0,0,.70277],8658:[-.10889,.39111,0,0,1.14999],8659:[.19444,.69444,0,0,.70277],8660:[-.10889,.39111,0,0,1.14999],8661:[.25,.75,0,0,.70277],8704:[0,.69444,0,0,.63889],8706:[0,.69444,.06389,0,.62847],8707:[0,.69444,0,0,.63889],8709:[.05556,.75,0,0,.575],8711:[0,.68611,0,0,.95833],8712:[.08556,.58556,0,0,.76666],8715:[.08556,.58556,0,0,.76666],8722:[.13333,.63333,0,0,.89444],8723:[.13333,.63333,0,0,.89444],8725:[.25,.75,0,0,.575],8726:[.25,.75,0,0,.575],8727:[-.02778,.47222,0,0,.575],8728:[-.02639,.47361,0,0,.575],8729:[-.02639,.47361,0,0,.575],8730:[.18,.82,0,0,.95833],8733:[0,.44444,0,0,.89444],8734:[0,.44444,0,0,1.14999],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.31944],8741:[.25,.75,0,0,.575],8743:[0,.55556,0,0,.76666],8744:[0,.55556,0,0,.76666],8745:[0,.55556,0,0,.76666],8746:[0,.55556,0,0,.76666],8747:[.19444,.69444,.12778,0,.56875],8764:[-.10889,.39111,0,0,.89444],8768:[.19444,.69444,0,0,.31944],8771:[.00222,.50222,0,0,.89444],8773:[.027,.638,0,0,.894],8776:[.02444,.52444,0,0,.89444],8781:[.00222,.50222,0,0,.89444],8801:[.00222,.50222,0,0,.89444],8804:[.19667,.69667,0,0,.89444],8805:[.19667,.69667,0,0,.89444],8810:[.08556,.58556,0,0,1.14999],8811:[.08556,.58556,0,0,1.14999],8826:[.08556,.58556,0,0,.89444],8827:[.08556,.58556,0,0,.89444],8834:[.08556,.58556,0,0,.89444],8835:[.08556,.58556,0,0,.89444],8838:[.19667,.69667,0,0,.89444],8839:[.19667,.69667,0,0,.89444],8846:[0,.55556,0,0,.76666],8849:[.19667,.69667,0,0,.89444],8850:[.19667,.69667,0,0,.89444],8851:[0,.55556,0,0,.76666],8852:[0,.55556,0,0,.76666],8853:[.13333,.63333,0,0,.89444],8854:[.13333,.63333,0,0,.89444],8855:[.13333,.63333,0,0,.89444],8856:[.13333,.63333,0,0,.89444],8857:[.13333,.63333,0,0,.89444],8866:[0,.69444,0,0,.70277],8867:[0,.69444,0,0,.70277],8868:[0,.69444,0,0,.89444],8869:[0,.69444,0,0,.89444],8900:[-.02639,.47361,0,0,.575],8901:[-.02639,.47361,0,0,.31944],8902:[-.02778,.47222,0,0,.575],8968:[.25,.75,0,0,.51111],8969:[.25,.75,0,0,.51111],8970:[.25,.75,0,0,.51111],8971:[.25,.75,0,0,.51111],8994:[-.13889,.36111,0,0,1.14999],8995:[-.13889,.36111,0,0,1.14999],9651:[.19444,.69444,0,0,1.02222],9657:[-.02778,.47222,0,0,.575],9661:[.19444,.69444,0,0,1.02222],9667:[-.02778,.47222,0,0,.575],9711:[.19444,.69444,0,0,1.14999],9824:[.12963,.69444,0,0,.89444],9825:[.12963,.69444,0,0,.89444],9826:[.12963,.69444,0,0,.89444],9827:[.12963,.69444,0,0,.89444],9837:[0,.75,0,0,.44722],9838:[.19444,.69444,0,0,.44722],9839:[.19444,.69444,0,0,.44722],10216:[.25,.75,0,0,.44722],10217:[.25,.75,0,0,.44722],10815:[0,.68611,0,0,.9],10927:[.19667,.69667,0,0,.89444],10928:[.19667,.69667,0,0,.89444],57376:[.19444,.69444,0,0,0]},"Main-BoldItalic":{32:[0,0,0,0,.25],33:[0,.69444,.11417,0,.38611],34:[0,.69444,.07939,0,.62055],35:[.19444,.69444,.06833,0,.94444],37:[.05556,.75,.12861,0,.94444],38:[0,.69444,.08528,0,.88555],39:[0,.69444,.12945,0,.35555],40:[.25,.75,.15806,0,.47333],41:[.25,.75,.03306,0,.47333],42:[0,.75,.14333,0,.59111],43:[.10333,.60333,.03306,0,.88555],44:[.19444,.14722,0,0,.35555],45:[0,.44444,.02611,0,.41444],46:[0,.14722,0,0,.35555],47:[.25,.75,.15806,0,.59111],48:[0,.64444,.13167,0,.59111],49:[0,.64444,.13167,0,.59111],50:[0,.64444,.13167,0,.59111],51:[0,.64444,.13167,0,.59111],52:[.19444,.64444,.13167,0,.59111],53:[0,.64444,.13167,0,.59111],54:[0,.64444,.13167,0,.59111],55:[.19444,.64444,.13167,0,.59111],56:[0,.64444,.13167,0,.59111],57:[0,.64444,.13167,0,.59111],58:[0,.44444,.06695,0,.35555],59:[.19444,.44444,.06695,0,.35555],61:[-.10889,.39111,.06833,0,.88555],63:[0,.69444,.11472,0,.59111],64:[0,.69444,.09208,0,.88555],65:[0,.68611,0,0,.86555],66:[0,.68611,.0992,0,.81666],67:[0,.68611,.14208,0,.82666],68:[0,.68611,.09062,0,.87555],69:[0,.68611,.11431,0,.75666],70:[0,.68611,.12903,0,.72722],71:[0,.68611,.07347,0,.89527],72:[0,.68611,.17208,0,.8961],73:[0,.68611,.15681,0,.47166],74:[0,.68611,.145,0,.61055],75:[0,.68611,.14208,0,.89499],76:[0,.68611,0,0,.69777],77:[0,.68611,.17208,0,1.07277],78:[0,.68611,.17208,0,.8961],79:[0,.68611,.09062,0,.85499],80:[0,.68611,.0992,0,.78721],81:[.19444,.68611,.09062,0,.85499],82:[0,.68611,.02559,0,.85944],83:[0,.68611,.11264,0,.64999],84:[0,.68611,.12903,0,.7961],85:[0,.68611,.17208,0,.88083],86:[0,.68611,.18625,0,.86555],87:[0,.68611,.18625,0,1.15999],88:[0,.68611,.15681,0,.86555],89:[0,.68611,.19803,0,.86555],90:[0,.68611,.14208,0,.70888],91:[.25,.75,.1875,0,.35611],93:[.25,.75,.09972,0,.35611],94:[0,.69444,.06709,0,.59111],95:[.31,.13444,.09811,0,.59111],97:[0,.44444,.09426,0,.59111],98:[0,.69444,.07861,0,.53222],99:[0,.44444,.05222,0,.53222],100:[0,.69444,.10861,0,.59111],101:[0,.44444,.085,0,.53222],102:[.19444,.69444,.21778,0,.4],103:[.19444,.44444,.105,0,.53222],104:[0,.69444,.09426,0,.59111],105:[0,.69326,.11387,0,.35555],106:[.19444,.69326,.1672,0,.35555],107:[0,.69444,.11111,0,.53222],108:[0,.69444,.10861,0,.29666],109:[0,.44444,.09426,0,.94444],110:[0,.44444,.09426,0,.64999],111:[0,.44444,.07861,0,.59111],112:[.19444,.44444,.07861,0,.59111],113:[.19444,.44444,.105,0,.53222],114:[0,.44444,.11111,0,.50167],115:[0,.44444,.08167,0,.48694],116:[0,.63492,.09639,0,.385],117:[0,.44444,.09426,0,.62055],118:[0,.44444,.11111,0,.53222],119:[0,.44444,.11111,0,.76777],120:[0,.44444,.12583,0,.56055],121:[.19444,.44444,.105,0,.56166],122:[0,.44444,.13889,0,.49055],126:[.35,.34444,.11472,0,.59111],160:[0,0,0,0,.25],168:[0,.69444,.11473,0,.59111],176:[0,.69444,0,0,.94888],184:[.17014,0,0,0,.53222],198:[0,.68611,.11431,0,1.02277],216:[.04861,.73472,.09062,0,.88555],223:[.19444,.69444,.09736,0,.665],230:[0,.44444,.085,0,.82666],248:[.09722,.54167,.09458,0,.59111],305:[0,.44444,.09426,0,.35555],338:[0,.68611,.11431,0,1.14054],339:[0,.44444,.085,0,.82666],567:[.19444,.44444,.04611,0,.385],710:[0,.69444,.06709,0,.59111],711:[0,.63194,.08271,0,.59111],713:[0,.59444,.10444,0,.59111],714:[0,.69444,.08528,0,.59111],715:[0,.69444,0,0,.59111],728:[0,.69444,.10333,0,.59111],729:[0,.69444,.12945,0,.35555],730:[0,.69444,0,0,.94888],732:[0,.69444,.11472,0,.59111],733:[0,.69444,.11472,0,.59111],915:[0,.68611,.12903,0,.69777],916:[0,.68611,0,0,.94444],920:[0,.68611,.09062,0,.88555],923:[0,.68611,0,0,.80666],926:[0,.68611,.15092,0,.76777],928:[0,.68611,.17208,0,.8961],931:[0,.68611,.11431,0,.82666],933:[0,.68611,.10778,0,.88555],934:[0,.68611,.05632,0,.82666],936:[0,.68611,.10778,0,.88555],937:[0,.68611,.0992,0,.82666],8211:[0,.44444,.09811,0,.59111],8212:[0,.44444,.09811,0,1.18221],8216:[0,.69444,.12945,0,.35555],8217:[0,.69444,.12945,0,.35555],8220:[0,.69444,.16772,0,.62055],8221:[0,.69444,.07939,0,.62055]},"Main-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.12417,0,.30667],34:[0,.69444,.06961,0,.51444],35:[.19444,.69444,.06616,0,.81777],37:[.05556,.75,.13639,0,.81777],38:[0,.69444,.09694,0,.76666],39:[0,.69444,.12417,0,.30667],40:[.25,.75,.16194,0,.40889],41:[.25,.75,.03694,0,.40889],42:[0,.75,.14917,0,.51111],43:[.05667,.56167,.03694,0,.76666],44:[.19444,.10556,0,0,.30667],45:[0,.43056,.02826,0,.35778],46:[0,.10556,0,0,.30667],47:[.25,.75,.16194,0,.51111],48:[0,.64444,.13556,0,.51111],49:[0,.64444,.13556,0,.51111],50:[0,.64444,.13556,0,.51111],51:[0,.64444,.13556,0,.51111],52:[.19444,.64444,.13556,0,.51111],53:[0,.64444,.13556,0,.51111],54:[0,.64444,.13556,0,.51111],55:[.19444,.64444,.13556,0,.51111],56:[0,.64444,.13556,0,.51111],57:[0,.64444,.13556,0,.51111],58:[0,.43056,.0582,0,.30667],59:[.19444,.43056,.0582,0,.30667],61:[-.13313,.36687,.06616,0,.76666],63:[0,.69444,.1225,0,.51111],64:[0,.69444,.09597,0,.76666],65:[0,.68333,0,0,.74333],66:[0,.68333,.10257,0,.70389],67:[0,.68333,.14528,0,.71555],68:[0,.68333,.09403,0,.755],69:[0,.68333,.12028,0,.67833],70:[0,.68333,.13305,0,.65277],71:[0,.68333,.08722,0,.77361],72:[0,.68333,.16389,0,.74333],73:[0,.68333,.15806,0,.38555],74:[0,.68333,.14028,0,.525],75:[0,.68333,.14528,0,.76888],76:[0,.68333,0,0,.62722],77:[0,.68333,.16389,0,.89666],78:[0,.68333,.16389,0,.74333],79:[0,.68333,.09403,0,.76666],80:[0,.68333,.10257,0,.67833],81:[.19444,.68333,.09403,0,.76666],82:[0,.68333,.03868,0,.72944],83:[0,.68333,.11972,0,.56222],84:[0,.68333,.13305,0,.71555],85:[0,.68333,.16389,0,.74333],86:[0,.68333,.18361,0,.74333],87:[0,.68333,.18361,0,.99888],88:[0,.68333,.15806,0,.74333],89:[0,.68333,.19383,0,.74333],90:[0,.68333,.14528,0,.61333],91:[.25,.75,.1875,0,.30667],93:[.25,.75,.10528,0,.30667],94:[0,.69444,.06646,0,.51111],95:[.31,.12056,.09208,0,.51111],97:[0,.43056,.07671,0,.51111],98:[0,.69444,.06312,0,.46],99:[0,.43056,.05653,0,.46],100:[0,.69444,.10333,0,.51111],101:[0,.43056,.07514,0,.46],102:[.19444,.69444,.21194,0,.30667],103:[.19444,.43056,.08847,0,.46],104:[0,.69444,.07671,0,.51111],105:[0,.65536,.1019,0,.30667],106:[.19444,.65536,.14467,0,.30667],107:[0,.69444,.10764,0,.46],108:[0,.69444,.10333,0,.25555],109:[0,.43056,.07671,0,.81777],110:[0,.43056,.07671,0,.56222],111:[0,.43056,.06312,0,.51111],112:[.19444,.43056,.06312,0,.51111],113:[.19444,.43056,.08847,0,.46],114:[0,.43056,.10764,0,.42166],115:[0,.43056,.08208,0,.40889],116:[0,.61508,.09486,0,.33222],117:[0,.43056,.07671,0,.53666],118:[0,.43056,.10764,0,.46],119:[0,.43056,.10764,0,.66444],120:[0,.43056,.12042,0,.46389],121:[.19444,.43056,.08847,0,.48555],122:[0,.43056,.12292,0,.40889],126:[.35,.31786,.11585,0,.51111],160:[0,0,0,0,.25],168:[0,.66786,.10474,0,.51111],176:[0,.69444,0,0,.83129],184:[.17014,0,0,0,.46],198:[0,.68333,.12028,0,.88277],216:[.04861,.73194,.09403,0,.76666],223:[.19444,.69444,.10514,0,.53666],230:[0,.43056,.07514,0,.71555],248:[.09722,.52778,.09194,0,.51111],338:[0,.68333,.12028,0,.98499],339:[0,.43056,.07514,0,.71555],710:[0,.69444,.06646,0,.51111],711:[0,.62847,.08295,0,.51111],713:[0,.56167,.10333,0,.51111],714:[0,.69444,.09694,0,.51111],715:[0,.69444,0,0,.51111],728:[0,.69444,.10806,0,.51111],729:[0,.66786,.11752,0,.30667],730:[0,.69444,0,0,.83129],732:[0,.66786,.11585,0,.51111],733:[0,.69444,.1225,0,.51111],915:[0,.68333,.13305,0,.62722],916:[0,.68333,0,0,.81777],920:[0,.68333,.09403,0,.76666],923:[0,.68333,0,0,.69222],926:[0,.68333,.15294,0,.66444],928:[0,.68333,.16389,0,.74333],931:[0,.68333,.12028,0,.71555],933:[0,.68333,.11111,0,.76666],934:[0,.68333,.05986,0,.71555],936:[0,.68333,.11111,0,.76666],937:[0,.68333,.10257,0,.71555],8211:[0,.43056,.09208,0,.51111],8212:[0,.43056,.09208,0,1.02222],8216:[0,.69444,.12417,0,.30667],8217:[0,.69444,.12417,0,.30667],8220:[0,.69444,.1685,0,.51444],8221:[0,.69444,.06961,0,.51444],8463:[0,.68889,0,0,.54028]},"Main-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.27778],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.77778],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.19444,.10556,0,0,.27778],45:[0,.43056,0,0,.33333],46:[0,.10556,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.64444,0,0,.5],49:[0,.64444,0,0,.5],50:[0,.64444,0,0,.5],51:[0,.64444,0,0,.5],52:[0,.64444,0,0,.5],53:[0,.64444,0,0,.5],54:[0,.64444,0,0,.5],55:[0,.64444,0,0,.5],56:[0,.64444,0,0,.5],57:[0,.64444,0,0,.5],58:[0,.43056,0,0,.27778],59:[.19444,.43056,0,0,.27778],60:[.0391,.5391,0,0,.77778],61:[-.13313,.36687,0,0,.77778],62:[.0391,.5391,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.77778],65:[0,.68333,0,0,.75],66:[0,.68333,0,0,.70834],67:[0,.68333,0,0,.72222],68:[0,.68333,0,0,.76389],69:[0,.68333,0,0,.68056],70:[0,.68333,0,0,.65278],71:[0,.68333,0,0,.78472],72:[0,.68333,0,0,.75],73:[0,.68333,0,0,.36111],74:[0,.68333,0,0,.51389],75:[0,.68333,0,0,.77778],76:[0,.68333,0,0,.625],77:[0,.68333,0,0,.91667],78:[0,.68333,0,0,.75],79:[0,.68333,0,0,.77778],80:[0,.68333,0,0,.68056],81:[.19444,.68333,0,0,.77778],82:[0,.68333,0,0,.73611],83:[0,.68333,0,0,.55556],84:[0,.68333,0,0,.72222],85:[0,.68333,0,0,.75],86:[0,.68333,.01389,0,.75],87:[0,.68333,.01389,0,1.02778],88:[0,.68333,0,0,.75],89:[0,.68333,.025,0,.75],90:[0,.68333,0,0,.61111],91:[.25,.75,0,0,.27778],92:[.25,.75,0,0,.5],93:[.25,.75,0,0,.27778],94:[0,.69444,0,0,.5],95:[.31,.12056,.02778,0,.5],97:[0,.43056,0,0,.5],98:[0,.69444,0,0,.55556],99:[0,.43056,0,0,.44445],100:[0,.69444,0,0,.55556],101:[0,.43056,0,0,.44445],102:[0,.69444,.07778,0,.30556],103:[.19444,.43056,.01389,0,.5],104:[0,.69444,0,0,.55556],105:[0,.66786,0,0,.27778],106:[.19444,.66786,0,0,.30556],107:[0,.69444,0,0,.52778],108:[0,.69444,0,0,.27778],109:[0,.43056,0,0,.83334],110:[0,.43056,0,0,.55556],111:[0,.43056,0,0,.5],112:[.19444,.43056,0,0,.55556],113:[.19444,.43056,0,0,.52778],114:[0,.43056,0,0,.39167],115:[0,.43056,0,0,.39445],116:[0,.61508,0,0,.38889],117:[0,.43056,0,0,.55556],118:[0,.43056,.01389,0,.52778],119:[0,.43056,.01389,0,.72222],120:[0,.43056,0,0,.52778],121:[.19444,.43056,.01389,0,.52778],122:[0,.43056,0,0,.44445],123:[.25,.75,0,0,.5],124:[.25,.75,0,0,.27778],125:[.25,.75,0,0,.5],126:[.35,.31786,0,0,.5],160:[0,0,0,0,.25],163:[0,.69444,0,0,.76909],167:[.19444,.69444,0,0,.44445],168:[0,.66786,0,0,.5],172:[0,.43056,0,0,.66667],176:[0,.69444,0,0,.75],177:[.08333,.58333,0,0,.77778],182:[.19444,.69444,0,0,.61111],184:[.17014,0,0,0,.44445],198:[0,.68333,0,0,.90278],215:[.08333,.58333,0,0,.77778],216:[.04861,.73194,0,0,.77778],223:[0,.69444,0,0,.5],230:[0,.43056,0,0,.72222],247:[.08333,.58333,0,0,.77778],248:[.09722,.52778,0,0,.5],305:[0,.43056,0,0,.27778],338:[0,.68333,0,0,1.01389],339:[0,.43056,0,0,.77778],567:[.19444,.43056,0,0,.30556],710:[0,.69444,0,0,.5],711:[0,.62847,0,0,.5],713:[0,.56778,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.66786,0,0,.27778],730:[0,.69444,0,0,.75],732:[0,.66786,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.68333,0,0,.625],916:[0,.68333,0,0,.83334],920:[0,.68333,0,0,.77778],923:[0,.68333,0,0,.69445],926:[0,.68333,0,0,.66667],928:[0,.68333,0,0,.75],931:[0,.68333,0,0,.72222],933:[0,.68333,0,0,.77778],934:[0,.68333,0,0,.72222],936:[0,.68333,0,0,.77778],937:[0,.68333,0,0,.72222],8211:[0,.43056,.02778,0,.5],8212:[0,.43056,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5],8224:[.19444,.69444,0,0,.44445],8225:[.19444,.69444,0,0,.44445],8230:[0,.123,0,0,1.172],8242:[0,.55556,0,0,.275],8407:[0,.71444,.15382,0,.5],8463:[0,.68889,0,0,.54028],8465:[0,.69444,0,0,.72222],8467:[0,.69444,0,.11111,.41667],8472:[.19444,.43056,0,.11111,.63646],8476:[0,.69444,0,0,.72222],8501:[0,.69444,0,0,.61111],8592:[-.13313,.36687,0,0,1],8593:[.19444,.69444,0,0,.5],8594:[-.13313,.36687,0,0,1],8595:[.19444,.69444,0,0,.5],8596:[-.13313,.36687,0,0,1],8597:[.25,.75,0,0,.5],8598:[.19444,.69444,0,0,1],8599:[.19444,.69444,0,0,1],8600:[.19444,.69444,0,0,1],8601:[.19444,.69444,0,0,1],8614:[.011,.511,0,0,1],8617:[.011,.511,0,0,1.126],8618:[.011,.511,0,0,1.126],8636:[-.13313,.36687,0,0,1],8637:[-.13313,.36687,0,0,1],8640:[-.13313,.36687,0,0,1],8641:[-.13313,.36687,0,0,1],8652:[.011,.671,0,0,1],8656:[-.13313,.36687,0,0,1],8657:[.19444,.69444,0,0,.61111],8658:[-.13313,.36687,0,0,1],8659:[.19444,.69444,0,0,.61111],8660:[-.13313,.36687,0,0,1],8661:[.25,.75,0,0,.61111],8704:[0,.69444,0,0,.55556],8706:[0,.69444,.05556,.08334,.5309],8707:[0,.69444,0,0,.55556],8709:[.05556,.75,0,0,.5],8711:[0,.68333,0,0,.83334],8712:[.0391,.5391,0,0,.66667],8715:[.0391,.5391,0,0,.66667],8722:[.08333,.58333,0,0,.77778],8723:[.08333,.58333,0,0,.77778],8725:[.25,.75,0,0,.5],8726:[.25,.75,0,0,.5],8727:[-.03472,.46528,0,0,.5],8728:[-.05555,.44445,0,0,.5],8729:[-.05555,.44445,0,0,.5],8730:[.2,.8,0,0,.83334],8733:[0,.43056,0,0,.77778],8734:[0,.43056,0,0,1],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.27778],8741:[.25,.75,0,0,.5],8743:[0,.55556,0,0,.66667],8744:[0,.55556,0,0,.66667],8745:[0,.55556,0,0,.66667],8746:[0,.55556,0,0,.66667],8747:[.19444,.69444,.11111,0,.41667],8764:[-.13313,.36687,0,0,.77778],8768:[.19444,.69444,0,0,.27778],8771:[-.03625,.46375,0,0,.77778],8773:[-.022,.589,0,0,.778],8776:[-.01688,.48312,0,0,.77778],8781:[-.03625,.46375,0,0,.77778],8784:[-.133,.673,0,0,.778],8801:[-.03625,.46375,0,0,.77778],8804:[.13597,.63597,0,0,.77778],8805:[.13597,.63597,0,0,.77778],8810:[.0391,.5391,0,0,1],8811:[.0391,.5391,0,0,1],8826:[.0391,.5391,0,0,.77778],8827:[.0391,.5391,0,0,.77778],8834:[.0391,.5391,0,0,.77778],8835:[.0391,.5391,0,0,.77778],8838:[.13597,.63597,0,0,.77778],8839:[.13597,.63597,0,0,.77778],8846:[0,.55556,0,0,.66667],8849:[.13597,.63597,0,0,.77778],8850:[.13597,.63597,0,0,.77778],8851:[0,.55556,0,0,.66667],8852:[0,.55556,0,0,.66667],8853:[.08333,.58333,0,0,.77778],8854:[.08333,.58333,0,0,.77778],8855:[.08333,.58333,0,0,.77778],8856:[.08333,.58333,0,0,.77778],8857:[.08333,.58333,0,0,.77778],8866:[0,.69444,0,0,.61111],8867:[0,.69444,0,0,.61111],8868:[0,.69444,0,0,.77778],8869:[0,.69444,0,0,.77778],8872:[.249,.75,0,0,.867],8900:[-.05555,.44445,0,0,.5],8901:[-.05555,.44445,0,0,.27778],8902:[-.03472,.46528,0,0,.5],8904:[.005,.505,0,0,.9],8942:[.03,.903,0,0,.278],8943:[-.19,.313,0,0,1.172],8945:[-.1,.823,0,0,1.282],8968:[.25,.75,0,0,.44445],8969:[.25,.75,0,0,.44445],8970:[.25,.75,0,0,.44445],8971:[.25,.75,0,0,.44445],8994:[-.14236,.35764,0,0,1],8995:[-.14236,.35764,0,0,1],9136:[.244,.744,0,0,.412],9137:[.244,.745,0,0,.412],9651:[.19444,.69444,0,0,.88889],9657:[-.03472,.46528,0,0,.5],9661:[.19444,.69444,0,0,.88889],9667:[-.03472,.46528,0,0,.5],9711:[.19444,.69444,0,0,1],9824:[.12963,.69444,0,0,.77778],9825:[.12963,.69444,0,0,.77778],9826:[.12963,.69444,0,0,.77778],9827:[.12963,.69444,0,0,.77778],9837:[0,.75,0,0,.38889],9838:[.19444,.69444,0,0,.38889],9839:[.19444,.69444,0,0,.38889],10216:[.25,.75,0,0,.38889],10217:[.25,.75,0,0,.38889],10222:[.244,.744,0,0,.412],10223:[.244,.745,0,0,.412],10229:[.011,.511,0,0,1.609],10230:[.011,.511,0,0,1.638],10231:[.011,.511,0,0,1.859],10232:[.024,.525,0,0,1.609],10233:[.024,.525,0,0,1.638],10234:[.024,.525,0,0,1.858],10236:[.011,.511,0,0,1.638],10815:[0,.68333,0,0,.75],10927:[.13597,.63597,0,0,.77778],10928:[.13597,.63597,0,0,.77778],57376:[.19444,.69444,0,0,0]},"Math-BoldItalic":{32:[0,0,0,0,.25],48:[0,.44444,0,0,.575],49:[0,.44444,0,0,.575],50:[0,.44444,0,0,.575],51:[.19444,.44444,0,0,.575],52:[.19444,.44444,0,0,.575],53:[.19444,.44444,0,0,.575],54:[0,.64444,0,0,.575],55:[.19444,.44444,0,0,.575],56:[0,.64444,0,0,.575],57:[.19444,.44444,0,0,.575],65:[0,.68611,0,0,.86944],66:[0,.68611,.04835,0,.8664],67:[0,.68611,.06979,0,.81694],68:[0,.68611,.03194,0,.93812],69:[0,.68611,.05451,0,.81007],70:[0,.68611,.15972,0,.68889],71:[0,.68611,0,0,.88673],72:[0,.68611,.08229,0,.98229],73:[0,.68611,.07778,0,.51111],74:[0,.68611,.10069,0,.63125],75:[0,.68611,.06979,0,.97118],76:[0,.68611,0,0,.75555],77:[0,.68611,.11424,0,1.14201],78:[0,.68611,.11424,0,.95034],79:[0,.68611,.03194,0,.83666],80:[0,.68611,.15972,0,.72309],81:[.19444,.68611,0,0,.86861],82:[0,.68611,.00421,0,.87235],83:[0,.68611,.05382,0,.69271],84:[0,.68611,.15972,0,.63663],85:[0,.68611,.11424,0,.80027],86:[0,.68611,.25555,0,.67778],87:[0,.68611,.15972,0,1.09305],88:[0,.68611,.07778,0,.94722],89:[0,.68611,.25555,0,.67458],90:[0,.68611,.06979,0,.77257],97:[0,.44444,0,0,.63287],98:[0,.69444,0,0,.52083],99:[0,.44444,0,0,.51342],100:[0,.69444,0,0,.60972],101:[0,.44444,0,0,.55361],102:[.19444,.69444,.11042,0,.56806],103:[.19444,.44444,.03704,0,.5449],104:[0,.69444,0,0,.66759],105:[0,.69326,0,0,.4048],106:[.19444,.69326,.0622,0,.47083],107:[0,.69444,.01852,0,.6037],108:[0,.69444,.0088,0,.34815],109:[0,.44444,0,0,1.0324],110:[0,.44444,0,0,.71296],111:[0,.44444,0,0,.58472],112:[.19444,.44444,0,0,.60092],113:[.19444,.44444,.03704,0,.54213],114:[0,.44444,.03194,0,.5287],115:[0,.44444,0,0,.53125],116:[0,.63492,0,0,.41528],117:[0,.44444,0,0,.68102],118:[0,.44444,.03704,0,.56666],119:[0,.44444,.02778,0,.83148],120:[0,.44444,0,0,.65903],121:[.19444,.44444,.03704,0,.59028],122:[0,.44444,.04213,0,.55509],160:[0,0,0,0,.25],915:[0,.68611,.15972,0,.65694],916:[0,.68611,0,0,.95833],920:[0,.68611,.03194,0,.86722],923:[0,.68611,0,0,.80555],926:[0,.68611,.07458,0,.84125],928:[0,.68611,.08229,0,.98229],931:[0,.68611,.05451,0,.88507],933:[0,.68611,.15972,0,.67083],934:[0,.68611,0,0,.76666],936:[0,.68611,.11653,0,.71402],937:[0,.68611,.04835,0,.8789],945:[0,.44444,0,0,.76064],946:[.19444,.69444,.03403,0,.65972],947:[.19444,.44444,.06389,0,.59003],948:[0,.69444,.03819,0,.52222],949:[0,.44444,0,0,.52882],950:[.19444,.69444,.06215,0,.50833],951:[.19444,.44444,.03704,0,.6],952:[0,.69444,.03194,0,.5618],953:[0,.44444,0,0,.41204],954:[0,.44444,0,0,.66759],955:[0,.69444,0,0,.67083],956:[.19444,.44444,0,0,.70787],957:[0,.44444,.06898,0,.57685],958:[.19444,.69444,.03021,0,.50833],959:[0,.44444,0,0,.58472],960:[0,.44444,.03704,0,.68241],961:[.19444,.44444,0,0,.6118],962:[.09722,.44444,.07917,0,.42361],963:[0,.44444,.03704,0,.68588],964:[0,.44444,.13472,0,.52083],965:[0,.44444,.03704,0,.63055],966:[.19444,.44444,0,0,.74722],967:[.19444,.44444,0,0,.71805],968:[.19444,.69444,.03704,0,.75833],969:[0,.44444,.03704,0,.71782],977:[0,.69444,0,0,.69155],981:[.19444,.69444,0,0,.7125],982:[0,.44444,.03194,0,.975],1009:[.19444,.44444,0,0,.6118],1013:[0,.44444,0,0,.48333],57649:[0,.44444,0,0,.39352],57911:[.19444,.44444,0,0,.43889]},"Math-Italic":{32:[0,0,0,0,.25],48:[0,.43056,0,0,.5],49:[0,.43056,0,0,.5],50:[0,.43056,0,0,.5],51:[.19444,.43056,0,0,.5],52:[.19444,.43056,0,0,.5],53:[.19444,.43056,0,0,.5],54:[0,.64444,0,0,.5],55:[.19444,.43056,0,0,.5],56:[0,.64444,0,0,.5],57:[.19444,.43056,0,0,.5],65:[0,.68333,0,.13889,.75],66:[0,.68333,.05017,.08334,.75851],67:[0,.68333,.07153,.08334,.71472],68:[0,.68333,.02778,.05556,.82792],69:[0,.68333,.05764,.08334,.7382],70:[0,.68333,.13889,.08334,.64306],71:[0,.68333,0,.08334,.78625],72:[0,.68333,.08125,.05556,.83125],73:[0,.68333,.07847,.11111,.43958],74:[0,.68333,.09618,.16667,.55451],75:[0,.68333,.07153,.05556,.84931],76:[0,.68333,0,.02778,.68056],77:[0,.68333,.10903,.08334,.97014],78:[0,.68333,.10903,.08334,.80347],79:[0,.68333,.02778,.08334,.76278],80:[0,.68333,.13889,.08334,.64201],81:[.19444,.68333,0,.08334,.79056],82:[0,.68333,.00773,.08334,.75929],83:[0,.68333,.05764,.08334,.6132],84:[0,.68333,.13889,.08334,.58438],85:[0,.68333,.10903,.02778,.68278],86:[0,.68333,.22222,0,.58333],87:[0,.68333,.13889,0,.94445],88:[0,.68333,.07847,.08334,.82847],89:[0,.68333,.22222,0,.58056],90:[0,.68333,.07153,.08334,.68264],97:[0,.43056,0,0,.52859],98:[0,.69444,0,0,.42917],99:[0,.43056,0,.05556,.43276],100:[0,.69444,0,.16667,.52049],101:[0,.43056,0,.05556,.46563],102:[.19444,.69444,.10764,.16667,.48959],103:[.19444,.43056,.03588,.02778,.47697],104:[0,.69444,0,0,.57616],105:[0,.65952,0,0,.34451],106:[.19444,.65952,.05724,0,.41181],107:[0,.69444,.03148,0,.5206],108:[0,.69444,.01968,.08334,.29838],109:[0,.43056,0,0,.87801],110:[0,.43056,0,0,.60023],111:[0,.43056,0,.05556,.48472],112:[.19444,.43056,0,.08334,.50313],113:[.19444,.43056,.03588,.08334,.44641],114:[0,.43056,.02778,.05556,.45116],115:[0,.43056,0,.05556,.46875],116:[0,.61508,0,.08334,.36111],117:[0,.43056,0,.02778,.57246],118:[0,.43056,.03588,.02778,.48472],119:[0,.43056,.02691,.08334,.71592],120:[0,.43056,0,.02778,.57153],121:[.19444,.43056,.03588,.05556,.49028],122:[0,.43056,.04398,.05556,.46505],160:[0,0,0,0,.25],915:[0,.68333,.13889,.08334,.61528],916:[0,.68333,0,.16667,.83334],920:[0,.68333,.02778,.08334,.76278],923:[0,.68333,0,.16667,.69445],926:[0,.68333,.07569,.08334,.74236],928:[0,.68333,.08125,.05556,.83125],931:[0,.68333,.05764,.08334,.77986],933:[0,.68333,.13889,.05556,.58333],934:[0,.68333,0,.08334,.66667],936:[0,.68333,.11,.05556,.61222],937:[0,.68333,.05017,.08334,.7724],945:[0,.43056,.0037,.02778,.6397],946:[.19444,.69444,.05278,.08334,.56563],947:[.19444,.43056,.05556,0,.51773],948:[0,.69444,.03785,.05556,.44444],949:[0,.43056,0,.08334,.46632],950:[.19444,.69444,.07378,.08334,.4375],951:[.19444,.43056,.03588,.05556,.49653],952:[0,.69444,.02778,.08334,.46944],953:[0,.43056,0,.05556,.35394],954:[0,.43056,0,0,.57616],955:[0,.69444,0,0,.58334],956:[.19444,.43056,0,.02778,.60255],957:[0,.43056,.06366,.02778,.49398],958:[.19444,.69444,.04601,.11111,.4375],959:[0,.43056,0,.05556,.48472],960:[0,.43056,.03588,0,.57003],961:[.19444,.43056,0,.08334,.51702],962:[.09722,.43056,.07986,.08334,.36285],963:[0,.43056,.03588,0,.57141],964:[0,.43056,.1132,.02778,.43715],965:[0,.43056,.03588,.02778,.54028],966:[.19444,.43056,0,.08334,.65417],967:[.19444,.43056,0,.05556,.62569],968:[.19444,.69444,.03588,.11111,.65139],969:[0,.43056,.03588,0,.62245],977:[0,.69444,0,.08334,.59144],981:[.19444,.69444,0,.08334,.59583],982:[0,.43056,.02778,0,.82813],1009:[.19444,.43056,0,.08334,.51702],1013:[0,.43056,0,.05556,.4059],57649:[0,.43056,0,.02778,.32246],57911:[.19444,.43056,0,.08334,.38403]},"SansSerif-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.36667],34:[0,.69444,0,0,.55834],35:[.19444,.69444,0,0,.91667],36:[.05556,.75,0,0,.55],37:[.05556,.75,0,0,1.02912],38:[0,.69444,0,0,.83056],39:[0,.69444,0,0,.30556],40:[.25,.75,0,0,.42778],41:[.25,.75,0,0,.42778],42:[0,.75,0,0,.55],43:[.11667,.61667,0,0,.85556],44:[.10556,.13056,0,0,.30556],45:[0,.45833,0,0,.36667],46:[0,.13056,0,0,.30556],47:[.25,.75,0,0,.55],48:[0,.69444,0,0,.55],49:[0,.69444,0,0,.55],50:[0,.69444,0,0,.55],51:[0,.69444,0,0,.55],52:[0,.69444,0,0,.55],53:[0,.69444,0,0,.55],54:[0,.69444,0,0,.55],55:[0,.69444,0,0,.55],56:[0,.69444,0,0,.55],57:[0,.69444,0,0,.55],58:[0,.45833,0,0,.30556],59:[.10556,.45833,0,0,.30556],61:[-.09375,.40625,0,0,.85556],63:[0,.69444,0,0,.51945],64:[0,.69444,0,0,.73334],65:[0,.69444,0,0,.73334],66:[0,.69444,0,0,.73334],67:[0,.69444,0,0,.70278],68:[0,.69444,0,0,.79445],69:[0,.69444,0,0,.64167],70:[0,.69444,0,0,.61111],71:[0,.69444,0,0,.73334],72:[0,.69444,0,0,.79445],73:[0,.69444,0,0,.33056],74:[0,.69444,0,0,.51945],75:[0,.69444,0,0,.76389],76:[0,.69444,0,0,.58056],77:[0,.69444,0,0,.97778],78:[0,.69444,0,0,.79445],79:[0,.69444,0,0,.79445],80:[0,.69444,0,0,.70278],81:[.10556,.69444,0,0,.79445],82:[0,.69444,0,0,.70278],83:[0,.69444,0,0,.61111],84:[0,.69444,0,0,.73334],85:[0,.69444,0,0,.76389],86:[0,.69444,.01528,0,.73334],87:[0,.69444,.01528,0,1.03889],88:[0,.69444,0,0,.73334],89:[0,.69444,.0275,0,.73334],90:[0,.69444,0,0,.67223],91:[.25,.75,0,0,.34306],93:[.25,.75,0,0,.34306],94:[0,.69444,0,0,.55],95:[.35,.10833,.03056,0,.55],97:[0,.45833,0,0,.525],98:[0,.69444,0,0,.56111],99:[0,.45833,0,0,.48889],100:[0,.69444,0,0,.56111],101:[0,.45833,0,0,.51111],102:[0,.69444,.07639,0,.33611],103:[.19444,.45833,.01528,0,.55],104:[0,.69444,0,0,.56111],105:[0,.69444,0,0,.25556],106:[.19444,.69444,0,0,.28611],107:[0,.69444,0,0,.53056],108:[0,.69444,0,0,.25556],109:[0,.45833,0,0,.86667],110:[0,.45833,0,0,.56111],111:[0,.45833,0,0,.55],112:[.19444,.45833,0,0,.56111],113:[.19444,.45833,0,0,.56111],114:[0,.45833,.01528,0,.37222],115:[0,.45833,0,0,.42167],116:[0,.58929,0,0,.40417],117:[0,.45833,0,0,.56111],118:[0,.45833,.01528,0,.5],119:[0,.45833,.01528,0,.74445],120:[0,.45833,0,0,.5],121:[.19444,.45833,.01528,0,.5],122:[0,.45833,0,0,.47639],126:[.35,.34444,0,0,.55],160:[0,0,0,0,.25],168:[0,.69444,0,0,.55],176:[0,.69444,0,0,.73334],180:[0,.69444,0,0,.55],184:[.17014,0,0,0,.48889],305:[0,.45833,0,0,.25556],567:[.19444,.45833,0,0,.28611],710:[0,.69444,0,0,.55],711:[0,.63542,0,0,.55],713:[0,.63778,0,0,.55],728:[0,.69444,0,0,.55],729:[0,.69444,0,0,.30556],730:[0,.69444,0,0,.73334],732:[0,.69444,0,0,.55],733:[0,.69444,0,0,.55],915:[0,.69444,0,0,.58056],916:[0,.69444,0,0,.91667],920:[0,.69444,0,0,.85556],923:[0,.69444,0,0,.67223],926:[0,.69444,0,0,.73334],928:[0,.69444,0,0,.79445],931:[0,.69444,0,0,.79445],933:[0,.69444,0,0,.85556],934:[0,.69444,0,0,.79445],936:[0,.69444,0,0,.85556],937:[0,.69444,0,0,.79445],8211:[0,.45833,.03056,0,.55],8212:[0,.45833,.03056,0,1.10001],8216:[0,.69444,0,0,.30556],8217:[0,.69444,0,0,.30556],8220:[0,.69444,0,0,.55834],8221:[0,.69444,0,0,.55834]},"SansSerif-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.05733,0,.31945],34:[0,.69444,.00316,0,.5],35:[.19444,.69444,.05087,0,.83334],36:[.05556,.75,.11156,0,.5],37:[.05556,.75,.03126,0,.83334],38:[0,.69444,.03058,0,.75834],39:[0,.69444,.07816,0,.27778],40:[.25,.75,.13164,0,.38889],41:[.25,.75,.02536,0,.38889],42:[0,.75,.11775,0,.5],43:[.08333,.58333,.02536,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,.01946,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,.13164,0,.5],48:[0,.65556,.11156,0,.5],49:[0,.65556,.11156,0,.5],50:[0,.65556,.11156,0,.5],51:[0,.65556,.11156,0,.5],52:[0,.65556,.11156,0,.5],53:[0,.65556,.11156,0,.5],54:[0,.65556,.11156,0,.5],55:[0,.65556,.11156,0,.5],56:[0,.65556,.11156,0,.5],57:[0,.65556,.11156,0,.5],58:[0,.44444,.02502,0,.27778],59:[.125,.44444,.02502,0,.27778],61:[-.13,.37,.05087,0,.77778],63:[0,.69444,.11809,0,.47222],64:[0,.69444,.07555,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,.08293,0,.66667],67:[0,.69444,.11983,0,.63889],68:[0,.69444,.07555,0,.72223],69:[0,.69444,.11983,0,.59722],70:[0,.69444,.13372,0,.56945],71:[0,.69444,.11983,0,.66667],72:[0,.69444,.08094,0,.70834],73:[0,.69444,.13372,0,.27778],74:[0,.69444,.08094,0,.47222],75:[0,.69444,.11983,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,.08094,0,.875],78:[0,.69444,.08094,0,.70834],79:[0,.69444,.07555,0,.73611],80:[0,.69444,.08293,0,.63889],81:[.125,.69444,.07555,0,.73611],82:[0,.69444,.08293,0,.64584],83:[0,.69444,.09205,0,.55556],84:[0,.69444,.13372,0,.68056],85:[0,.69444,.08094,0,.6875],86:[0,.69444,.1615,0,.66667],87:[0,.69444,.1615,0,.94445],88:[0,.69444,.13372,0,.66667],89:[0,.69444,.17261,0,.66667],90:[0,.69444,.11983,0,.61111],91:[.25,.75,.15942,0,.28889],93:[.25,.75,.08719,0,.28889],94:[0,.69444,.0799,0,.5],95:[.35,.09444,.08616,0,.5],97:[0,.44444,.00981,0,.48056],98:[0,.69444,.03057,0,.51667],99:[0,.44444,.08336,0,.44445],100:[0,.69444,.09483,0,.51667],101:[0,.44444,.06778,0,.44445],102:[0,.69444,.21705,0,.30556],103:[.19444,.44444,.10836,0,.5],104:[0,.69444,.01778,0,.51667],105:[0,.67937,.09718,0,.23889],106:[.19444,.67937,.09162,0,.26667],107:[0,.69444,.08336,0,.48889],108:[0,.69444,.09483,0,.23889],109:[0,.44444,.01778,0,.79445],110:[0,.44444,.01778,0,.51667],111:[0,.44444,.06613,0,.5],112:[.19444,.44444,.0389,0,.51667],113:[.19444,.44444,.04169,0,.51667],114:[0,.44444,.10836,0,.34167],115:[0,.44444,.0778,0,.38333],116:[0,.57143,.07225,0,.36111],117:[0,.44444,.04169,0,.51667],118:[0,.44444,.10836,0,.46111],119:[0,.44444,.10836,0,.68334],120:[0,.44444,.09169,0,.46111],121:[.19444,.44444,.10836,0,.46111],122:[0,.44444,.08752,0,.43472],126:[.35,.32659,.08826,0,.5],160:[0,0,0,0,.25],168:[0,.67937,.06385,0,.5],176:[0,.69444,0,0,.73752],184:[.17014,0,0,0,.44445],305:[0,.44444,.04169,0,.23889],567:[.19444,.44444,.04169,0,.26667],710:[0,.69444,.0799,0,.5],711:[0,.63194,.08432,0,.5],713:[0,.60889,.08776,0,.5],714:[0,.69444,.09205,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,.09483,0,.5],729:[0,.67937,.07774,0,.27778],730:[0,.69444,0,0,.73752],732:[0,.67659,.08826,0,.5],733:[0,.69444,.09205,0,.5],915:[0,.69444,.13372,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,.07555,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,.12816,0,.66667],928:[0,.69444,.08094,0,.70834],931:[0,.69444,.11983,0,.72222],933:[0,.69444,.09031,0,.77778],934:[0,.69444,.04603,0,.72222],936:[0,.69444,.09031,0,.77778],937:[0,.69444,.08293,0,.72222],8211:[0,.44444,.08616,0,.5],8212:[0,.44444,.08616,0,1],8216:[0,.69444,.07816,0,.27778],8217:[0,.69444,.07816,0,.27778],8220:[0,.69444,.14205,0,.5],8221:[0,.69444,.00316,0,.5]},"SansSerif-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.31945],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.75834],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,0,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.65556,0,0,.5],49:[0,.65556,0,0,.5],50:[0,.65556,0,0,.5],51:[0,.65556,0,0,.5],52:[0,.65556,0,0,.5],53:[0,.65556,0,0,.5],54:[0,.65556,0,0,.5],55:[0,.65556,0,0,.5],56:[0,.65556,0,0,.5],57:[0,.65556,0,0,.5],58:[0,.44444,0,0,.27778],59:[.125,.44444,0,0,.27778],61:[-.13,.37,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,0,0,.66667],67:[0,.69444,0,0,.63889],68:[0,.69444,0,0,.72223],69:[0,.69444,0,0,.59722],70:[0,.69444,0,0,.56945],71:[0,.69444,0,0,.66667],72:[0,.69444,0,0,.70834],73:[0,.69444,0,0,.27778],74:[0,.69444,0,0,.47222],75:[0,.69444,0,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,0,0,.875],78:[0,.69444,0,0,.70834],79:[0,.69444,0,0,.73611],80:[0,.69444,0,0,.63889],81:[.125,.69444,0,0,.73611],82:[0,.69444,0,0,.64584],83:[0,.69444,0,0,.55556],84:[0,.69444,0,0,.68056],85:[0,.69444,0,0,.6875],86:[0,.69444,.01389,0,.66667],87:[0,.69444,.01389,0,.94445],88:[0,.69444,0,0,.66667],89:[0,.69444,.025,0,.66667],90:[0,.69444,0,0,.61111],91:[.25,.75,0,0,.28889],93:[.25,.75,0,0,.28889],94:[0,.69444,0,0,.5],95:[.35,.09444,.02778,0,.5],97:[0,.44444,0,0,.48056],98:[0,.69444,0,0,.51667],99:[0,.44444,0,0,.44445],100:[0,.69444,0,0,.51667],101:[0,.44444,0,0,.44445],102:[0,.69444,.06944,0,.30556],103:[.19444,.44444,.01389,0,.5],104:[0,.69444,0,0,.51667],105:[0,.67937,0,0,.23889],106:[.19444,.67937,0,0,.26667],107:[0,.69444,0,0,.48889],108:[0,.69444,0,0,.23889],109:[0,.44444,0,0,.79445],110:[0,.44444,0,0,.51667],111:[0,.44444,0,0,.5],112:[.19444,.44444,0,0,.51667],113:[.19444,.44444,0,0,.51667],114:[0,.44444,.01389,0,.34167],115:[0,.44444,0,0,.38333],116:[0,.57143,0,0,.36111],117:[0,.44444,0,0,.51667],118:[0,.44444,.01389,0,.46111],119:[0,.44444,.01389,0,.68334],120:[0,.44444,0,0,.46111],121:[.19444,.44444,.01389,0,.46111],122:[0,.44444,0,0,.43472],126:[.35,.32659,0,0,.5],160:[0,0,0,0,.25],168:[0,.67937,0,0,.5],176:[0,.69444,0,0,.66667],184:[.17014,0,0,0,.44445],305:[0,.44444,0,0,.23889],567:[.19444,.44444,0,0,.26667],710:[0,.69444,0,0,.5],711:[0,.63194,0,0,.5],713:[0,.60889,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.67937,0,0,.27778],730:[0,.69444,0,0,.66667],732:[0,.67659,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.69444,0,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,0,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,0,0,.66667],928:[0,.69444,0,0,.70834],931:[0,.69444,0,0,.72222],933:[0,.69444,0,0,.77778],934:[0,.69444,0,0,.72222],936:[0,.69444,0,0,.77778],937:[0,.69444,0,0,.72222],8211:[0,.44444,.02778,0,.5],8212:[0,.44444,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5]},"Script-Regular":{32:[0,0,0,0,.25],65:[0,.7,.22925,0,.80253],66:[0,.7,.04087,0,.90757],67:[0,.7,.1689,0,.66619],68:[0,.7,.09371,0,.77443],69:[0,.7,.18583,0,.56162],70:[0,.7,.13634,0,.89544],71:[0,.7,.17322,0,.60961],72:[0,.7,.29694,0,.96919],73:[0,.7,.19189,0,.80907],74:[.27778,.7,.19189,0,1.05159],75:[0,.7,.31259,0,.91364],76:[0,.7,.19189,0,.87373],77:[0,.7,.15981,0,1.08031],78:[0,.7,.3525,0,.9015],79:[0,.7,.08078,0,.73787],80:[0,.7,.08078,0,1.01262],81:[0,.7,.03305,0,.88282],82:[0,.7,.06259,0,.85],83:[0,.7,.19189,0,.86767],84:[0,.7,.29087,0,.74697],85:[0,.7,.25815,0,.79996],86:[0,.7,.27523,0,.62204],87:[0,.7,.27523,0,.80532],88:[0,.7,.26006,0,.94445],89:[0,.7,.2939,0,.70961],90:[0,.7,.24037,0,.8212],160:[0,0,0,0,.25]},"Size1-Regular":{32:[0,0,0,0,.25],40:[.35001,.85,0,0,.45834],41:[.35001,.85,0,0,.45834],47:[.35001,.85,0,0,.57778],91:[.35001,.85,0,0,.41667],92:[.35001,.85,0,0,.57778],93:[.35001,.85,0,0,.41667],123:[.35001,.85,0,0,.58334],125:[.35001,.85,0,0,.58334],160:[0,0,0,0,.25],710:[0,.72222,0,0,.55556],732:[0,.72222,0,0,.55556],770:[0,.72222,0,0,.55556],771:[0,.72222,0,0,.55556],8214:[-99e-5,.601,0,0,.77778],8593:[1e-5,.6,0,0,.66667],8595:[1e-5,.6,0,0,.66667],8657:[1e-5,.6,0,0,.77778],8659:[1e-5,.6,0,0,.77778],8719:[.25001,.75,0,0,.94445],8720:[.25001,.75,0,0,.94445],8721:[.25001,.75,0,0,1.05556],8730:[.35001,.85,0,0,1],8739:[-.00599,.606,0,0,.33333],8741:[-.00599,.606,0,0,.55556],8747:[.30612,.805,.19445,0,.47222],8748:[.306,.805,.19445,0,.47222],8749:[.306,.805,.19445,0,.47222],8750:[.30612,.805,.19445,0,.47222],8896:[.25001,.75,0,0,.83334],8897:[.25001,.75,0,0,.83334],8898:[.25001,.75,0,0,.83334],8899:[.25001,.75,0,0,.83334],8968:[.35001,.85,0,0,.47222],8969:[.35001,.85,0,0,.47222],8970:[.35001,.85,0,0,.47222],8971:[.35001,.85,0,0,.47222],9168:[-99e-5,.601,0,0,.66667],10216:[.35001,.85,0,0,.47222],10217:[.35001,.85,0,0,.47222],10752:[.25001,.75,0,0,1.11111],10753:[.25001,.75,0,0,1.11111],10754:[.25001,.75,0,0,1.11111],10756:[.25001,.75,0,0,.83334],10758:[.25001,.75,0,0,.83334]},"Size2-Regular":{32:[0,0,0,0,.25],40:[.65002,1.15,0,0,.59722],41:[.65002,1.15,0,0,.59722],47:[.65002,1.15,0,0,.81111],91:[.65002,1.15,0,0,.47222],92:[.65002,1.15,0,0,.81111],93:[.65002,1.15,0,0,.47222],123:[.65002,1.15,0,0,.66667],125:[.65002,1.15,0,0,.66667],160:[0,0,0,0,.25],710:[0,.75,0,0,1],732:[0,.75,0,0,1],770:[0,.75,0,0,1],771:[0,.75,0,0,1],8719:[.55001,1.05,0,0,1.27778],8720:[.55001,1.05,0,0,1.27778],8721:[.55001,1.05,0,0,1.44445],8730:[.65002,1.15,0,0,1],8747:[.86225,1.36,.44445,0,.55556],8748:[.862,1.36,.44445,0,.55556],8749:[.862,1.36,.44445,0,.55556],8750:[.86225,1.36,.44445,0,.55556],8896:[.55001,1.05,0,0,1.11111],8897:[.55001,1.05,0,0,1.11111],8898:[.55001,1.05,0,0,1.11111],8899:[.55001,1.05,0,0,1.11111],8968:[.65002,1.15,0,0,.52778],8969:[.65002,1.15,0,0,.52778],8970:[.65002,1.15,0,0,.52778],8971:[.65002,1.15,0,0,.52778],10216:[.65002,1.15,0,0,.61111],10217:[.65002,1.15,0,0,.61111],10752:[.55001,1.05,0,0,1.51112],10753:[.55001,1.05,0,0,1.51112],10754:[.55001,1.05,0,0,1.51112],10756:[.55001,1.05,0,0,1.11111],10758:[.55001,1.05,0,0,1.11111]},"Size3-Regular":{32:[0,0,0,0,.25],40:[.95003,1.45,0,0,.73611],41:[.95003,1.45,0,0,.73611],47:[.95003,1.45,0,0,1.04445],91:[.95003,1.45,0,0,.52778],92:[.95003,1.45,0,0,1.04445],93:[.95003,1.45,0,0,.52778],123:[.95003,1.45,0,0,.75],125:[.95003,1.45,0,0,.75],160:[0,0,0,0,.25],710:[0,.75,0,0,1.44445],732:[0,.75,0,0,1.44445],770:[0,.75,0,0,1.44445],771:[0,.75,0,0,1.44445],8730:[.95003,1.45,0,0,1],8968:[.95003,1.45,0,0,.58334],8969:[.95003,1.45,0,0,.58334],8970:[.95003,1.45,0,0,.58334],8971:[.95003,1.45,0,0,.58334],10216:[.95003,1.45,0,0,.75],10217:[.95003,1.45,0,0,.75]},"Size4-Regular":{32:[0,0,0,0,.25],40:[1.25003,1.75,0,0,.79167],41:[1.25003,1.75,0,0,.79167],47:[1.25003,1.75,0,0,1.27778],91:[1.25003,1.75,0,0,.58334],92:[1.25003,1.75,0,0,1.27778],93:[1.25003,1.75,0,0,.58334],123:[1.25003,1.75,0,0,.80556],125:[1.25003,1.75,0,0,.80556],160:[0,0,0,0,.25],710:[0,.825,0,0,1.8889],732:[0,.825,0,0,1.8889],770:[0,.825,0,0,1.8889],771:[0,.825,0,0,1.8889],8730:[1.25003,1.75,0,0,1],8968:[1.25003,1.75,0,0,.63889],8969:[1.25003,1.75,0,0,.63889],8970:[1.25003,1.75,0,0,.63889],8971:[1.25003,1.75,0,0,.63889],9115:[.64502,1.155,0,0,.875],9116:[1e-5,.6,0,0,.875],9117:[.64502,1.155,0,0,.875],9118:[.64502,1.155,0,0,.875],9119:[1e-5,.6,0,0,.875],9120:[.64502,1.155,0,0,.875],9121:[.64502,1.155,0,0,.66667],9122:[-99e-5,.601,0,0,.66667],9123:[.64502,1.155,0,0,.66667],9124:[.64502,1.155,0,0,.66667],9125:[-99e-5,.601,0,0,.66667],9126:[.64502,1.155,0,0,.66667],9127:[1e-5,.9,0,0,.88889],9128:[.65002,1.15,0,0,.88889],9129:[.90001,0,0,0,.88889],9130:[0,.3,0,0,.88889],9131:[1e-5,.9,0,0,.88889],9132:[.65002,1.15,0,0,.88889],9133:[.90001,0,0,0,.88889],9143:[.88502,.915,0,0,1.05556],10216:[1.25003,1.75,0,0,.80556],10217:[1.25003,1.75,0,0,.80556],57344:[-.00499,.605,0,0,1.05556],57345:[-.00499,.605,0,0,1.05556],57680:[0,.12,0,0,.45],57681:[0,.12,0,0,.45],57682:[0,.12,0,0,.45],57683:[0,.12,0,0,.45]},"Typewriter-Regular":{32:[0,0,0,0,.525],33:[0,.61111,0,0,.525],34:[0,.61111,0,0,.525],35:[0,.61111,0,0,.525],36:[.08333,.69444,0,0,.525],37:[.08333,.69444,0,0,.525],38:[0,.61111,0,0,.525],39:[0,.61111,0,0,.525],40:[.08333,.69444,0,0,.525],41:[.08333,.69444,0,0,.525],42:[0,.52083,0,0,.525],43:[-.08056,.53055,0,0,.525],44:[.13889,.125,0,0,.525],45:[-.08056,.53055,0,0,.525],46:[0,.125,0,0,.525],47:[.08333,.69444,0,0,.525],48:[0,.61111,0,0,.525],49:[0,.61111,0,0,.525],50:[0,.61111,0,0,.525],51:[0,.61111,0,0,.525],52:[0,.61111,0,0,.525],53:[0,.61111,0,0,.525],54:[0,.61111,0,0,.525],55:[0,.61111,0,0,.525],56:[0,.61111,0,0,.525],57:[0,.61111,0,0,.525],58:[0,.43056,0,0,.525],59:[.13889,.43056,0,0,.525],60:[-.05556,.55556,0,0,.525],61:[-.19549,.41562,0,0,.525],62:[-.05556,.55556,0,0,.525],63:[0,.61111,0,0,.525],64:[0,.61111,0,0,.525],65:[0,.61111,0,0,.525],66:[0,.61111,0,0,.525],67:[0,.61111,0,0,.525],68:[0,.61111,0,0,.525],69:[0,.61111,0,0,.525],70:[0,.61111,0,0,.525],71:[0,.61111,0,0,.525],72:[0,.61111,0,0,.525],73:[0,.61111,0,0,.525],74:[0,.61111,0,0,.525],75:[0,.61111,0,0,.525],76:[0,.61111,0,0,.525],77:[0,.61111,0,0,.525],78:[0,.61111,0,0,.525],79:[0,.61111,0,0,.525],80:[0,.61111,0,0,.525],81:[.13889,.61111,0,0,.525],82:[0,.61111,0,0,.525],83:[0,.61111,0,0,.525],84:[0,.61111,0,0,.525],85:[0,.61111,0,0,.525],86:[0,.61111,0,0,.525],87:[0,.61111,0,0,.525],88:[0,.61111,0,0,.525],89:[0,.61111,0,0,.525],90:[0,.61111,0,0,.525],91:[.08333,.69444,0,0,.525],92:[.08333,.69444,0,0,.525],93:[.08333,.69444,0,0,.525],94:[0,.61111,0,0,.525],95:[.09514,0,0,0,.525],96:[0,.61111,0,0,.525],97:[0,.43056,0,0,.525],98:[0,.61111,0,0,.525],99:[0,.43056,0,0,.525],100:[0,.61111,0,0,.525],101:[0,.43056,0,0,.525],102:[0,.61111,0,0,.525],103:[.22222,.43056,0,0,.525],104:[0,.61111,0,0,.525],105:[0,.61111,0,0,.525],106:[.22222,.61111,0,0,.525],107:[0,.61111,0,0,.525],108:[0,.61111,0,0,.525],109:[0,.43056,0,0,.525],110:[0,.43056,0,0,.525],111:[0,.43056,0,0,.525],112:[.22222,.43056,0,0,.525],113:[.22222,.43056,0,0,.525],114:[0,.43056,0,0,.525],115:[0,.43056,0,0,.525],116:[0,.55358,0,0,.525],117:[0,.43056,0,0,.525],118:[0,.43056,0,0,.525],119:[0,.43056,0,0,.525],120:[0,.43056,0,0,.525],121:[.22222,.43056,0,0,.525],122:[0,.43056,0,0,.525],123:[.08333,.69444,0,0,.525],124:[.08333,.69444,0,0,.525],125:[.08333,.69444,0,0,.525],126:[0,.61111,0,0,.525],127:[0,.61111,0,0,.525],160:[0,0,0,0,.525],176:[0,.61111,0,0,.525],184:[.19445,0,0,0,.525],305:[0,.43056,0,0,.525],567:[.22222,.43056,0,0,.525],711:[0,.56597,0,0,.525],713:[0,.56555,0,0,.525],714:[0,.61111,0,0,.525],715:[0,.61111,0,0,.525],728:[0,.61111,0,0,.525],730:[0,.61111,0,0,.525],770:[0,.61111,0,0,.525],771:[0,.61111,0,0,.525],776:[0,.61111,0,0,.525],915:[0,.61111,0,0,.525],916:[0,.61111,0,0,.525],920:[0,.61111,0,0,.525],923:[0,.61111,0,0,.525],926:[0,.61111,0,0,.525],928:[0,.61111,0,0,.525],931:[0,.61111,0,0,.525],933:[0,.61111,0,0,.525],934:[0,.61111,0,0,.525],936:[0,.61111,0,0,.525],937:[0,.61111,0,0,.525],8216:[0,.61111,0,0,.525],8217:[0,.61111,0,0,.525],8242:[0,.61111,0,0,.525],9251:[.11111,.21944,0,0,.525]}},Te={slant:[.25,.25,.25],space:[0,0,0],stretch:[0,0,0],shrink:[0,0,0],xHeight:[.431,.431,.431],quad:[1,1.171,1.472],extraSpace:[0,0,0],num1:[.677,.732,.925],num2:[.394,.384,.387],num3:[.444,.471,.504],denom1:[.686,.752,1.025],denom2:[.345,.344,.532],sup1:[.413,.503,.504],sup2:[.363,.431,.404],sup3:[.289,.286,.294],sub1:[.15,.143,.2],sub2:[.247,.286,.4],supDrop:[.386,.353,.494],subDrop:[.05,.071,.1],delim1:[2.39,1.7,1.98],delim2:[1.01,1.157,1.42],axisHeight:[.25,.25,.25],defaultRuleThickness:[.04,.049,.049],bigOpSpacing1:[.111,.111,.111],bigOpSpacing2:[.166,.166,.166],bigOpSpacing3:[.2,.2,.2],bigOpSpacing4:[.6,.611,.611],bigOpSpacing5:[.1,.143,.143],sqrtRuleThickness:[.04,.04,.04],ptPerEm:[10,10,10],doubleRuleSep:[.2,.2,.2],arrayRuleWidth:[.04,.04,.04],fboxsep:[.3,.3,.3],fboxrule:[.04,.04,.04]},ur={Å:"A",Ð:"D",Þ:"o",å:"a",ð:"d",þ:"o",А:"A",Б:"B",В:"B",Г:"F",Д:"A",Е:"E",Ж:"K",З:"3",И:"N",Й:"N",К:"K",Л:"N",М:"M",Н:"H",О:"O",П:"N",Р:"P",С:"C",Т:"T",У:"y",Ф:"O",Х:"X",Ц:"U",Ч:"h",Ш:"W",Щ:"W",Ъ:"B",Ы:"X",Ь:"B",Э:"3",Ю:"X",Я:"R",а:"a",б:"b",в:"a",г:"r",д:"y",е:"e",ж:"m",з:"e",и:"n",й:"n",к:"n",л:"n",м:"m",н:"n",о:"o",п:"n",р:"p",с:"c",т:"o",у:"y",ф:"b",х:"x",ц:"n",ч:"n",ш:"w",щ:"w",ъ:"a",ы:"m",ь:"a",э:"e",ю:"m",я:"r"};function Et(r,e,t){if(!q0[e])throw new Error("Font metrics not found for font: "+e+".");var a=r.charCodeAt(0),n=q0[e][a];if(!n&&r[0]in ur&&(a=ur[r[0]].charCodeAt(0),n=q0[e][a]),!n&&t==="text"&&ia(a)&&(n=q0[e][77]),n)return{depth:n[0],height:n[1],italic:n[2],skew:n[3],width:n[4]}}var at={};function b1(r){var e;if(r>=5?e=0:r>=3?e=1:e=2,!at[e]){var t=at[e]={cssEmPerMu:Te.quad[e]/18};for(var a in Te)Te.hasOwnProperty(a)&&(t[a]=Te[a][e])}return at[e]}var y1=[[1,1,1],[2,1,1],[3,1,1],[4,2,1],[5,2,1],[6,3,1],[7,4,2],[8,6,3],[9,7,6],[10,8,7],[11,10,9]],hr=[.5,.6,.7,.8,.9,1,1.2,1.44,1.728,2.074,2.488],cr=function(e,t){return t.size<2?e:y1[e-1][t.size-1]};class E0{constructor(e){this.style=void 0,this.color=void 0,this.size=void 0,this.textSize=void 0,this.phantom=void 0,this.font=void 0,this.fontFamily=void 0,this.fontWeight=void 0,this.fontShape=void 0,this.sizeMultiplier=void 0,this.maxSize=void 0,this.minRuleThickness=void 0,this._fontMetrics=void 0,this.style=e.style,this.color=e.color,this.size=e.size||E0.BASESIZE,this.textSize=e.textSize||this.size,this.phantom=!!e.phantom,this.font=e.font||"",this.fontFamily=e.fontFamily||"",this.fontWeight=e.fontWeight||"",this.fontShape=e.fontShape||"",this.sizeMultiplier=hr[this.size-1],this.maxSize=e.maxSize,this.minRuleThickness=e.minRuleThickness,this._fontMetrics=void 0}extend(e){var t={style:this.style,size:this.size,textSize:this.textSize,color:this.color,phantom:this.phantom,font:this.font,fontFamily:this.fontFamily,fontWeight:this.fontWeight,fontShape:this.fontShape,maxSize:this.maxSize,minRuleThickness:this.minRuleThickness};for(var a in e)e.hasOwnProperty(a)&&(t[a]=e[a]);return new E0(t)}havingStyle(e){return this.style===e?this:this.extend({style:e,size:cr(this.textSize,e)})}havingCrampedStyle(){return this.havingStyle(this.style.cramp())}havingSize(e){return this.size===e&&this.textSize===e?this:this.extend({style:this.style.text(),size:e,textSize:e,sizeMultiplier:hr[e-1]})}havingBaseStyle(e){e=e||this.style.text();var t=cr(E0.BASESIZE,e);return this.size===t&&this.textSize===E0.BASESIZE&&this.style===e?this:this.extend({style:e,size:t})}havingBaseSizing(){var e;switch(this.style.id){case 4:case 5:e=3;break;case 6:case 7:e=1;break;default:e=6}return this.extend({style:this.style.text(),size:e})}withColor(e){return this.extend({color:e})}withPhantom(){return this.extend({phantom:!0})}withFont(e){return this.extend({font:e})}withTextFontFamily(e){return this.extend({fontFamily:e,font:""})}withTextFontWeight(e){return this.extend({fontWeight:e,font:""})}withTextFontShape(e){return this.extend({fontShape:e,font:""})}sizingClasses(e){return e.size!==this.size?["sizing","reset-size"+e.size,"size"+this.size]:[]}baseSizingClasses(){return this.size!==E0.BASESIZE?["sizing","reset-size"+this.size,"size"+E0.BASESIZE]:[]}fontMetrics(){return this._fontMetrics||(this._fontMetrics=b1(this.size)),this._fontMetrics}getColor(){return this.phantom?"transparent":this.color}}E0.BASESIZE=6;var St={pt:1,mm:7227/2540,cm:7227/254,in:72.27,bp:803/800,pc:12,dd:1238/1157,cc:14856/1157,nd:685/642,nc:1370/107,sp:1/65536,px:803/800},x1={ex:!0,em:!0,mu:!0},sa=function(e){return typeof e!="string"&&(e=e.unit),e in St||e in x1||e==="ex"},_=function(e,t){var a;if(e.unit in St)a=St[e.unit]/t.fontMetrics().ptPerEm/t.sizeMultiplier;else if(e.unit==="mu")a=t.fontMetrics().cssEmPerMu;else{var n;if(t.style.isTight()?n=t.havingStyle(t.style.text()):n=t,e.unit==="ex")a=n.fontMetrics().xHeight;else if(e.unit==="em")a=n.fontMetrics().quad;else throw new A("Invalid unit: '"+e.unit+"'");n!==t&&(a*=n.sizeMultiplier/t.sizeMultiplier)}return Math.min(e.number*a,t.maxSize)},T=function(e){return+e.toFixed(4)+"em"},W0=function(e){return e.filter(t=>t).join(" ")},oa=function(e,t,a){if(this.classes=e||[],this.attributes={},this.height=0,this.depth=0,this.maxFontSize=0,this.style=a||{},t){t.style.isTight()&&this.classes.push("mtight");var n=t.getColor();n&&(this.style.color=n)}},ua=function(e){var t=document.createElement(e);t.className=W0(this.classes);for(var a in this.style)this.style.hasOwnProperty(a)&&(t.style[a]=this.style[a]);for(var n in this.attributes)this.attributes.hasOwnProperty(n)&&t.setAttribute(n,this.attributes[n]);for(var l=0;l<this.children.length;l++)t.appendChild(this.children[l].toNode());return t},w1=/[\s"'>/=\x00-\x1f]/,ha=function(e){var t="<"+e;this.classes.length&&(t+=' class="'+q.escape(W0(this.classes))+'"');var a="";for(var n in this.style)this.style.hasOwnProperty(n)&&(a+=q.hyphenate(n)+":"+this.style[n]+";");a&&(t+=' style="'+q.escape(a)+'"');for(var l in this.attributes)if(this.attributes.hasOwnProperty(l)){if(w1.test(l))throw new A("Invalid attribute name '"+l+"'");t+=" "+l+'="'+q.escape(this.attributes[l])+'"'}t+=">";for(var o=0;o<this.children.length;o++)t+=this.children[o].toMarkup();return t+="</"+e+">",t};class Ue{constructor(e,t,a,n){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.width=void 0,this.maxFontSize=void 0,this.style=void 0,oa.call(this,e,a,n),this.children=t||[]}setAttribute(e,t){this.attributes[e]=t}hasClass(e){return q.contains(this.classes,e)}toNode(){return ua.call(this,"span")}toMarkup(){return ha.call(this,"span")}}class ca{constructor(e,t,a,n){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,oa.call(this,t,n),this.children=a||[],this.setAttribute("href",e)}setAttribute(e,t){this.attributes[e]=t}hasClass(e){return q.contains(this.classes,e)}toNode(){return ua.call(this,"a")}toMarkup(){return ha.call(this,"a")}}class k1{constructor(e,t,a){this.src=void 0,this.alt=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.alt=t,this.src=e,this.classes=["mord"],this.style=a}hasClass(e){return q.contains(this.classes,e)}toNode(){var e=document.createElement("img");e.src=this.src,e.alt=this.alt,e.className="mord";for(var t in this.style)this.style.hasOwnProperty(t)&&(e.style[t]=this.style[t]);return e}toMarkup(){var e='<img src="'+q.escape(this.src)+'"'+(' alt="'+q.escape(this.alt)+'"'),t="";for(var a in this.style)this.style.hasOwnProperty(a)&&(t+=q.hyphenate(a)+":"+this.style[a]+";");return t&&(e+=' style="'+q.escape(t)+'"'),e+="'/>",e}}var S1={î:"ı̂",ï:"ı̈",í:"ı́",ì:"ı̀"};class M0{constructor(e,t,a,n,l,o,h,c){this.text=void 0,this.height=void 0,this.depth=void 0,this.italic=void 0,this.skew=void 0,this.width=void 0,this.maxFontSize=void 0,this.classes=void 0,this.style=void 0,this.text=e,this.height=t||0,this.depth=a||0,this.italic=n||0,this.skew=l||0,this.width=o||0,this.classes=h||[],this.style=c||{},this.maxFontSize=0;var p=s1(this.text.charCodeAt(0));p&&this.classes.push(p+"_fallback"),/[îïíì]/.test(this.text)&&(this.text=S1[this.text])}hasClass(e){return q.contains(this.classes,e)}toNode(){var e=document.createTextNode(this.text),t=null;this.italic>0&&(t=document.createElement("span"),t.style.marginRight=T(this.italic)),this.classes.length>0&&(t=t||document.createElement("span"),t.className=W0(this.classes));for(var a in this.style)this.style.hasOwnProperty(a)&&(t=t||document.createElement("span"),t.style[a]=this.style[a]);return t?(t.appendChild(e),t):e}toMarkup(){var e=!1,t="<span";this.classes.length&&(e=!0,t+=' class="',t+=q.escape(W0(this.classes)),t+='"');var a="";this.italic>0&&(a+="margin-right:"+this.italic+"em;");for(var n in this.style)this.style.hasOwnProperty(n)&&(a+=q.hyphenate(n)+":"+this.style[n]+";");a&&(e=!0,t+=' style="'+q.escape(a)+'"');var l=q.escape(this.text);return e?(t+=">",t+=l,t+="</span>",t):l}}class X0{constructor(e,t){this.children=void 0,this.attributes=void 0,this.children=e||[],this.attributes=t||{}}toNode(){var e="http://www.w3.org/2000/svg",t=document.createElementNS(e,"svg");for(var a in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,a)&&t.setAttribute(a,this.attributes[a]);for(var n=0;n<this.children.length;n++)t.appendChild(this.children[n].toNode());return t}toMarkup(){var e='<svg xmlns="http://www.w3.org/2000/svg"';for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="'+q.escape(this.attributes[t])+'"');e+=">";for(var a=0;a<this.children.length;a++)e+=this.children[a].toMarkup();return e+="</svg>",e}}class Q0{constructor(e,t){this.pathName=void 0,this.alternate=void 0,this.pathName=e,this.alternate=t}toNode(){var e="http://www.w3.org/2000/svg",t=document.createElementNS(e,"path");return this.alternate?t.setAttribute("d",this.alternate):t.setAttribute("d",or[this.pathName]),t}toMarkup(){return this.alternate?'<path d="'+q.escape(this.alternate)+'"/>':'<path d="'+q.escape(or[this.pathName])+'"/>'}}class mr{constructor(e){this.attributes=void 0,this.attributes=e||{}}toNode(){var e="http://www.w3.org/2000/svg",t=document.createElementNS(e,"line");for(var a in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,a)&&t.setAttribute(a,this.attributes[a]);return t}toMarkup(){var e="<line";for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="'+q.escape(this.attributes[t])+'"');return e+="/>",e}}function dr(r){if(r instanceof M0)return r;throw new Error("Expected symbolNode but got "+String(r)+".")}function M1(r){if(r instanceof Ue)return r;throw new Error("Expected span<HtmlDomNode> but got "+String(r)+".")}var z1={bin:1,close:1,inner:1,open:1,punct:1,rel:1},A1={"accent-token":1,mathord:1,"op-token":1,spacing:1,textord:1},K={math:{},text:{}};function i(r,e,t,a,n,l){K[r][n]={font:e,group:t,replace:a},l&&a&&(K[r][a]=K[r][n])}var s="math",k="text",u="main",d="ams",Z="accent-token",B="bin",u0="close",me="inner",R="mathord",a0="op-token",g0="open",Ge="punct",f="rel",F0="spacing",v="textord";i(s,u,f,"≡","\\equiv",!0);i(s,u,f,"≺","\\prec",!0);i(s,u,f,"≻","\\succ",!0);i(s,u,f,"∼","\\sim",!0);i(s,u,f,"⊥","\\perp");i(s,u,f,"⪯","\\preceq",!0);i(s,u,f,"⪰","\\succeq",!0);i(s,u,f,"≃","\\simeq",!0);i(s,u,f,"∣","\\mid",!0);i(s,u,f,"≪","\\ll",!0);i(s,u,f,"≫","\\gg",!0);i(s,u,f,"≍","\\asymp",!0);i(s,u,f,"∥","\\parallel");i(s,u,f,"⋈","\\bowtie",!0);i(s,u,f,"⌣","\\smile",!0);i(s,u,f,"⊑","\\sqsubseteq",!0);i(s,u,f,"⊒","\\sqsupseteq",!0);i(s,u,f,"≐","\\doteq",!0);i(s,u,f,"⌢","\\frown",!0);i(s,u,f,"∋","\\ni",!0);i(s,u,f,"∝","\\propto",!0);i(s,u,f,"⊢","\\vdash",!0);i(s,u,f,"⊣","\\dashv",!0);i(s,u,f,"∋","\\owns");i(s,u,Ge,".","\\ldotp");i(s,u,Ge,"⋅","\\cdotp");i(s,u,v,"#","\\#");i(k,u,v,"#","\\#");i(s,u,v,"&","\\&");i(k,u,v,"&","\\&");i(s,u,v,"ℵ","\\aleph",!0);i(s,u,v,"∀","\\forall",!0);i(s,u,v,"ℏ","\\hbar",!0);i(s,u,v,"∃","\\exists",!0);i(s,u,v,"∇","\\nabla",!0);i(s,u,v,"♭","\\flat",!0);i(s,u,v,"ℓ","\\ell",!0);i(s,u,v,"♮","\\natural",!0);i(s,u,v,"♣","\\clubsuit",!0);i(s,u,v,"℘","\\wp",!0);i(s,u,v,"♯","\\sharp",!0);i(s,u,v,"♢","\\diamondsuit",!0);i(s,u,v,"ℜ","\\Re",!0);i(s,u,v,"♡","\\heartsuit",!0);i(s,u,v,"ℑ","\\Im",!0);i(s,u,v,"♠","\\spadesuit",!0);i(s,u,v,"§","\\S",!0);i(k,u,v,"§","\\S");i(s,u,v,"¶","\\P",!0);i(k,u,v,"¶","\\P");i(s,u,v,"†","\\dag");i(k,u,v,"†","\\dag");i(k,u,v,"†","\\textdagger");i(s,u,v,"‡","\\ddag");i(k,u,v,"‡","\\ddag");i(k,u,v,"‡","\\textdaggerdbl");i(s,u,u0,"⎱","\\rmoustache",!0);i(s,u,g0,"⎰","\\lmoustache",!0);i(s,u,u0,"⟯","\\rgroup",!0);i(s,u,g0,"⟮","\\lgroup",!0);i(s,u,B,"∓","\\mp",!0);i(s,u,B,"⊖","\\ominus",!0);i(s,u,B,"⊎","\\uplus",!0);i(s,u,B,"⊓","\\sqcap",!0);i(s,u,B,"∗","\\ast");i(s,u,B,"⊔","\\sqcup",!0);i(s,u,B,"◯","\\bigcirc",!0);i(s,u,B,"∙","\\bullet",!0);i(s,u,B,"‡","\\ddagger");i(s,u,B,"≀","\\wr",!0);i(s,u,B,"⨿","\\amalg");i(s,u,B,"&","\\And");i(s,u,f,"⟵","\\longleftarrow",!0);i(s,u,f,"⇐","\\Leftarrow",!0);i(s,u,f,"⟸","\\Longleftarrow",!0);i(s,u,f,"⟶","\\longrightarrow",!0);i(s,u,f,"⇒","\\Rightarrow",!0);i(s,u,f,"⟹","\\Longrightarrow",!0);i(s,u,f,"↔","\\leftrightarrow",!0);i(s,u,f,"⟷","\\longleftrightarrow",!0);i(s,u,f,"⇔","\\Leftrightarrow",!0);i(s,u,f,"⟺","\\Longleftrightarrow",!0);i(s,u,f,"↦","\\mapsto",!0);i(s,u,f,"⟼","\\longmapsto",!0);i(s,u,f,"↗","\\nearrow",!0);i(s,u,f,"↩","\\hookleftarrow",!0);i(s,u,f,"↪","\\hookrightarrow",!0);i(s,u,f,"↘","\\searrow",!0);i(s,u,f,"↼","\\leftharpoonup",!0);i(s,u,f,"⇀","\\rightharpoonup",!0);i(s,u,f,"↙","\\swarrow",!0);i(s,u,f,"↽","\\leftharpoondown",!0);i(s,u,f,"⇁","\\rightharpoondown",!0);i(s,u,f,"↖","\\nwarrow",!0);i(s,u,f,"⇌","\\rightleftharpoons",!0);i(s,d,f,"≮","\\nless",!0);i(s,d,f,"","\\@nleqslant");i(s,d,f,"","\\@nleqq");i(s,d,f,"⪇","\\lneq",!0);i(s,d,f,"≨","\\lneqq",!0);i(s,d,f,"","\\@lvertneqq");i(s,d,f,"⋦","\\lnsim",!0);i(s,d,f,"⪉","\\lnapprox",!0);i(s,d,f,"⊀","\\nprec",!0);i(s,d,f,"⋠","\\npreceq",!0);i(s,d,f,"⋨","\\precnsim",!0);i(s,d,f,"⪹","\\precnapprox",!0);i(s,d,f,"≁","\\nsim",!0);i(s,d,f,"","\\@nshortmid");i(s,d,f,"∤","\\nmid",!0);i(s,d,f,"⊬","\\nvdash",!0);i(s,d,f,"⊭","\\nvDash",!0);i(s,d,f,"⋪","\\ntriangleleft");i(s,d,f,"⋬","\\ntrianglelefteq",!0);i(s,d,f,"⊊","\\subsetneq",!0);i(s,d,f,"","\\@varsubsetneq");i(s,d,f,"⫋","\\subsetneqq",!0);i(s,d,f,"","\\@varsubsetneqq");i(s,d,f,"≯","\\ngtr",!0);i(s,d,f,"","\\@ngeqslant");i(s,d,f,"","\\@ngeqq");i(s,d,f,"⪈","\\gneq",!0);i(s,d,f,"≩","\\gneqq",!0);i(s,d,f,"","\\@gvertneqq");i(s,d,f,"⋧","\\gnsim",!0);i(s,d,f,"⪊","\\gnapprox",!0);i(s,d,f,"⊁","\\nsucc",!0);i(s,d,f,"⋡","\\nsucceq",!0);i(s,d,f,"⋩","\\succnsim",!0);i(s,d,f,"⪺","\\succnapprox",!0);i(s,d,f,"≆","\\ncong",!0);i(s,d,f,"","\\@nshortparallel");i(s,d,f,"∦","\\nparallel",!0);i(s,d,f,"⊯","\\nVDash",!0);i(s,d,f,"⋫","\\ntriangleright");i(s,d,f,"⋭","\\ntrianglerighteq",!0);i(s,d,f,"","\\@nsupseteqq");i(s,d,f,"⊋","\\supsetneq",!0);i(s,d,f,"","\\@varsupsetneq");i(s,d,f,"⫌","\\supsetneqq",!0);i(s,d,f,"","\\@varsupsetneqq");i(s,d,f,"⊮","\\nVdash",!0);i(s,d,f,"⪵","\\precneqq",!0);i(s,d,f,"⪶","\\succneqq",!0);i(s,d,f,"","\\@nsubseteqq");i(s,d,B,"⊴","\\unlhd");i(s,d,B,"⊵","\\unrhd");i(s,d,f,"↚","\\nleftarrow",!0);i(s,d,f,"↛","\\nrightarrow",!0);i(s,d,f,"⇍","\\nLeftarrow",!0);i(s,d,f,"⇏","\\nRightarrow",!0);i(s,d,f,"↮","\\nleftrightarrow",!0);i(s,d,f,"⇎","\\nLeftrightarrow",!0);i(s,d,f,"△","\\vartriangle");i(s,d,v,"ℏ","\\hslash");i(s,d,v,"▽","\\triangledown");i(s,d,v,"◊","\\lozenge");i(s,d,v,"Ⓢ","\\circledS");i(s,d,v,"®","\\circledR");i(k,d,v,"®","\\circledR");i(s,d,v,"∡","\\measuredangle",!0);i(s,d,v,"∄","\\nexists");i(s,d,v,"℧","\\mho");i(s,d,v,"Ⅎ","\\Finv",!0);i(s,d,v,"⅁","\\Game",!0);i(s,d,v,"‵","\\backprime");i(s,d,v,"▲","\\blacktriangle");i(s,d,v,"▼","\\blacktriangledown");i(s,d,v,"■","\\blacksquare");i(s,d,v,"⧫","\\blacklozenge");i(s,d,v,"★","\\bigstar");i(s,d,v,"∢","\\sphericalangle",!0);i(s,d,v,"∁","\\complement",!0);i(s,d,v,"ð","\\eth",!0);i(k,u,v,"ð","ð");i(s,d,v,"╱","\\diagup");i(s,d,v,"╲","\\diagdown");i(s,d,v,"□","\\square");i(s,d,v,"□","\\Box");i(s,d,v,"◊","\\Diamond");i(s,d,v,"¥","\\yen",!0);i(k,d,v,"¥","\\yen",!0);i(s,d,v,"✓","\\checkmark",!0);i(k,d,v,"✓","\\checkmark");i(s,d,v,"ℶ","\\beth",!0);i(s,d,v,"ℸ","\\daleth",!0);i(s,d,v,"ℷ","\\gimel",!0);i(s,d,v,"ϝ","\\digamma",!0);i(s,d,v,"ϰ","\\varkappa");i(s,d,g0,"┌","\\@ulcorner",!0);i(s,d,u0,"┐","\\@urcorner",!0);i(s,d,g0,"└","\\@llcorner",!0);i(s,d,u0,"┘","\\@lrcorner",!0);i(s,d,f,"≦","\\leqq",!0);i(s,d,f,"⩽","\\leqslant",!0);i(s,d,f,"⪕","\\eqslantless",!0);i(s,d,f,"≲","\\lesssim",!0);i(s,d,f,"⪅","\\lessapprox",!0);i(s,d,f,"≊","\\approxeq",!0);i(s,d,B,"⋖","\\lessdot");i(s,d,f,"⋘","\\lll",!0);i(s,d,f,"≶","\\lessgtr",!0);i(s,d,f,"⋚","\\lesseqgtr",!0);i(s,d,f,"⪋","\\lesseqqgtr",!0);i(s,d,f,"≑","\\doteqdot");i(s,d,f,"≓","\\risingdotseq",!0);i(s,d,f,"≒","\\fallingdotseq",!0);i(s,d,f,"∽","\\backsim",!0);i(s,d,f,"⋍","\\backsimeq",!0);i(s,d,f,"⫅","\\subseteqq",!0);i(s,d,f,"⋐","\\Subset",!0);i(s,d,f,"⊏","\\sqsubset",!0);i(s,d,f,"≼","\\preccurlyeq",!0);i(s,d,f,"⋞","\\curlyeqprec",!0);i(s,d,f,"≾","\\precsim",!0);i(s,d,f,"⪷","\\precapprox",!0);i(s,d,f,"⊲","\\vartriangleleft");i(s,d,f,"⊴","\\trianglelefteq");i(s,d,f,"⊨","\\vDash",!0);i(s,d,f,"⊪","\\Vvdash",!0);i(s,d,f,"⌣","\\smallsmile");i(s,d,f,"⌢","\\smallfrown");i(s,d,f,"≏","\\bumpeq",!0);i(s,d,f,"≎","\\Bumpeq",!0);i(s,d,f,"≧","\\geqq",!0);i(s,d,f,"⩾","\\geqslant",!0);i(s,d,f,"⪖","\\eqslantgtr",!0);i(s,d,f,"≳","\\gtrsim",!0);i(s,d,f,"⪆","\\gtrapprox",!0);i(s,d,B,"⋗","\\gtrdot");i(s,d,f,"⋙","\\ggg",!0);i(s,d,f,"≷","\\gtrless",!0);i(s,d,f,"⋛","\\gtreqless",!0);i(s,d,f,"⪌","\\gtreqqless",!0);i(s,d,f,"≖","\\eqcirc",!0);i(s,d,f,"≗","\\circeq",!0);i(s,d,f,"≜","\\triangleq",!0);i(s,d,f,"∼","\\thicksim");i(s,d,f,"≈","\\thickapprox");i(s,d,f,"⫆","\\supseteqq",!0);i(s,d,f,"⋑","\\Supset",!0);i(s,d,f,"⊐","\\sqsupset",!0);i(s,d,f,"≽","\\succcurlyeq",!0);i(s,d,f,"⋟","\\curlyeqsucc",!0);i(s,d,f,"≿","\\succsim",!0);i(s,d,f,"⪸","\\succapprox",!0);i(s,d,f,"⊳","\\vartriangleright");i(s,d,f,"⊵","\\trianglerighteq");i(s,d,f,"⊩","\\Vdash",!0);i(s,d,f,"∣","\\shortmid");i(s,d,f,"∥","\\shortparallel");i(s,d,f,"≬","\\between",!0);i(s,d,f,"⋔","\\pitchfork",!0);i(s,d,f,"∝","\\varpropto");i(s,d,f,"◀","\\blacktriangleleft");i(s,d,f,"∴","\\therefore",!0);i(s,d,f,"∍","\\backepsilon");i(s,d,f,"▶","\\blacktriangleright");i(s,d,f,"∵","\\because",!0);i(s,d,f,"⋘","\\llless");i(s,d,f,"⋙","\\gggtr");i(s,d,B,"⊲","\\lhd");i(s,d,B,"⊳","\\rhd");i(s,d,f,"≂","\\eqsim",!0);i(s,u,f,"⋈","\\Join");i(s,d,f,"≑","\\Doteq",!0);i(s,d,B,"∔","\\dotplus",!0);i(s,d,B,"∖","\\smallsetminus");i(s,d,B,"⋒","\\Cap",!0);i(s,d,B,"⋓","\\Cup",!0);i(s,d,B,"⩞","\\doublebarwedge",!0);i(s,d,B,"⊟","\\boxminus",!0);i(s,d,B,"⊞","\\boxplus",!0);i(s,d,B,"⋇","\\divideontimes",!0);i(s,d,B,"⋉","\\ltimes",!0);i(s,d,B,"⋊","\\rtimes",!0);i(s,d,B,"⋋","\\leftthreetimes",!0);i(s,d,B,"⋌","\\rightthreetimes",!0);i(s,d,B,"⋏","\\curlywedge",!0);i(s,d,B,"⋎","\\curlyvee",!0);i(s,d,B,"⊝","\\circleddash",!0);i(s,d,B,"⊛","\\circledast",!0);i(s,d,B,"⋅","\\centerdot");i(s,d,B,"⊺","\\intercal",!0);i(s,d,B,"⋒","\\doublecap");i(s,d,B,"⋓","\\doublecup");i(s,d,B,"⊠","\\boxtimes",!0);i(s,d,f,"⇢","\\dashrightarrow",!0);i(s,d,f,"⇠","\\dashleftarrow",!0);i(s,d,f,"⇇","\\leftleftarrows",!0);i(s,d,f,"⇆","\\leftrightarrows",!0);i(s,d,f,"⇚","\\Lleftarrow",!0);i(s,d,f,"↞","\\twoheadleftarrow",!0);i(s,d,f,"↢","\\leftarrowtail",!0);i(s,d,f,"↫","\\looparrowleft",!0);i(s,d,f,"⇋","\\leftrightharpoons",!0);i(s,d,f,"↶","\\curvearrowleft",!0);i(s,d,f,"↺","\\circlearrowleft",!0);i(s,d,f,"↰","\\Lsh",!0);i(s,d,f,"⇈","\\upuparrows",!0);i(s,d,f,"↿","\\upharpoonleft",!0);i(s,d,f,"⇃","\\downharpoonleft",!0);i(s,u,f,"⊶","\\origof",!0);i(s,u,f,"⊷","\\imageof",!0);i(s,d,f,"⊸","\\multimap",!0);i(s,d,f,"↭","\\leftrightsquigarrow",!0);i(s,d,f,"⇉","\\rightrightarrows",!0);i(s,d,f,"⇄","\\rightleftarrows",!0);i(s,d,f,"↠","\\twoheadrightarrow",!0);i(s,d,f,"↣","\\rightarrowtail",!0);i(s,d,f,"↬","\\looparrowright",!0);i(s,d,f,"↷","\\curvearrowright",!0);i(s,d,f,"↻","\\circlearrowright",!0);i(s,d,f,"↱","\\Rsh",!0);i(s,d,f,"⇊","\\downdownarrows",!0);i(s,d,f,"↾","\\upharpoonright",!0);i(s,d,f,"⇂","\\downharpoonright",!0);i(s,d,f,"⇝","\\rightsquigarrow",!0);i(s,d,f,"⇝","\\leadsto");i(s,d,f,"⇛","\\Rrightarrow",!0);i(s,d,f,"↾","\\restriction");i(s,u,v,"‘","`");i(s,u,v,"$","\\$");i(k,u,v,"$","\\$");i(k,u,v,"$","\\textdollar");i(s,u,v,"%","\\%");i(k,u,v,"%","\\%");i(s,u,v,"_","\\_");i(k,u,v,"_","\\_");i(k,u,v,"_","\\textunderscore");i(s,u,v,"∠","\\angle",!0);i(s,u,v,"∞","\\infty",!0);i(s,u,v,"′","\\prime");i(s,u,v,"△","\\triangle");i(s,u,v,"Γ","\\Gamma",!0);i(s,u,v,"Δ","\\Delta",!0);i(s,u,v,"Θ","\\Theta",!0);i(s,u,v,"Λ","\\Lambda",!0);i(s,u,v,"Ξ","\\Xi",!0);i(s,u,v,"Π","\\Pi",!0);i(s,u,v,"Σ","\\Sigma",!0);i(s,u,v,"Υ","\\Upsilon",!0);i(s,u,v,"Φ","\\Phi",!0);i(s,u,v,"Ψ","\\Psi",!0);i(s,u,v,"Ω","\\Omega",!0);i(s,u,v,"A","Α");i(s,u,v,"B","Β");i(s,u,v,"E","Ε");i(s,u,v,"Z","Ζ");i(s,u,v,"H","Η");i(s,u,v,"I","Ι");i(s,u,v,"K","Κ");i(s,u,v,"M","Μ");i(s,u,v,"N","Ν");i(s,u,v,"O","Ο");i(s,u,v,"P","Ρ");i(s,u,v,"T","Τ");i(s,u,v,"X","Χ");i(s,u,v,"¬","\\neg",!0);i(s,u,v,"¬","\\lnot");i(s,u,v,"⊤","\\top");i(s,u,v,"⊥","\\bot");i(s,u,v,"∅","\\emptyset");i(s,d,v,"∅","\\varnothing");i(s,u,R,"α","\\alpha",!0);i(s,u,R,"β","\\beta",!0);i(s,u,R,"γ","\\gamma",!0);i(s,u,R,"δ","\\delta",!0);i(s,u,R,"ϵ","\\epsilon",!0);i(s,u,R,"ζ","\\zeta",!0);i(s,u,R,"η","\\eta",!0);i(s,u,R,"θ","\\theta",!0);i(s,u,R,"ι","\\iota",!0);i(s,u,R,"κ","\\kappa",!0);i(s,u,R,"λ","\\lambda",!0);i(s,u,R,"μ","\\mu",!0);i(s,u,R,"ν","\\nu",!0);i(s,u,R,"ξ","\\xi",!0);i(s,u,R,"ο","\\omicron",!0);i(s,u,R,"π","\\pi",!0);i(s,u,R,"ρ","\\rho",!0);i(s,u,R,"σ","\\sigma",!0);i(s,u,R,"τ","\\tau",!0);i(s,u,R,"υ","\\upsilon",!0);i(s,u,R,"ϕ","\\phi",!0);i(s,u,R,"χ","\\chi",!0);i(s,u,R,"ψ","\\psi",!0);i(s,u,R,"ω","\\omega",!0);i(s,u,R,"ε","\\varepsilon",!0);i(s,u,R,"ϑ","\\vartheta",!0);i(s,u,R,"ϖ","\\varpi",!0);i(s,u,R,"ϱ","\\varrho",!0);i(s,u,R,"ς","\\varsigma",!0);i(s,u,R,"φ","\\varphi",!0);i(s,u,B,"∗","*",!0);i(s,u,B,"+","+");i(s,u,B,"−","-",!0);i(s,u,B,"⋅","\\cdot",!0);i(s,u,B,"∘","\\circ",!0);i(s,u,B,"÷","\\div",!0);i(s,u,B,"±","\\pm",!0);i(s,u,B,"×","\\times",!0);i(s,u,B,"∩","\\cap",!0);i(s,u,B,"∪","\\cup",!0);i(s,u,B,"∖","\\setminus",!0);i(s,u,B,"∧","\\land");i(s,u,B,"∨","\\lor");i(s,u,B,"∧","\\wedge",!0);i(s,u,B,"∨","\\vee",!0);i(s,u,v,"√","\\surd");i(s,u,g0,"⟨","\\langle",!0);i(s,u,g0,"∣","\\lvert");i(s,u,g0,"∥","\\lVert");i(s,u,u0,"?","?");i(s,u,u0,"!","!");i(s,u,u0,"⟩","\\rangle",!0);i(s,u,u0,"∣","\\rvert");i(s,u,u0,"∥","\\rVert");i(s,u,f,"=","=");i(s,u,f,":",":");i(s,u,f,"≈","\\approx",!0);i(s,u,f,"≅","\\cong",!0);i(s,u,f,"≥","\\ge");i(s,u,f,"≥","\\geq",!0);i(s,u,f,"←","\\gets");i(s,u,f,">","\\gt",!0);i(s,u,f,"∈","\\in",!0);i(s,u,f,"","\\@not");i(s,u,f,"⊂","\\subset",!0);i(s,u,f,"⊃","\\supset",!0);i(s,u,f,"⊆","\\subseteq",!0);i(s,u,f,"⊇","\\supseteq",!0);i(s,d,f,"⊈","\\nsubseteq",!0);i(s,d,f,"⊉","\\nsupseteq",!0);i(s,u,f,"⊨","\\models");i(s,u,f,"←","\\leftarrow",!0);i(s,u,f,"≤","\\le");i(s,u,f,"≤","\\leq",!0);i(s,u,f,"<","\\lt",!0);i(s,u,f,"→","\\rightarrow",!0);i(s,u,f,"→","\\to");i(s,d,f,"≱","\\ngeq",!0);i(s,d,f,"≰","\\nleq",!0);i(s,u,F0," ","\\ ");i(s,u,F0," ","\\space");i(s,u,F0," ","\\nobreakspace");i(k,u,F0," ","\\ ");i(k,u,F0," "," ");i(k,u,F0," ","\\space");i(k,u,F0," ","\\nobreakspace");i(s,u,F0,null,"\\nobreak");i(s,u,F0,null,"\\allowbreak");i(s,u,Ge,",",",");i(s,u,Ge,";",";");i(s,d,B,"⊼","\\barwedge",!0);i(s,d,B,"⊻","\\veebar",!0);i(s,u,B,"⊙","\\odot",!0);i(s,u,B,"⊕","\\oplus",!0);i(s,u,B,"⊗","\\otimes",!0);i(s,u,v,"∂","\\partial",!0);i(s,u,B,"⊘","\\oslash",!0);i(s,d,B,"⊚","\\circledcirc",!0);i(s,d,B,"⊡","\\boxdot",!0);i(s,u,B,"△","\\bigtriangleup");i(s,u,B,"▽","\\bigtriangledown");i(s,u,B,"†","\\dagger");i(s,u,B,"⋄","\\diamond");i(s,u,B,"⋆","\\star");i(s,u,B,"◃","\\triangleleft");i(s,u,B,"▹","\\triangleright");i(s,u,g0,"{","\\{");i(k,u,v,"{","\\{");i(k,u,v,"{","\\textbraceleft");i(s,u,u0,"}","\\}");i(k,u,v,"}","\\}");i(k,u,v,"}","\\textbraceright");i(s,u,g0,"{","\\lbrace");i(s,u,u0,"}","\\rbrace");i(s,u,g0,"[","\\lbrack",!0);i(k,u,v,"[","\\lbrack",!0);i(s,u,u0,"]","\\rbrack",!0);i(k,u,v,"]","\\rbrack",!0);i(s,u,g0,"(","\\lparen",!0);i(s,u,u0,")","\\rparen",!0);i(k,u,v,"<","\\textless",!0);i(k,u,v,">","\\textgreater",!0);i(s,u,g0,"⌊","\\lfloor",!0);i(s,u,u0,"⌋","\\rfloor",!0);i(s,u,g0,"⌈","\\lceil",!0);i(s,u,u0,"⌉","\\rceil",!0);i(s,u,v,"\\","\\backslash");i(s,u,v,"∣","|");i(s,u,v,"∣","\\vert");i(k,u,v,"|","\\textbar",!0);i(s,u,v,"∥","\\|");i(s,u,v,"∥","\\Vert");i(k,u,v,"∥","\\textbardbl");i(k,u,v,"~","\\textasciitilde");i(k,u,v,"\\","\\textbackslash");i(k,u,v,"^","\\textasciicircum");i(s,u,f,"↑","\\uparrow",!0);i(s,u,f,"⇑","\\Uparrow",!0);i(s,u,f,"↓","\\downarrow",!0);i(s,u,f,"⇓","\\Downarrow",!0);i(s,u,f,"↕","\\updownarrow",!0);i(s,u,f,"⇕","\\Updownarrow",!0);i(s,u,a0,"∐","\\coprod");i(s,u,a0,"⋁","\\bigvee");i(s,u,a0,"⋀","\\bigwedge");i(s,u,a0,"⨄","\\biguplus");i(s,u,a0,"⋂","\\bigcap");i(s,u,a0,"⋃","\\bigcup");i(s,u,a0,"∫","\\int");i(s,u,a0,"∫","\\intop");i(s,u,a0,"∬","\\iint");i(s,u,a0,"∭","\\iiint");i(s,u,a0,"∏","\\prod");i(s,u,a0,"∑","\\sum");i(s,u,a0,"⨂","\\bigotimes");i(s,u,a0,"⨁","\\bigoplus");i(s,u,a0,"⨀","\\bigodot");i(s,u,a0,"∮","\\oint");i(s,u,a0,"∯","\\oiint");i(s,u,a0,"∰","\\oiiint");i(s,u,a0,"⨆","\\bigsqcup");i(s,u,a0,"∫","\\smallint");i(k,u,me,"…","\\textellipsis");i(s,u,me,"…","\\mathellipsis");i(k,u,me,"…","\\ldots",!0);i(s,u,me,"…","\\ldots",!0);i(s,u,me,"⋯","\\@cdots",!0);i(s,u,me,"⋱","\\ddots",!0);i(s,u,v,"⋮","\\varvdots");i(k,u,v,"⋮","\\varvdots");i(s,u,Z,"ˊ","\\acute");i(s,u,Z,"ˋ","\\grave");i(s,u,Z,"¨","\\ddot");i(s,u,Z,"~","\\tilde");i(s,u,Z,"ˉ","\\bar");i(s,u,Z,"˘","\\breve");i(s,u,Z,"ˇ","\\check");i(s,u,Z,"^","\\hat");i(s,u,Z,"⃗","\\vec");i(s,u,Z,"˙","\\dot");i(s,u,Z,"˚","\\mathring");i(s,u,R,"","\\@imath");i(s,u,R,"","\\@jmath");i(s,u,v,"ı","ı");i(s,u,v,"ȷ","ȷ");i(k,u,v,"ı","\\i",!0);i(k,u,v,"ȷ","\\j",!0);i(k,u,v,"ß","\\ss",!0);i(k,u,v,"æ","\\ae",!0);i(k,u,v,"œ","\\oe",!0);i(k,u,v,"ø","\\o",!0);i(k,u,v,"Æ","\\AE",!0);i(k,u,v,"Œ","\\OE",!0);i(k,u,v,"Ø","\\O",!0);i(k,u,Z,"ˊ","\\'");i(k,u,Z,"ˋ","\\`");i(k,u,Z,"ˆ","\\^");i(k,u,Z,"˜","\\~");i(k,u,Z,"ˉ","\\=");i(k,u,Z,"˘","\\u");i(k,u,Z,"˙","\\.");i(k,u,Z,"¸","\\c");i(k,u,Z,"˚","\\r");i(k,u,Z,"ˇ","\\v");i(k,u,Z,"¨",'\\"');i(k,u,Z,"˝","\\H");i(k,u,Z,"◯","\\textcircled");var ma={"--":!0,"---":!0,"``":!0,"''":!0};i(k,u,v,"–","--",!0);i(k,u,v,"–","\\textendash");i(k,u,v,"—","---",!0);i(k,u,v,"—","\\textemdash");i(k,u,v,"‘","`",!0);i(k,u,v,"‘","\\textquoteleft");i(k,u,v,"’","'",!0);i(k,u,v,"’","\\textquoteright");i(k,u,v,"“","``",!0);i(k,u,v,"“","\\textquotedblleft");i(k,u,v,"”","''",!0);i(k,u,v,"”","\\textquotedblright");i(s,u,v,"°","\\degree",!0);i(k,u,v,"°","\\degree");i(k,u,v,"°","\\textdegree",!0);i(s,u,v,"£","\\pounds");i(s,u,v,"£","\\mathsterling",!0);i(k,u,v,"£","\\pounds");i(k,u,v,"£","\\textsterling",!0);i(s,d,v,"✠","\\maltese");i(k,d,v,"✠","\\maltese");var fr='0123456789/@."';for(var nt=0;nt<fr.length;nt++){var pr=fr.charAt(nt);i(s,u,v,pr,pr)}var vr='0123456789!@*()-=+";:?/.,';for(var lt=0;lt<vr.length;lt++){var gr=vr.charAt(lt);i(k,u,v,gr,gr)}var Fe="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";for(var it=0;it<Fe.length;it++){var Ce=Fe.charAt(it);i(s,u,R,Ce,Ce),i(k,u,v,Ce,Ce)}i(s,d,v,"C","ℂ");i(k,d,v,"C","ℂ");i(s,d,v,"H","ℍ");i(k,d,v,"H","ℍ");i(s,d,v,"N","ℕ");i(k,d,v,"N","ℕ");i(s,d,v,"P","ℙ");i(k,d,v,"P","ℙ");i(s,d,v,"Q","ℚ");i(k,d,v,"Q","ℚ");i(s,d,v,"R","ℝ");i(k,d,v,"R","ℝ");i(s,d,v,"Z","ℤ");i(k,d,v,"Z","ℤ");i(s,u,R,"h","ℎ");i(k,u,R,"h","ℎ");var I="";for(var s0=0;s0<Fe.length;s0++){var e0=Fe.charAt(s0);I=String.fromCharCode(55349,56320+s0),i(s,u,R,e0,I),i(k,u,v,e0,I),I=String.fromCharCode(55349,56372+s0),i(s,u,R,e0,I),i(k,u,v,e0,I),I=String.fromCharCode(55349,56424+s0),i(s,u,R,e0,I),i(k,u,v,e0,I),I=String.fromCharCode(55349,56580+s0),i(s,u,R,e0,I),i(k,u,v,e0,I),I=String.fromCharCode(55349,56684+s0),i(s,u,R,e0,I),i(k,u,v,e0,I),I=String.fromCharCode(55349,56736+s0),i(s,u,R,e0,I),i(k,u,v,e0,I),I=String.fromCharCode(55349,56788+s0),i(s,u,R,e0,I),i(k,u,v,e0,I),I=String.fromCharCode(55349,56840+s0),i(s,u,R,e0,I),i(k,u,v,e0,I),I=String.fromCharCode(55349,56944+s0),i(s,u,R,e0,I),i(k,u,v,e0,I),s0<26&&(I=String.fromCharCode(55349,56632+s0),i(s,u,R,e0,I),i(k,u,v,e0,I),I=String.fromCharCode(55349,56476+s0),i(s,u,R,e0,I),i(k,u,v,e0,I))}I="𝕜";i(s,u,R,"k",I);i(k,u,v,"k",I);for(var Z0=0;Z0<10;Z0++){var G0=Z0.toString();I=String.fromCharCode(55349,57294+Z0),i(s,u,R,G0,I),i(k,u,v,G0,I),I=String.fromCharCode(55349,57314+Z0),i(s,u,R,G0,I),i(k,u,v,G0,I),I=String.fromCharCode(55349,57324+Z0),i(s,u,R,G0,I),i(k,u,v,G0,I),I=String.fromCharCode(55349,57334+Z0),i(s,u,R,G0,I),i(k,u,v,G0,I)}var Mt="ÐÞþ";for(var st=0;st<Mt.length;st++){var De=Mt.charAt(st);i(s,u,R,De,De),i(k,u,v,De,De)}var Be=[["mathbf","textbf","Main-Bold"],["mathbf","textbf","Main-Bold"],["mathnormal","textit","Math-Italic"],["mathnormal","textit","Math-Italic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["mathscr","textscr","Script-Regular"],["","",""],["","",""],["","",""],["mathfrak","textfrak","Fraktur-Regular"],["mathfrak","textfrak","Fraktur-Regular"],["mathbb","textbb","AMS-Regular"],["mathbb","textbb","AMS-Regular"],["mathboldfrak","textboldfrak","Fraktur-Regular"],["mathboldfrak","textboldfrak","Fraktur-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathitsf","textitsf","SansSerif-Italic"],["mathitsf","textitsf","SansSerif-Italic"],["","",""],["","",""],["mathtt","texttt","Typewriter-Regular"],["mathtt","texttt","Typewriter-Regular"]],br=[["mathbf","textbf","Main-Bold"],["","",""],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathtt","texttt","Typewriter-Regular"]],T1=function(e,t){var a=e.charCodeAt(0),n=e.charCodeAt(1),l=(a-55296)*1024+(n-56320)+65536,o=t==="math"?0:1;if(119808<=l&&l<120484){var h=Math.floor((l-119808)/26);return[Be[h][2],Be[h][o]]}else if(120782<=l&&l<=120831){var c=Math.floor((l-120782)/10);return[br[c][2],br[c][o]]}else{if(l===120485||l===120486)return[Be[0][2],Be[0][o]];if(120486<l&&l<120782)return["",""];throw new A("Unsupported character: "+e)}},Ye=function(e,t,a){return K[a][e]&&K[a][e].replace&&(e=K[a][e].replace),{value:e,metrics:Et(e,t,a)}},S0=function(e,t,a,n,l){var o=Ye(e,t,a),h=o.metrics;e=o.value;var c;if(h){var p=h.italic;(a==="text"||n&&n.font==="mathit")&&(p=0),c=new M0(e,h.height,h.depth,p,h.skew,h.width,l)}else typeof console<"u"&&console.warn("No character metrics "+("for '"+e+"' in style '"+t+"' and mode '"+a+"'")),c=new M0(e,0,0,0,0,0,l);if(n){c.maxFontSize=n.sizeMultiplier,n.style.isTight()&&c.classes.push("mtight");var g=n.getColor();g&&(c.style.color=g)}return c},C1=function(e,t,a,n){return n===void 0&&(n=[]),a.font==="boldsymbol"&&Ye(e,"Main-Bold",t).metrics?S0(e,"Main-Bold",t,a,n.concat(["mathbf"])):e==="\\"||K[t][e].font==="main"?S0(e,"Main-Regular",t,a,n):S0(e,"AMS-Regular",t,a,n.concat(["amsrm"]))},D1=function(e,t,a,n,l){return l!=="textord"&&Ye(e,"Math-BoldItalic",t).metrics?{fontName:"Math-BoldItalic",fontClass:"boldsymbol"}:{fontName:"Main-Bold",fontClass:"mathbf"}},B1=function(e,t,a){var n=e.mode,l=e.text,o=["mord"],h=n==="math"||n==="text"&&t.font,c=h?t.font:t.fontFamily,p="",g="";if(l.charCodeAt(0)===55349&&([p,g]=T1(l,n)),p.length>0)return S0(l,p,n,t,o.concat(g));if(c){var b,x;if(c==="boldsymbol"){var w=D1(l,n,t,o,a);b=w.fontName,x=[w.fontClass]}else h?(b=pa[c].fontName,x=[c]):(b=Ne(c,t.fontWeight,t.fontShape),x=[c,t.fontWeight,t.fontShape]);if(Ye(l,b,n).metrics)return S0(l,b,n,t,o.concat(x));if(ma.hasOwnProperty(l)&&b.slice(0,10)==="Typewriter"){for(var M=[],C=0;C<l.length;C++)M.push(S0(l[C],b,n,t,o.concat(x)));return fa(M)}}if(a==="mathord")return S0(l,"Math-Italic",n,t,o.concat(["mathnormal"]));if(a==="textord"){var N=K[n][l]&&K[n][l].font;if(N==="ams"){var E=Ne("amsrm",t.fontWeight,t.fontShape);return S0(l,E,n,t,o.concat("amsrm",t.fontWeight,t.fontShape))}else if(N==="main"||!N){var L=Ne("textrm",t.fontWeight,t.fontShape);return S0(l,L,n,t,o.concat(t.fontWeight,t.fontShape))}else{var F=Ne(N,t.fontWeight,t.fontShape);return S0(l,F,n,t,o.concat(F,t.fontWeight,t.fontShape))}}else throw new Error("unexpected type: "+a+" in makeOrd")},N1=(r,e)=>{if(W0(r.classes)!==W0(e.classes)||r.skew!==e.skew||r.maxFontSize!==e.maxFontSize)return!1;if(r.classes.length===1){var t=r.classes[0];if(t==="mbin"||t==="mord")return!1}for(var a in r.style)if(r.style.hasOwnProperty(a)&&r.style[a]!==e.style[a])return!1;for(var n in e.style)if(e.style.hasOwnProperty(n)&&r.style[n]!==e.style[n])return!1;return!0},E1=r=>{for(var e=0;e<r.length-1;e++){var t=r[e],a=r[e+1];t instanceof M0&&a instanceof M0&&N1(t,a)&&(t.text+=a.text,t.height=Math.max(t.height,a.height),t.depth=Math.max(t.depth,a.depth),t.italic=a.italic,r.splice(e+1,1),e--)}return r},qt=function(e){for(var t=0,a=0,n=0,l=0;l<e.children.length;l++){var o=e.children[l];o.height>t&&(t=o.height),o.depth>a&&(a=o.depth),o.maxFontSize>n&&(n=o.maxFontSize)}e.height=t,e.depth=a,e.maxFontSize=n},c0=function(e,t,a,n){var l=new Ue(e,t,a,n);return qt(l),l},da=(r,e,t,a)=>new Ue(r,e,t,a),q1=function(e,t,a){var n=c0([e],[],t);return n.height=Math.max(a||t.fontMetrics().defaultRuleThickness,t.minRuleThickness),n.style.borderBottomWidth=T(n.height),n.maxFontSize=1,n},R1=function(e,t,a,n){var l=new ca(e,t,a,n);return qt(l),l},fa=function(e){var t=new we(e);return qt(t),t},O1=function(e,t){return e instanceof we?c0([],[e],t):e},L1=function(e){if(e.positionType==="individualShift"){for(var t=e.children,a=[t[0]],n=-t[0].shift-t[0].elem.depth,l=n,o=1;o<t.length;o++){var h=-t[o].shift-l-t[o].elem.depth,c=h-(t[o-1].elem.height+t[o-1].elem.depth);l=l+h,a.push({type:"kern",size:c}),a.push(t[o])}return{children:a,depth:n}}var p;if(e.positionType==="top"){for(var g=e.positionData,b=0;b<e.children.length;b++){var x=e.children[b];g-=x.type==="kern"?x.size:x.elem.height+x.elem.depth}p=g}else if(e.positionType==="bottom")p=-e.positionData;else{var w=e.children[0];if(w.type!=="elem")throw new Error('First child must have type "elem".');if(e.positionType==="shift")p=-w.elem.depth-e.positionData;else if(e.positionType==="firstBaseline")p=-w.elem.depth;else throw new Error("Invalid positionType "+e.positionType+".")}return{children:e.children,depth:p}},I1=function(e,t){for(var{children:a,depth:n}=L1(e),l=0,o=0;o<a.length;o++){var h=a[o];if(h.type==="elem"){var c=h.elem;l=Math.max(l,c.maxFontSize,c.height)}}l+=2;var p=c0(["pstrut"],[]);p.style.height=T(l);for(var g=[],b=n,x=n,w=n,M=0;M<a.length;M++){var C=a[M];if(C.type==="kern")w+=C.size;else{var N=C.elem,E=C.wrapperClasses||[],L=C.wrapperStyle||{},F=c0(E,[p,N],void 0,L);F.style.top=T(-l-w-N.depth),C.marginLeft&&(F.style.marginLeft=C.marginLeft),C.marginRight&&(F.style.marginRight=C.marginRight),g.push(F),w+=N.height+N.depth}b=Math.min(b,w),x=Math.max(x,w)}var Y=c0(["vlist"],g);Y.style.height=T(x);var V;if(b<0){var W=c0([],[]),G=c0(["vlist"],[W]);G.style.height=T(-b);var J=c0(["vlist-s"],[new M0("​")]);V=[c0(["vlist-r"],[Y,J]),c0(["vlist-r"],[G])]}else V=[c0(["vlist-r"],[Y])];var X=c0(["vlist-t"],V);return V.length===2&&X.classes.push("vlist-t2"),X.height=x,X.depth=-b,X},F1=(r,e)=>{var t=c0(["mspace"],[],e),a=_(r,e);return t.style.marginRight=T(a),t},Ne=function(e,t,a){var n="";switch(e){case"amsrm":n="AMS";break;case"textrm":n="Main";break;case"textsf":n="SansSerif";break;case"texttt":n="Typewriter";break;default:n=e}var l;return t==="textbf"&&a==="textit"?l="BoldItalic":t==="textbf"?l="Bold":t==="textit"?l="Italic":l="Regular",n+"-"+l},pa={mathbf:{variant:"bold",fontName:"Main-Bold"},mathrm:{variant:"normal",fontName:"Main-Regular"},textit:{variant:"italic",fontName:"Main-Italic"},mathit:{variant:"italic",fontName:"Main-Italic"},mathnormal:{variant:"italic",fontName:"Math-Italic"},mathsfit:{variant:"sans-serif-italic",fontName:"SansSerif-Italic"},mathbb:{variant:"double-struck",fontName:"AMS-Regular"},mathcal:{variant:"script",fontName:"Caligraphic-Regular"},mathfrak:{variant:"fraktur",fontName:"Fraktur-Regular"},mathscr:{variant:"script",fontName:"Script-Regular"},mathsf:{variant:"sans-serif",fontName:"SansSerif-Regular"},mathtt:{variant:"monospace",fontName:"Typewriter-Regular"}},va={vec:["vec",.471,.714],oiintSize1:["oiintSize1",.957,.499],oiintSize2:["oiintSize2",1.472,.659],oiiintSize1:["oiiintSize1",1.304,.499],oiiintSize2:["oiiintSize2",1.98,.659]},P1=function(e,t){var[a,n,l]=va[e],o=new Q0(a),h=new X0([o],{width:T(n),height:T(l),style:"width:"+T(n),viewBox:"0 0 "+1e3*n+" "+1e3*l,preserveAspectRatio:"xMinYMin"}),c=da(["overlay"],[h],t);return c.height=l,c.style.height=T(l),c.style.width=T(n),c},y={fontMap:pa,makeSymbol:S0,mathsym:C1,makeSpan:c0,makeSvgSpan:da,makeLineSpan:q1,makeAnchor:R1,makeFragment:fa,wrapFragment:O1,makeVList:I1,makeOrd:B1,makeGlue:F1,staticSvg:P1,svgData:va,tryCombineChars:E1},Q={number:3,unit:"mu"},J0={number:4,unit:"mu"},N0={number:5,unit:"mu"},H1={mord:{mop:Q,mbin:J0,mrel:N0,minner:Q},mop:{mord:Q,mop:Q,mrel:N0,minner:Q},mbin:{mord:J0,mop:J0,mopen:J0,minner:J0},mrel:{mord:N0,mop:N0,mopen:N0,minner:N0},mopen:{},mclose:{mop:Q,mbin:J0,mrel:N0,minner:Q},mpunct:{mord:Q,mop:Q,mrel:N0,mopen:Q,mclose:Q,mpunct:Q,minner:Q},minner:{mord:Q,mop:Q,mbin:J0,mrel:N0,mopen:Q,mpunct:Q,minner:Q}},V1={mord:{mop:Q},mop:{mord:Q,mop:Q},mbin:{},mrel:{},mopen:{},mclose:{mop:Q},mpunct:{},minner:{mop:Q}},ga={},Pe={},He={};function D(r){for(var{type:e,names:t,props:a,handler:n,htmlBuilder:l,mathmlBuilder:o}=r,h={type:e,numArgs:a.numArgs,argTypes:a.argTypes,allowedInArgument:!!a.allowedInArgument,allowedInText:!!a.allowedInText,allowedInMath:a.allowedInMath===void 0?!0:a.allowedInMath,numOptionalArgs:a.numOptionalArgs||0,infix:!!a.infix,primitive:!!a.primitive,handler:n},c=0;c<t.length;++c)ga[t[c]]=h;e&&(l&&(Pe[e]=l),o&&(He[e]=o))}function te(r){var{type:e,htmlBuilder:t,mathmlBuilder:a}=r;D({type:e,names:[],props:{numArgs:0},handler(){throw new Error("Should never be called.")},htmlBuilder:t,mathmlBuilder:a})}var Ve=function(e){return e.type==="ordgroup"&&e.body.length===1?e.body[0]:e},t0=function(e){return e.type==="ordgroup"?e.body:[e]},L0=y.makeSpan,U1=["leftmost","mbin","mopen","mrel","mop","mpunct"],G1=["rightmost","mrel","mclose","mpunct"],Y1={display:O.DISPLAY,text:O.TEXT,script:O.SCRIPT,scriptscript:O.SCRIPTSCRIPT},W1={mord:"mord",mop:"mop",mbin:"mbin",mrel:"mrel",mopen:"mopen",mclose:"mclose",mpunct:"mpunct",minner:"minner"},l0=function(e,t,a,n){n===void 0&&(n=[null,null]);for(var l=[],o=0;o<e.length;o++){var h=U(e[o],t);if(h instanceof we){var c=h.children;l.push(...c)}else l.push(h)}if(y.tryCombineChars(l),!a)return l;var p=t;if(e.length===1){var g=e[0];g.type==="sizing"?p=t.havingSize(g.size):g.type==="styling"&&(p=t.havingStyle(Y1[g.style]))}var b=L0([n[0]||"leftmost"],[],t),x=L0([n[1]||"rightmost"],[],t),w=a==="root";return yr(l,(M,C)=>{var N=C.classes[0],E=M.classes[0];N==="mbin"&&q.contains(G1,E)?C.classes[0]="mord":E==="mbin"&&q.contains(U1,N)&&(M.classes[0]="mord")},{node:b},x,w),yr(l,(M,C)=>{var N=zt(C),E=zt(M),L=N&&E?M.hasClass("mtight")?V1[N][E]:H1[N][E]:null;if(L)return y.makeGlue(L,p)},{node:b},x,w),l},yr=function r(e,t,a,n,l){n&&e.push(n);for(var o=0;o<e.length;o++){var h=e[o],c=ba(h);if(c){r(c.children,t,a,null,l);continue}var p=!h.hasClass("mspace");if(p){var g=t(h,a.node);g&&(a.insertAfter?a.insertAfter(g):(e.unshift(g),o++))}p?a.node=h:l&&h.hasClass("newline")&&(a.node=L0(["leftmost"])),a.insertAfter=(b=>x=>{e.splice(b+1,0,x),o++})(o)}n&&e.pop()},ba=function(e){return e instanceof we||e instanceof ca||e instanceof Ue&&e.hasClass("enclosing")?e:null},X1=function r(e,t){var a=ba(e);if(a){var n=a.children;if(n.length){if(t==="right")return r(n[n.length-1],"right");if(t==="left")return r(n[0],"left")}}return e},zt=function(e,t){return e?(t&&(e=X1(e,t)),W1[e.classes[0]]||null):null},ye=function(e,t){var a=["nulldelimiter"].concat(e.baseSizingClasses());return L0(t.concat(a))},U=function(e,t,a){if(!e)return L0();if(Pe[e.type]){var n=Pe[e.type](e,t);if(a&&t.size!==a.size){n=L0(t.sizingClasses(a),[n],t);var l=t.sizeMultiplier/a.sizeMultiplier;n.height*=l,n.depth*=l}return n}else throw new A("Got group of unknown type: '"+e.type+"'")};function Ee(r,e){var t=L0(["base"],r,e),a=L0(["strut"]);return a.style.height=T(t.height+t.depth),t.depth&&(a.style.verticalAlign=T(-t.depth)),t.children.unshift(a),t}function xr(r,e){var t=null;r.length===1&&r[0].type==="tag"&&(t=r[0].tag,r=r[0].body);var a=l0(r,e,"root"),n;a.length===2&&a[1].hasClass("tag")&&(n=a.pop());for(var l=[],o=[],h=0;h<a.length;h++)if(o.push(a[h]),a[h].hasClass("mbin")||a[h].hasClass("mrel")||a[h].hasClass("allowbreak")){for(var c=!1;h<a.length-1&&a[h+1].hasClass("mspace")&&!a[h+1].hasClass("newline");)h++,o.push(a[h]),a[h].hasClass("nobreak")&&(c=!0);c||(l.push(Ee(o,e)),o=[])}else a[h].hasClass("newline")&&(o.pop(),o.length>0&&(l.push(Ee(o,e)),o=[]),l.push(a[h]));o.length>0&&l.push(Ee(o,e));var p;t?(p=Ee(l0(t,e,!0)),p.classes=["tag"],l.push(p)):n&&l.push(n);var g=L0(["katex-html"],l);if(g.setAttribute("aria-hidden","true"),p){var b=p.children[0];b.style.height=T(g.height+g.depth),g.depth&&(b.style.verticalAlign=T(-g.depth))}return g}function ya(r){return new we(r)}class v0{constructor(e,t,a){this.type=void 0,this.attributes=void 0,this.children=void 0,this.classes=void 0,this.type=e,this.attributes={},this.children=t||[],this.classes=a||[]}setAttribute(e,t){this.attributes[e]=t}getAttribute(e){return this.attributes[e]}toNode(){var e=document.createElementNS("http://www.w3.org/1998/Math/MathML",this.type);for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&e.setAttribute(t,this.attributes[t]);this.classes.length>0&&(e.className=W0(this.classes));for(var a=0;a<this.children.length;a++)if(this.children[a]instanceof A0&&this.children[a+1]instanceof A0){for(var n=this.children[a].toText()+this.children[++a].toText();this.children[a+1]instanceof A0;)n+=this.children[++a].toText();e.appendChild(new A0(n).toNode())}else e.appendChild(this.children[a].toNode());return e}toMarkup(){var e="<"+this.type;for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="',e+=q.escape(this.attributes[t]),e+='"');this.classes.length>0&&(e+=' class ="'+q.escape(W0(this.classes))+'"'),e+=">";for(var a=0;a<this.children.length;a++)e+=this.children[a].toMarkup();return e+="</"+this.type+">",e}toText(){return this.children.map(e=>e.toText()).join("")}}class A0{constructor(e){this.text=void 0,this.text=e}toNode(){return document.createTextNode(this.text)}toMarkup(){return q.escape(this.toText())}toText(){return this.text}}class $1{constructor(e){this.width=void 0,this.character=void 0,this.width=e,e>=.05555&&e<=.05556?this.character=" ":e>=.1666&&e<=.1667?this.character=" ":e>=.2222&&e<=.2223?this.character=" ":e>=.2777&&e<=.2778?this.character="  ":e>=-.05556&&e<=-.05555?this.character=" ⁣":e>=-.1667&&e<=-.1666?this.character=" ⁣":e>=-.2223&&e<=-.2222?this.character=" ⁣":e>=-.2778&&e<=-.2777?this.character=" ⁣":this.character=null}toNode(){if(this.character)return document.createTextNode(this.character);var e=document.createElementNS("http://www.w3.org/1998/Math/MathML","mspace");return e.setAttribute("width",T(this.width)),e}toMarkup(){return this.character?"<mtext>"+this.character+"</mtext>":'<mspace width="'+T(this.width)+'"/>'}toText(){return this.character?this.character:" "}}var S={MathNode:v0,TextNode:A0,SpaceNode:$1,newDocumentFragment:ya},w0=function(e,t,a){return K[t][e]&&K[t][e].replace&&e.charCodeAt(0)!==55349&&!(ma.hasOwnProperty(e)&&a&&(a.fontFamily&&a.fontFamily.slice(4,6)==="tt"||a.font&&a.font.slice(4,6)==="tt"))&&(e=K[t][e].replace),new S.TextNode(e)},Rt=function(e){return e.length===1?e[0]:new S.MathNode("mrow",e)},Ot=function(e,t){if(t.fontFamily==="texttt")return"monospace";if(t.fontFamily==="textsf")return t.fontShape==="textit"&&t.fontWeight==="textbf"?"sans-serif-bold-italic":t.fontShape==="textit"?"sans-serif-italic":t.fontWeight==="textbf"?"bold-sans-serif":"sans-serif";if(t.fontShape==="textit"&&t.fontWeight==="textbf")return"bold-italic";if(t.fontShape==="textit")return"italic";if(t.fontWeight==="textbf")return"bold";var a=t.font;if(!a||a==="mathnormal")return null;var n=e.mode;if(a==="mathit")return"italic";if(a==="boldsymbol")return e.type==="textord"?"bold":"bold-italic";if(a==="mathbf")return"bold";if(a==="mathbb")return"double-struck";if(a==="mathsfit")return"sans-serif-italic";if(a==="mathfrak")return"fraktur";if(a==="mathscr"||a==="mathcal")return"script";if(a==="mathsf")return"sans-serif";if(a==="mathtt")return"monospace";var l=e.text;if(q.contains(["\\imath","\\jmath"],l))return null;K[n][l]&&K[n][l].replace&&(l=K[n][l].replace);var o=y.fontMap[a].fontName;return Et(l,o,n)?y.fontMap[a].variant:null};function ot(r){if(!r)return!1;if(r.type==="mi"&&r.children.length===1){var e=r.children[0];return e instanceof A0&&e.text==="."}else if(r.type==="mo"&&r.children.length===1&&r.getAttribute("separator")==="true"&&r.getAttribute("lspace")==="0em"&&r.getAttribute("rspace")==="0em"){var t=r.children[0];return t instanceof A0&&t.text===","}else return!1}var d0=function(e,t,a){if(e.length===1){var n=$(e[0],t);return a&&n instanceof v0&&n.type==="mo"&&(n.setAttribute("lspace","0em"),n.setAttribute("rspace","0em")),[n]}for(var l=[],o,h=0;h<e.length;h++){var c=$(e[h],t);if(c instanceof v0&&o instanceof v0){if(c.type==="mtext"&&o.type==="mtext"&&c.getAttribute("mathvariant")===o.getAttribute("mathvariant")){o.children.push(...c.children);continue}else if(c.type==="mn"&&o.type==="mn"){o.children.push(...c.children);continue}else if(ot(c)&&o.type==="mn"){o.children.push(...c.children);continue}else if(c.type==="mn"&&ot(o))c.children=[...o.children,...c.children],l.pop();else if((c.type==="msup"||c.type==="msub")&&c.children.length>=1&&(o.type==="mn"||ot(o))){var p=c.children[0];p instanceof v0&&p.type==="mn"&&(p.children=[...o.children,...p.children],l.pop())}else if(o.type==="mi"&&o.children.length===1){var g=o.children[0];if(g instanceof A0&&g.text==="̸"&&(c.type==="mo"||c.type==="mi"||c.type==="mn")){var b=c.children[0];b instanceof A0&&b.text.length>0&&(b.text=b.text.slice(0,1)+"̸"+b.text.slice(1),l.pop())}}}l.push(c),o=c}return l},$0=function(e,t,a){return Rt(d0(e,t,a))},$=function(e,t){if(!e)return new S.MathNode("mrow");if(He[e.type]){var a=He[e.type](e,t);return a}else throw new A("Got group of unknown type: '"+e.type+"'")};function wr(r,e,t,a,n){var l=d0(r,t),o;l.length===1&&l[0]instanceof v0&&q.contains(["mrow","mtable"],l[0].type)?o=l[0]:o=new S.MathNode("mrow",l);var h=new S.MathNode("annotation",[new S.TextNode(e)]);h.setAttribute("encoding","application/x-tex");var c=new S.MathNode("semantics",[o,h]),p=new S.MathNode("math",[c]);p.setAttribute("xmlns","http://www.w3.org/1998/Math/MathML"),a&&p.setAttribute("display","block");var g=n?"katex":"katex-mathml";return y.makeSpan([g],[p])}var j1=function(e){return new E0({style:e.displayMode?O.DISPLAY:O.TEXT,maxSize:e.maxSize,minRuleThickness:e.minRuleThickness})},K1=function(e,t){if(t.displayMode){var a=["katex-display"];t.leqno&&a.push("leqno"),t.fleqn&&a.push("fleqn"),e=y.makeSpan(a,[e])}return e},Z1=function(e,t,a){var n=j1(a),l;if(a.output==="mathml")return wr(e,t,n,a.displayMode,!0);if(a.output==="html"){var o=xr(e,n);l=y.makeSpan(["katex"],[o])}else{var h=wr(e,t,n,a.displayMode,!1),c=xr(e,n);l=y.makeSpan(["katex"],[h,c])}return K1(l,a)},J1={widehat:"^",widecheck:"ˇ",widetilde:"~",utilde:"~",overleftarrow:"←",underleftarrow:"←",xleftarrow:"←",overrightarrow:"→",underrightarrow:"→",xrightarrow:"→",underbrace:"⏟",overbrace:"⏞",overgroup:"⏠",undergroup:"⏡",overleftrightarrow:"↔",underleftrightarrow:"↔",xleftrightarrow:"↔",Overrightarrow:"⇒",xRightarrow:"⇒",overleftharpoon:"↼",xleftharpoonup:"↼",overrightharpoon:"⇀",xrightharpoonup:"⇀",xLeftarrow:"⇐",xLeftrightarrow:"⇔",xhookleftarrow:"↩",xhookrightarrow:"↪",xmapsto:"↦",xrightharpoondown:"⇁",xleftharpoondown:"↽",xrightleftharpoons:"⇌",xleftrightharpoons:"⇋",xtwoheadleftarrow:"↞",xtwoheadrightarrow:"↠",xlongequal:"=",xtofrom:"⇄",xrightleftarrows:"⇄",xrightequilibrium:"⇌",xleftequilibrium:"⇋","\\cdrightarrow":"→","\\cdleftarrow":"←","\\cdlongequal":"="},Q1=function(e){var t=new S.MathNode("mo",[new S.TextNode(J1[e.replace(/^\\/,"")])]);return t.setAttribute("stretchy","true"),t},_1={overrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],overleftarrow:[["leftarrow"],.888,522,"xMinYMin"],underrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],underleftarrow:[["leftarrow"],.888,522,"xMinYMin"],xrightarrow:[["rightarrow"],1.469,522,"xMaxYMin"],"\\cdrightarrow":[["rightarrow"],3,522,"xMaxYMin"],xleftarrow:[["leftarrow"],1.469,522,"xMinYMin"],"\\cdleftarrow":[["leftarrow"],3,522,"xMinYMin"],Overrightarrow:[["doublerightarrow"],.888,560,"xMaxYMin"],xRightarrow:[["doublerightarrow"],1.526,560,"xMaxYMin"],xLeftarrow:[["doubleleftarrow"],1.526,560,"xMinYMin"],overleftharpoon:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoonup:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoondown:[["leftharpoondown"],.888,522,"xMinYMin"],overrightharpoon:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoonup:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoondown:[["rightharpoondown"],.888,522,"xMaxYMin"],xlongequal:[["longequal"],.888,334,"xMinYMin"],"\\cdlongequal":[["longequal"],3,334,"xMinYMin"],xtwoheadleftarrow:[["twoheadleftarrow"],.888,334,"xMinYMin"],xtwoheadrightarrow:[["twoheadrightarrow"],.888,334,"xMaxYMin"],overleftrightarrow:[["leftarrow","rightarrow"],.888,522],overbrace:[["leftbrace","midbrace","rightbrace"],1.6,548],underbrace:[["leftbraceunder","midbraceunder","rightbraceunder"],1.6,548],underleftrightarrow:[["leftarrow","rightarrow"],.888,522],xleftrightarrow:[["leftarrow","rightarrow"],1.75,522],xLeftrightarrow:[["doubleleftarrow","doublerightarrow"],1.75,560],xrightleftharpoons:[["leftharpoondownplus","rightharpoonplus"],1.75,716],xleftrightharpoons:[["leftharpoonplus","rightharpoondownplus"],1.75,716],xhookleftarrow:[["leftarrow","righthook"],1.08,522],xhookrightarrow:[["lefthook","rightarrow"],1.08,522],overlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],underlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],overgroup:[["leftgroup","rightgroup"],.888,342],undergroup:[["leftgroupunder","rightgroupunder"],.888,342],xmapsto:[["leftmapsto","rightarrow"],1.5,522],xtofrom:[["leftToFrom","rightToFrom"],1.75,528],xrightleftarrows:[["baraboveleftarrow","rightarrowabovebar"],1.75,901],xrightequilibrium:[["baraboveshortleftharpoon","rightharpoonaboveshortbar"],1.75,716],xleftequilibrium:[["shortbaraboveleftharpoon","shortrightharpoonabovebar"],1.75,716]},el=function(e){return e.type==="ordgroup"?e.body.length:1},tl=function(e,t){function a(){var h=4e5,c=e.label.slice(1);if(q.contains(["widehat","widecheck","widetilde","utilde"],c)){var p=e,g=el(p.base),b,x,w;if(g>5)c==="widehat"||c==="widecheck"?(b=420,h=2364,w=.42,x=c+"4"):(b=312,h=2340,w=.34,x="tilde4");else{var M=[1,1,2,2,3,3][g];c==="widehat"||c==="widecheck"?(h=[0,1062,2364,2364,2364][M],b=[0,239,300,360,420][M],w=[0,.24,.3,.3,.36,.42][M],x=c+M):(h=[0,600,1033,2339,2340][M],b=[0,260,286,306,312][M],w=[0,.26,.286,.3,.306,.34][M],x="tilde"+M)}var C=new Q0(x),N=new X0([C],{width:"100%",height:T(w),viewBox:"0 0 "+h+" "+b,preserveAspectRatio:"none"});return{span:y.makeSvgSpan([],[N],t),minWidth:0,height:w}}else{var E=[],L=_1[c],[F,Y,V]=L,W=V/1e3,G=F.length,J,X;if(G===1){var B0=L[3];J=["hide-tail"],X=[B0]}else if(G===2)J=["halfarrow-left","halfarrow-right"],X=["xMinYMin","xMaxYMin"];else if(G===3)J=["brace-left","brace-center","brace-right"],X=["xMinYMin","xMidYMin","xMaxYMin"];else throw new Error(`Correct katexImagesData or update code here to support
                    `+G+" children.");for(var i0=0;i0<G;i0++){var n0=new Q0(F[i0]),K0=new X0([n0],{width:"400em",height:T(W),viewBox:"0 0 "+h+" "+V,preserveAspectRatio:X[i0]+" slice"}),h0=y.makeSvgSpan([J[i0]],[K0],t);if(G===1)return{span:h0,minWidth:Y,height:W};h0.style.height=T(W),E.push(h0)}return{span:y.makeSpan(["stretchy"],E,t),minWidth:Y,height:W}}}var{span:n,minWidth:l,height:o}=a();return n.height=o,n.style.height=T(o),l>0&&(n.style.minWidth=T(l)),n},rl=function(e,t,a,n,l){var o,h=e.height+e.depth+a+n;if(/fbox|color|angl/.test(t)){if(o=y.makeSpan(["stretchy",t],[],l),t==="fbox"){var c=l.color&&l.getColor();c&&(o.style.borderColor=c)}}else{var p=[];/^[bx]cancel$/.test(t)&&p.push(new mr({x1:"0",y1:"0",x2:"100%",y2:"100%","stroke-width":"0.046em"})),/^x?cancel$/.test(t)&&p.push(new mr({x1:"0",y1:"100%",x2:"100%",y2:"0","stroke-width":"0.046em"}));var g=new X0(p,{width:"100%",height:T(h)});o=y.makeSvgSpan([],[g],l)}return o.height=h,o.style.height=T(h),o},I0={encloseSpan:rl,mathMLnode:Q1,svgSpan:tl};function H(r,e){if(!r||r.type!==e)throw new Error("Expected node of type "+e+", but got "+(r?"node of type "+r.type:String(r)));return r}function Lt(r){var e=We(r);if(!e)throw new Error("Expected node of symbol group type, but got "+(r?"node of type "+r.type:String(r)));return e}function We(r){return r&&(r.type==="atom"||A1.hasOwnProperty(r.type))?r:null}var It=(r,e)=>{var t,a,n;r&&r.type==="supsub"?(a=H(r.base,"accent"),t=a.base,r.base=t,n=M1(U(r,e)),r.base=a):(a=H(r,"accent"),t=a.base);var l=U(t,e.havingCrampedStyle()),o=a.isShifty&&q.isCharacterBox(t),h=0;if(o){var c=q.getBaseElem(t),p=U(c,e.havingCrampedStyle());h=dr(p).skew}var g=a.label==="\\c",b=g?l.height+l.depth:Math.min(l.height,e.fontMetrics().xHeight),x;if(a.isStretchy)x=I0.svgSpan(a,e),x=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:l},{type:"elem",elem:x,wrapperClasses:["svg-align"],wrapperStyle:h>0?{width:"calc(100% - "+T(2*h)+")",marginLeft:T(2*h)}:void 0}]},e);else{var w,M;a.label==="\\vec"?(w=y.staticSvg("vec",e),M=y.svgData.vec[1]):(w=y.makeOrd({mode:a.mode,text:a.label},e,"textord"),w=dr(w),w.italic=0,M=w.width,g&&(b+=w.depth)),x=y.makeSpan(["accent-body"],[w]);var C=a.label==="\\textcircled";C&&(x.classes.push("accent-full"),b=l.height);var N=h;C||(N-=M/2),x.style.left=T(N),a.label==="\\textcircled"&&(x.style.top=".2em"),x=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:l},{type:"kern",size:-b},{type:"elem",elem:x}]},e)}var E=y.makeSpan(["mord","accent"],[x],e);return n?(n.children[0]=E,n.height=Math.max(E.height,n.height),n.classes[0]="mord",n):E},xa=(r,e)=>{var t=r.isStretchy?I0.mathMLnode(r.label):new S.MathNode("mo",[w0(r.label,r.mode)]),a=new S.MathNode("mover",[$(r.base,e),t]);return a.setAttribute("accent","true"),a},al=new RegExp(["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring"].map(r=>"\\"+r).join("|"));D({type:"accent",names:["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring","\\widecheck","\\widehat","\\widetilde","\\overrightarrow","\\overleftarrow","\\Overrightarrow","\\overleftrightarrow","\\overgroup","\\overlinesegment","\\overleftharpoon","\\overrightharpoon"],props:{numArgs:1},handler:(r,e)=>{var t=Ve(e[0]),a=!al.test(r.funcName),n=!a||r.funcName==="\\widehat"||r.funcName==="\\widetilde"||r.funcName==="\\widecheck";return{type:"accent",mode:r.parser.mode,label:r.funcName,isStretchy:a,isShifty:n,base:t}},htmlBuilder:It,mathmlBuilder:xa});D({type:"accent",names:["\\'","\\`","\\^","\\~","\\=","\\u","\\.",'\\"',"\\c","\\r","\\H","\\v","\\textcircled"],props:{numArgs:1,allowedInText:!0,allowedInMath:!0,argTypes:["primitive"]},handler:(r,e)=>{var t=e[0],a=r.parser.mode;return a==="math"&&(r.parser.settings.reportNonstrict("mathVsTextAccents","LaTeX's accent "+r.funcName+" works only in text mode"),a="text"),{type:"accent",mode:a,label:r.funcName,isStretchy:!1,isShifty:!0,base:t}},htmlBuilder:It,mathmlBuilder:xa});D({type:"accentUnder",names:["\\underleftarrow","\\underrightarrow","\\underleftrightarrow","\\undergroup","\\underlinesegment","\\utilde"],props:{numArgs:1},handler:(r,e)=>{var{parser:t,funcName:a}=r,n=e[0];return{type:"accentUnder",mode:t.mode,label:a,base:n}},htmlBuilder:(r,e)=>{var t=U(r.base,e),a=I0.svgSpan(r,e),n=r.label==="\\utilde"?.12:0,l=y.makeVList({positionType:"top",positionData:t.height,children:[{type:"elem",elem:a,wrapperClasses:["svg-align"]},{type:"kern",size:n},{type:"elem",elem:t}]},e);return y.makeSpan(["mord","accentunder"],[l],e)},mathmlBuilder:(r,e)=>{var t=I0.mathMLnode(r.label),a=new S.MathNode("munder",[$(r.base,e),t]);return a.setAttribute("accentunder","true"),a}});var qe=r=>{var e=new S.MathNode("mpadded",r?[r]:[]);return e.setAttribute("width","+0.6em"),e.setAttribute("lspace","0.3em"),e};D({type:"xArrow",names:["\\xleftarrow","\\xrightarrow","\\xLeftarrow","\\xRightarrow","\\xleftrightarrow","\\xLeftrightarrow","\\xhookleftarrow","\\xhookrightarrow","\\xmapsto","\\xrightharpoondown","\\xrightharpoonup","\\xleftharpoondown","\\xleftharpoonup","\\xrightleftharpoons","\\xleftrightharpoons","\\xlongequal","\\xtwoheadrightarrow","\\xtwoheadleftarrow","\\xtofrom","\\xrightleftarrows","\\xrightequilibrium","\\xleftequilibrium","\\\\cdrightarrow","\\\\cdleftarrow","\\\\cdlongequal"],props:{numArgs:1,numOptionalArgs:1},handler(r,e,t){var{parser:a,funcName:n}=r;return{type:"xArrow",mode:a.mode,label:n,body:e[0],below:t[0]}},htmlBuilder(r,e){var t=e.style,a=e.havingStyle(t.sup()),n=y.wrapFragment(U(r.body,a,e),e),l=r.label.slice(0,2)==="\\x"?"x":"cd";n.classes.push(l+"-arrow-pad");var o;r.below&&(a=e.havingStyle(t.sub()),o=y.wrapFragment(U(r.below,a,e),e),o.classes.push(l+"-arrow-pad"));var h=I0.svgSpan(r,e),c=-e.fontMetrics().axisHeight+.5*h.height,p=-e.fontMetrics().axisHeight-.5*h.height-.111;(n.depth>.25||r.label==="\\xleftequilibrium")&&(p-=n.depth);var g;if(o){var b=-e.fontMetrics().axisHeight+o.height+.5*h.height+.111;g=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:n,shift:p},{type:"elem",elem:h,shift:c},{type:"elem",elem:o,shift:b}]},e)}else g=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:n,shift:p},{type:"elem",elem:h,shift:c}]},e);return g.children[0].children[0].children[1].classes.push("svg-align"),y.makeSpan(["mrel","x-arrow"],[g],e)},mathmlBuilder(r,e){var t=I0.mathMLnode(r.label);t.setAttribute("minsize",r.label.charAt(0)==="x"?"1.75em":"3.0em");var a;if(r.body){var n=qe($(r.body,e));if(r.below){var l=qe($(r.below,e));a=new S.MathNode("munderover",[t,l,n])}else a=new S.MathNode("mover",[t,n])}else if(r.below){var o=qe($(r.below,e));a=new S.MathNode("munder",[t,o])}else a=qe(),a=new S.MathNode("mover",[t,a]);return a}});var nl=y.makeSpan;function wa(r,e){var t=l0(r.body,e,!0);return nl([r.mclass],t,e)}function ka(r,e){var t,a=d0(r.body,e);return r.mclass==="minner"?t=new S.MathNode("mpadded",a):r.mclass==="mord"?r.isCharacterBox?(t=a[0],t.type="mi"):t=new S.MathNode("mi",a):(r.isCharacterBox?(t=a[0],t.type="mo"):t=new S.MathNode("mo",a),r.mclass==="mbin"?(t.attributes.lspace="0.22em",t.attributes.rspace="0.22em"):r.mclass==="mpunct"?(t.attributes.lspace="0em",t.attributes.rspace="0.17em"):r.mclass==="mopen"||r.mclass==="mclose"?(t.attributes.lspace="0em",t.attributes.rspace="0em"):r.mclass==="minner"&&(t.attributes.lspace="0.0556em",t.attributes.width="+0.1111em")),t}D({type:"mclass",names:["\\mathord","\\mathbin","\\mathrel","\\mathopen","\\mathclose","\\mathpunct","\\mathinner"],props:{numArgs:1,primitive:!0},handler(r,e){var{parser:t,funcName:a}=r,n=e[0];return{type:"mclass",mode:t.mode,mclass:"m"+a.slice(5),body:t0(n),isCharacterBox:q.isCharacterBox(n)}},htmlBuilder:wa,mathmlBuilder:ka});var Xe=r=>{var e=r.type==="ordgroup"&&r.body.length?r.body[0]:r;return e.type==="atom"&&(e.family==="bin"||e.family==="rel")?"m"+e.family:"mord"};D({type:"mclass",names:["\\@binrel"],props:{numArgs:2},handler(r,e){var{parser:t}=r;return{type:"mclass",mode:t.mode,mclass:Xe(e[0]),body:t0(e[1]),isCharacterBox:q.isCharacterBox(e[1])}}});D({type:"mclass",names:["\\stackrel","\\overset","\\underset"],props:{numArgs:2},handler(r,e){var{parser:t,funcName:a}=r,n=e[1],l=e[0],o;a!=="\\stackrel"?o=Xe(n):o="mrel";var h={type:"op",mode:n.mode,limits:!0,alwaysHandleSupSub:!0,parentIsSupSub:!1,symbol:!1,suppressBaseShift:a!=="\\stackrel",body:t0(n)},c={type:"supsub",mode:l.mode,base:h,sup:a==="\\underset"?null:l,sub:a==="\\underset"?l:null};return{type:"mclass",mode:t.mode,mclass:o,body:[c],isCharacterBox:q.isCharacterBox(c)}},htmlBuilder:wa,mathmlBuilder:ka});D({type:"pmb",names:["\\pmb"],props:{numArgs:1,allowedInText:!0},handler(r,e){var{parser:t}=r;return{type:"pmb",mode:t.mode,mclass:Xe(e[0]),body:t0(e[0])}},htmlBuilder(r,e){var t=l0(r.body,e,!0),a=y.makeSpan([r.mclass],t,e);return a.style.textShadow="0.02em 0.01em 0.04px",a},mathmlBuilder(r,e){var t=d0(r.body,e),a=new S.MathNode("mstyle",t);return a.setAttribute("style","text-shadow: 0.02em 0.01em 0.04px"),a}});var ll={">":"\\\\cdrightarrow","<":"\\\\cdleftarrow","=":"\\\\cdlongequal",A:"\\uparrow",V:"\\downarrow","|":"\\Vert",".":"no arrow"},kr=()=>({type:"styling",body:[],mode:"math",style:"display"}),Sr=r=>r.type==="textord"&&r.text==="@",il=(r,e)=>(r.type==="mathord"||r.type==="atom")&&r.text===e;function sl(r,e,t){var a=ll[r];switch(a){case"\\\\cdrightarrow":case"\\\\cdleftarrow":return t.callFunction(a,[e[0]],[e[1]]);case"\\uparrow":case"\\downarrow":{var n=t.callFunction("\\\\cdleft",[e[0]],[]),l={type:"atom",text:a,mode:"math",family:"rel"},o=t.callFunction("\\Big",[l],[]),h=t.callFunction("\\\\cdright",[e[1]],[]),c={type:"ordgroup",mode:"math",body:[n,o,h]};return t.callFunction("\\\\cdparent",[c],[])}case"\\\\cdlongequal":return t.callFunction("\\\\cdlongequal",[],[]);case"\\Vert":{var p={type:"textord",text:"\\Vert",mode:"math"};return t.callFunction("\\Big",[p],[])}default:return{type:"textord",text:" ",mode:"math"}}}function ol(r){var e=[];for(r.gullet.beginGroup(),r.gullet.macros.set("\\cr","\\\\\\relax"),r.gullet.beginGroup();;){e.push(r.parseExpression(!1,"\\\\")),r.gullet.endGroup(),r.gullet.beginGroup();var t=r.fetch().text;if(t==="&"||t==="\\\\")r.consume();else if(t==="\\end"){e[e.length-1].length===0&&e.pop();break}else throw new A("Expected \\\\ or \\cr or \\end",r.nextToken)}for(var a=[],n=[a],l=0;l<e.length;l++){for(var o=e[l],h=kr(),c=0;c<o.length;c++)if(!Sr(o[c]))h.body.push(o[c]);else{a.push(h),c+=1;var p=Lt(o[c]).text,g=new Array(2);if(g[0]={type:"ordgroup",mode:"math",body:[]},g[1]={type:"ordgroup",mode:"math",body:[]},!("=|.".indexOf(p)>-1))if("<>AV".indexOf(p)>-1)for(var b=0;b<2;b++){for(var x=!0,w=c+1;w<o.length;w++){if(il(o[w],p)){x=!1,c=w;break}if(Sr(o[w]))throw new A("Missing a "+p+" character to complete a CD arrow.",o[w]);g[b].body.push(o[w])}if(x)throw new A("Missing a "+p+" character to complete a CD arrow.",o[c])}else throw new A('Expected one of "<>AV=|." after @',o[c]);var M=sl(p,g,r),C={type:"styling",body:[M],mode:"math",style:"display"};a.push(C),h=kr()}l%2===0?a.push(h):a.shift(),a=[],n.push(a)}r.gullet.endGroup(),r.gullet.endGroup();var N=new Array(n[0].length).fill({type:"align",align:"c",pregap:.25,postgap:.25});return{type:"array",mode:"math",body:n,arraystretch:1,addJot:!0,rowGaps:[null],cols:N,colSeparationType:"CD",hLinesBeforeRow:new Array(n.length+1).fill([])}}D({type:"cdlabel",names:["\\\\cdleft","\\\\cdright"],props:{numArgs:1},handler(r,e){var{parser:t,funcName:a}=r;return{type:"cdlabel",mode:t.mode,side:a.slice(4),label:e[0]}},htmlBuilder(r,e){var t=e.havingStyle(e.style.sup()),a=y.wrapFragment(U(r.label,t,e),e);return a.classes.push("cd-label-"+r.side),a.style.bottom=T(.8-a.depth),a.height=0,a.depth=0,a},mathmlBuilder(r,e){var t=new S.MathNode("mrow",[$(r.label,e)]);return t=new S.MathNode("mpadded",[t]),t.setAttribute("width","0"),r.side==="left"&&t.setAttribute("lspace","-1width"),t.setAttribute("voffset","0.7em"),t=new S.MathNode("mstyle",[t]),t.setAttribute("displaystyle","false"),t.setAttribute("scriptlevel","1"),t}});D({type:"cdlabelparent",names:["\\\\cdparent"],props:{numArgs:1},handler(r,e){var{parser:t}=r;return{type:"cdlabelparent",mode:t.mode,fragment:e[0]}},htmlBuilder(r,e){var t=y.wrapFragment(U(r.fragment,e),e);return t.classes.push("cd-vert-arrow"),t},mathmlBuilder(r,e){return new S.MathNode("mrow",[$(r.fragment,e)])}});D({type:"textord",names:["\\@char"],props:{numArgs:1,allowedInText:!0},handler(r,e){for(var{parser:t}=r,a=H(e[0],"ordgroup"),n=a.body,l="",o=0;o<n.length;o++){var h=H(n[o],"textord");l+=h.text}var c=parseInt(l),p;if(isNaN(c))throw new A("\\@char has non-numeric argument "+l);if(c<0||c>=1114111)throw new A("\\@char with invalid code point "+l);return c<=65535?p=String.fromCharCode(c):(c-=65536,p=String.fromCharCode((c>>10)+55296,(c&1023)+56320)),{type:"textord",mode:t.mode,text:p}}});var Sa=(r,e)=>{var t=l0(r.body,e.withColor(r.color),!1);return y.makeFragment(t)},Ma=(r,e)=>{var t=d0(r.body,e.withColor(r.color)),a=new S.MathNode("mstyle",t);return a.setAttribute("mathcolor",r.color),a};D({type:"color",names:["\\textcolor"],props:{numArgs:2,allowedInText:!0,argTypes:["color","original"]},handler(r,e){var{parser:t}=r,a=H(e[0],"color-token").color,n=e[1];return{type:"color",mode:t.mode,color:a,body:t0(n)}},htmlBuilder:Sa,mathmlBuilder:Ma});D({type:"color",names:["\\color"],props:{numArgs:1,allowedInText:!0,argTypes:["color"]},handler(r,e){var{parser:t,breakOnTokenText:a}=r,n=H(e[0],"color-token").color;t.gullet.macros.set("\\current@color",n);var l=t.parseExpression(!0,a);return{type:"color",mode:t.mode,color:n,body:l}},htmlBuilder:Sa,mathmlBuilder:Ma});D({type:"cr",names:["\\\\"],props:{numArgs:0,numOptionalArgs:0,allowedInText:!0},handler(r,e,t){var{parser:a}=r,n=a.gullet.future().text==="["?a.parseSizeGroup(!0):null,l=!a.settings.displayMode||!a.settings.useStrictBehavior("newLineInDisplayMode","In LaTeX, \\\\ or \\newline does nothing in display mode");return{type:"cr",mode:a.mode,newLine:l,size:n&&H(n,"size").value}},htmlBuilder(r,e){var t=y.makeSpan(["mspace"],[],e);return r.newLine&&(t.classes.push("newline"),r.size&&(t.style.marginTop=T(_(r.size,e)))),t},mathmlBuilder(r,e){var t=new S.MathNode("mspace");return r.newLine&&(t.setAttribute("linebreak","newline"),r.size&&t.setAttribute("height",T(_(r.size,e)))),t}});var At={"\\global":"\\global","\\long":"\\\\globallong","\\\\globallong":"\\\\globallong","\\def":"\\gdef","\\gdef":"\\gdef","\\edef":"\\xdef","\\xdef":"\\xdef","\\let":"\\\\globallet","\\futurelet":"\\\\globalfuture"},za=r=>{var e=r.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(e))throw new A("Expected a control sequence",r);return e},ul=r=>{var e=r.gullet.popToken();return e.text==="="&&(e=r.gullet.popToken(),e.text===" "&&(e=r.gullet.popToken())),e},Aa=(r,e,t,a)=>{var n=r.gullet.macros.get(t.text);n==null&&(t.noexpand=!0,n={tokens:[t],numArgs:0,unexpandable:!r.gullet.isExpandable(t.text)}),r.gullet.macros.set(e,n,a)};D({type:"internal",names:["\\global","\\long","\\\\globallong"],props:{numArgs:0,allowedInText:!0},handler(r){var{parser:e,funcName:t}=r;e.consumeSpaces();var a=e.fetch();if(At[a.text])return(t==="\\global"||t==="\\\\globallong")&&(a.text=At[a.text]),H(e.parseFunction(),"internal");throw new A("Invalid token after macro prefix",a)}});D({type:"internal",names:["\\def","\\gdef","\\edef","\\xdef"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(r){var{parser:e,funcName:t}=r,a=e.gullet.popToken(),n=a.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(n))throw new A("Expected a control sequence",a);for(var l=0,o,h=[[]];e.gullet.future().text!=="{";)if(a=e.gullet.popToken(),a.text==="#"){if(e.gullet.future().text==="{"){o=e.gullet.future(),h[l].push("{");break}if(a=e.gullet.popToken(),!/^[1-9]$/.test(a.text))throw new A('Invalid argument number "'+a.text+'"');if(parseInt(a.text)!==l+1)throw new A('Argument number "'+a.text+'" out of order');l++,h.push([])}else{if(a.text==="EOF")throw new A("Expected a macro definition");h[l].push(a.text)}var{tokens:c}=e.gullet.consumeArg();return o&&c.unshift(o),(t==="\\edef"||t==="\\xdef")&&(c=e.gullet.expandTokens(c),c.reverse()),e.gullet.macros.set(n,{tokens:c,numArgs:l,delimiters:h},t===At[t]),{type:"internal",mode:e.mode}}});D({type:"internal",names:["\\let","\\\\globallet"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(r){var{parser:e,funcName:t}=r,a=za(e.gullet.popToken());e.gullet.consumeSpaces();var n=ul(e);return Aa(e,a,n,t==="\\\\globallet"),{type:"internal",mode:e.mode}}});D({type:"internal",names:["\\futurelet","\\\\globalfuture"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(r){var{parser:e,funcName:t}=r,a=za(e.gullet.popToken()),n=e.gullet.popToken(),l=e.gullet.popToken();return Aa(e,a,l,t==="\\\\globalfuture"),e.gullet.pushToken(l),e.gullet.pushToken(n),{type:"internal",mode:e.mode}}});var pe=function(e,t,a){var n=K.math[e]&&K.math[e].replace,l=Et(n||e,t,a);if(!l)throw new Error("Unsupported symbol "+e+" and font size "+t+".");return l},Ft=function(e,t,a,n){var l=a.havingBaseStyle(t),o=y.makeSpan(n.concat(l.sizingClasses(a)),[e],a),h=l.sizeMultiplier/a.sizeMultiplier;return o.height*=h,o.depth*=h,o.maxFontSize=l.sizeMultiplier,o},Ta=function(e,t,a){var n=t.havingBaseStyle(a),l=(1-t.sizeMultiplier/n.sizeMultiplier)*t.fontMetrics().axisHeight;e.classes.push("delimcenter"),e.style.top=T(l),e.height-=l,e.depth+=l},hl=function(e,t,a,n,l,o){var h=y.makeSymbol(e,"Main-Regular",l,n),c=Ft(h,t,n,o);return a&&Ta(c,n,t),c},cl=function(e,t,a,n){return y.makeSymbol(e,"Size"+t+"-Regular",a,n)},Ca=function(e,t,a,n,l,o){var h=cl(e,t,l,n),c=Ft(y.makeSpan(["delimsizing","size"+t],[h],n),O.TEXT,n,o);return a&&Ta(c,n,O.TEXT),c},ut=function(e,t,a){var n;t==="Size1-Regular"?n="delim-size1":n="delim-size4";var l=y.makeSpan(["delimsizinginner",n],[y.makeSpan([],[y.makeSymbol(e,t,a)])]);return{type:"elem",elem:l}},ht=function(e,t,a){var n=q0["Size4-Regular"][e.charCodeAt(0)]?q0["Size4-Regular"][e.charCodeAt(0)][4]:q0["Size1-Regular"][e.charCodeAt(0)][4],l=new Q0("inner",v1(e,Math.round(1e3*t))),o=new X0([l],{width:T(n),height:T(t),style:"width:"+T(n),viewBox:"0 0 "+1e3*n+" "+Math.round(1e3*t),preserveAspectRatio:"xMinYMin"}),h=y.makeSvgSpan([],[o],a);return h.height=t,h.style.height=T(t),h.style.width=T(n),{type:"elem",elem:h}},Tt=.008,Re={type:"kern",size:-1*Tt},ml=["|","\\lvert","\\rvert","\\vert"],dl=["\\|","\\lVert","\\rVert","\\Vert"],Da=function(e,t,a,n,l,o){var h,c,p,g,b="",x=0;h=p=g=e,c=null;var w="Size1-Regular";e==="\\uparrow"?p=g="⏐":e==="\\Uparrow"?p=g="‖":e==="\\downarrow"?h=p="⏐":e==="\\Downarrow"?h=p="‖":e==="\\updownarrow"?(h="\\uparrow",p="⏐",g="\\downarrow"):e==="\\Updownarrow"?(h="\\Uparrow",p="‖",g="\\Downarrow"):q.contains(ml,e)?(p="∣",b="vert",x=333):q.contains(dl,e)?(p="∥",b="doublevert",x=556):e==="["||e==="\\lbrack"?(h="⎡",p="⎢",g="⎣",w="Size4-Regular",b="lbrack",x=667):e==="]"||e==="\\rbrack"?(h="⎤",p="⎥",g="⎦",w="Size4-Regular",b="rbrack",x=667):e==="\\lfloor"||e==="⌊"?(p=h="⎢",g="⎣",w="Size4-Regular",b="lfloor",x=667):e==="\\lceil"||e==="⌈"?(h="⎡",p=g="⎢",w="Size4-Regular",b="lceil",x=667):e==="\\rfloor"||e==="⌋"?(p=h="⎥",g="⎦",w="Size4-Regular",b="rfloor",x=667):e==="\\rceil"||e==="⌉"?(h="⎤",p=g="⎥",w="Size4-Regular",b="rceil",x=667):e==="("||e==="\\lparen"?(h="⎛",p="⎜",g="⎝",w="Size4-Regular",b="lparen",x=875):e===")"||e==="\\rparen"?(h="⎞",p="⎟",g="⎠",w="Size4-Regular",b="rparen",x=875):e==="\\{"||e==="\\lbrace"?(h="⎧",c="⎨",g="⎩",p="⎪",w="Size4-Regular"):e==="\\}"||e==="\\rbrace"?(h="⎫",c="⎬",g="⎭",p="⎪",w="Size4-Regular"):e==="\\lgroup"||e==="⟮"?(h="⎧",g="⎩",p="⎪",w="Size4-Regular"):e==="\\rgroup"||e==="⟯"?(h="⎫",g="⎭",p="⎪",w="Size4-Regular"):e==="\\lmoustache"||e==="⎰"?(h="⎧",g="⎭",p="⎪",w="Size4-Regular"):(e==="\\rmoustache"||e==="⎱")&&(h="⎫",g="⎩",p="⎪",w="Size4-Regular");var M=pe(h,w,l),C=M.height+M.depth,N=pe(p,w,l),E=N.height+N.depth,L=pe(g,w,l),F=L.height+L.depth,Y=0,V=1;if(c!==null){var W=pe(c,w,l);Y=W.height+W.depth,V=2}var G=C+F+Y,J=Math.max(0,Math.ceil((t-G)/(V*E))),X=G+J*V*E,B0=n.fontMetrics().axisHeight;a&&(B0*=n.sizeMultiplier);var i0=X/2-B0,n0=[];if(b.length>0){var K0=X-C-F,h0=Math.round(X*1e3),k0=g1(b,Math.round(K0*1e3)),P0=new Q0(b,k0),re=(x/1e3).toFixed(3)+"em",ae=(h0/1e3).toFixed(3)+"em",Ze=new X0([P0],{width:re,height:ae,viewBox:"0 0 "+x+" "+h0}),H0=y.makeSvgSpan([],[Ze],n);H0.height=h0/1e3,H0.style.width=re,H0.style.height=ae,n0.push({type:"elem",elem:H0})}else{if(n0.push(ut(g,w,l)),n0.push(Re),c===null){var V0=X-C-F+2*Tt;n0.push(ht(p,V0,n))}else{var b0=(X-C-F-Y)/2+2*Tt;n0.push(ht(p,b0,n)),n0.push(Re),n0.push(ut(c,w,l)),n0.push(Re),n0.push(ht(p,b0,n))}n0.push(Re),n0.push(ut(h,w,l))}var fe=n.havingBaseStyle(O.TEXT),Je=y.makeVList({positionType:"bottom",positionData:i0,children:n0},fe);return Ft(y.makeSpan(["delimsizing","mult"],[Je],fe),O.TEXT,n,o)},ct=80,mt=.08,dt=function(e,t,a,n,l){var o=p1(e,n,a),h=new Q0(e,o),c=new X0([h],{width:"400em",height:T(t),viewBox:"0 0 400000 "+a,preserveAspectRatio:"xMinYMin slice"});return y.makeSvgSpan(["hide-tail"],[c],l)},fl=function(e,t){var a=t.havingBaseSizing(),n=qa("\\surd",e*a.sizeMultiplier,Ea,a),l=a.sizeMultiplier,o=Math.max(0,t.minRuleThickness-t.fontMetrics().sqrtRuleThickness),h,c=0,p=0,g=0,b;return n.type==="small"?(g=1e3+1e3*o+ct,e<1?l=1:e<1.4&&(l=.7),c=(1+o+mt)/l,p=(1+o)/l,h=dt("sqrtMain",c,g,o,t),h.style.minWidth="0.853em",b=.833/l):n.type==="large"?(g=(1e3+ct)*ve[n.size],p=(ve[n.size]+o)/l,c=(ve[n.size]+o+mt)/l,h=dt("sqrtSize"+n.size,c,g,o,t),h.style.minWidth="1.02em",b=1/l):(c=e+o+mt,p=e+o,g=Math.floor(1e3*e+o)+ct,h=dt("sqrtTall",c,g,o,t),h.style.minWidth="0.742em",b=1.056),h.height=p,h.style.height=T(c),{span:h,advanceWidth:b,ruleWidth:(t.fontMetrics().sqrtRuleThickness+o)*l}},Ba=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","⌊","⌋","\\lceil","\\rceil","⌈","⌉","\\surd"],pl=["\\uparrow","\\downarrow","\\updownarrow","\\Uparrow","\\Downarrow","\\Updownarrow","|","\\|","\\vert","\\Vert","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","⟮","⟯","\\lmoustache","\\rmoustache","⎰","⎱"],Na=["<",">","\\langle","\\rangle","/","\\backslash","\\lt","\\gt"],ve=[0,1.2,1.8,2.4,3],vl=function(e,t,a,n,l){if(e==="<"||e==="\\lt"||e==="⟨"?e="\\langle":(e===">"||e==="\\gt"||e==="⟩")&&(e="\\rangle"),q.contains(Ba,e)||q.contains(Na,e))return Ca(e,t,!1,a,n,l);if(q.contains(pl,e))return Da(e,ve[t],!1,a,n,l);throw new A("Illegal delimiter: '"+e+"'")},gl=[{type:"small",style:O.SCRIPTSCRIPT},{type:"small",style:O.SCRIPT},{type:"small",style:O.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4}],bl=[{type:"small",style:O.SCRIPTSCRIPT},{type:"small",style:O.SCRIPT},{type:"small",style:O.TEXT},{type:"stack"}],Ea=[{type:"small",style:O.SCRIPTSCRIPT},{type:"small",style:O.SCRIPT},{type:"small",style:O.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4},{type:"stack"}],yl=function(e){if(e.type==="small")return"Main-Regular";if(e.type==="large")return"Size"+e.size+"-Regular";if(e.type==="stack")return"Size4-Regular";throw new Error("Add support for delim type '"+e.type+"' here.")},qa=function(e,t,a,n){for(var l=Math.min(2,3-n.style.size),o=l;o<a.length&&a[o].type!=="stack";o++){var h=pe(e,yl(a[o]),"math"),c=h.height+h.depth;if(a[o].type==="small"){var p=n.havingBaseStyle(a[o].style);c*=p.sizeMultiplier}if(c>t)return a[o]}return a[a.length-1]},Ra=function(e,t,a,n,l,o){e==="<"||e==="\\lt"||e==="⟨"?e="\\langle":(e===">"||e==="\\gt"||e==="⟩")&&(e="\\rangle");var h;q.contains(Na,e)?h=gl:q.contains(Ba,e)?h=Ea:h=bl;var c=qa(e,t,h,n);return c.type==="small"?hl(e,c.style,a,n,l,o):c.type==="large"?Ca(e,c.size,a,n,l,o):Da(e,t,a,n,l,o)},xl=function(e,t,a,n,l,o){var h=n.fontMetrics().axisHeight*n.sizeMultiplier,c=901,p=5/n.fontMetrics().ptPerEm,g=Math.max(t-h,a+h),b=Math.max(g/500*c,2*g-p);return Ra(e,b,!0,n,l,o)},O0={sqrtImage:fl,sizedDelim:vl,sizeToMaxHeight:ve,customSizedDelim:Ra,leftRightDelim:xl},Mr={"\\bigl":{mclass:"mopen",size:1},"\\Bigl":{mclass:"mopen",size:2},"\\biggl":{mclass:"mopen",size:3},"\\Biggl":{mclass:"mopen",size:4},"\\bigr":{mclass:"mclose",size:1},"\\Bigr":{mclass:"mclose",size:2},"\\biggr":{mclass:"mclose",size:3},"\\Biggr":{mclass:"mclose",size:4},"\\bigm":{mclass:"mrel",size:1},"\\Bigm":{mclass:"mrel",size:2},"\\biggm":{mclass:"mrel",size:3},"\\Biggm":{mclass:"mrel",size:4},"\\big":{mclass:"mord",size:1},"\\Big":{mclass:"mord",size:2},"\\bigg":{mclass:"mord",size:3},"\\Bigg":{mclass:"mord",size:4}},wl=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","⌊","⌋","\\lceil","\\rceil","⌈","⌉","<",">","\\langle","⟨","\\rangle","⟩","\\lt","\\gt","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","⟮","⟯","\\lmoustache","\\rmoustache","⎰","⎱","/","\\backslash","|","\\vert","\\|","\\Vert","\\uparrow","\\Uparrow","\\downarrow","\\Downarrow","\\updownarrow","\\Updownarrow","."];function $e(r,e){var t=We(r);if(t&&q.contains(wl,t.text))return t;throw t?new A("Invalid delimiter '"+t.text+"' after '"+e.funcName+"'",r):new A("Invalid delimiter type '"+r.type+"'",r)}D({type:"delimsizing",names:["\\bigl","\\Bigl","\\biggl","\\Biggl","\\bigr","\\Bigr","\\biggr","\\Biggr","\\bigm","\\Bigm","\\biggm","\\Biggm","\\big","\\Big","\\bigg","\\Bigg"],props:{numArgs:1,argTypes:["primitive"]},handler:(r,e)=>{var t=$e(e[0],r);return{type:"delimsizing",mode:r.parser.mode,size:Mr[r.funcName].size,mclass:Mr[r.funcName].mclass,delim:t.text}},htmlBuilder:(r,e)=>r.delim==="."?y.makeSpan([r.mclass]):O0.sizedDelim(r.delim,r.size,e,r.mode,[r.mclass]),mathmlBuilder:r=>{var e=[];r.delim!=="."&&e.push(w0(r.delim,r.mode));var t=new S.MathNode("mo",e);r.mclass==="mopen"||r.mclass==="mclose"?t.setAttribute("fence","true"):t.setAttribute("fence","false"),t.setAttribute("stretchy","true");var a=T(O0.sizeToMaxHeight[r.size]);return t.setAttribute("minsize",a),t.setAttribute("maxsize",a),t}});function zr(r){if(!r.body)throw new Error("Bug: The leftright ParseNode wasn't fully parsed.")}D({type:"leftright-right",names:["\\right"],props:{numArgs:1,primitive:!0},handler:(r,e)=>{var t=r.parser.gullet.macros.get("\\current@color");if(t&&typeof t!="string")throw new A("\\current@color set to non-string in \\right");return{type:"leftright-right",mode:r.parser.mode,delim:$e(e[0],r).text,color:t}}});D({type:"leftright",names:["\\left"],props:{numArgs:1,primitive:!0},handler:(r,e)=>{var t=$e(e[0],r),a=r.parser;++a.leftrightDepth;var n=a.parseExpression(!1);--a.leftrightDepth,a.expect("\\right",!1);var l=H(a.parseFunction(),"leftright-right");return{type:"leftright",mode:a.mode,body:n,left:t.text,right:l.delim,rightColor:l.color}},htmlBuilder:(r,e)=>{zr(r);for(var t=l0(r.body,e,!0,["mopen","mclose"]),a=0,n=0,l=!1,o=0;o<t.length;o++)t[o].isMiddle?l=!0:(a=Math.max(t[o].height,a),n=Math.max(t[o].depth,n));a*=e.sizeMultiplier,n*=e.sizeMultiplier;var h;if(r.left==="."?h=ye(e,["mopen"]):h=O0.leftRightDelim(r.left,a,n,e,r.mode,["mopen"]),t.unshift(h),l)for(var c=1;c<t.length;c++){var p=t[c],g=p.isMiddle;g&&(t[c]=O0.leftRightDelim(g.delim,a,n,g.options,r.mode,[]))}var b;if(r.right===".")b=ye(e,["mclose"]);else{var x=r.rightColor?e.withColor(r.rightColor):e;b=O0.leftRightDelim(r.right,a,n,x,r.mode,["mclose"])}return t.push(b),y.makeSpan(["minner"],t,e)},mathmlBuilder:(r,e)=>{zr(r);var t=d0(r.body,e);if(r.left!=="."){var a=new S.MathNode("mo",[w0(r.left,r.mode)]);a.setAttribute("fence","true"),t.unshift(a)}if(r.right!=="."){var n=new S.MathNode("mo",[w0(r.right,r.mode)]);n.setAttribute("fence","true"),r.rightColor&&n.setAttribute("mathcolor",r.rightColor),t.push(n)}return Rt(t)}});D({type:"middle",names:["\\middle"],props:{numArgs:1,primitive:!0},handler:(r,e)=>{var t=$e(e[0],r);if(!r.parser.leftrightDepth)throw new A("\\middle without preceding \\left",t);return{type:"middle",mode:r.parser.mode,delim:t.text}},htmlBuilder:(r,e)=>{var t;if(r.delim===".")t=ye(e,[]);else{t=O0.sizedDelim(r.delim,1,e,r.mode,[]);var a={delim:r.delim,options:e};t.isMiddle=a}return t},mathmlBuilder:(r,e)=>{var t=r.delim==="\\vert"||r.delim==="|"?w0("|","text"):w0(r.delim,r.mode),a=new S.MathNode("mo",[t]);return a.setAttribute("fence","true"),a.setAttribute("lspace","0.05em"),a.setAttribute("rspace","0.05em"),a}});var Pt=(r,e)=>{var t=y.wrapFragment(U(r.body,e),e),a=r.label.slice(1),n=e.sizeMultiplier,l,o=0,h=q.isCharacterBox(r.body);if(a==="sout")l=y.makeSpan(["stretchy","sout"]),l.height=e.fontMetrics().defaultRuleThickness/n,o=-.5*e.fontMetrics().xHeight;else if(a==="phase"){var c=_({number:.6,unit:"pt"},e),p=_({number:.35,unit:"ex"},e),g=e.havingBaseSizing();n=n/g.sizeMultiplier;var b=t.height+t.depth+c+p;t.style.paddingLeft=T(b/2+c);var x=Math.floor(1e3*b*n),w=d1(x),M=new X0([new Q0("phase",w)],{width:"400em",height:T(x/1e3),viewBox:"0 0 400000 "+x,preserveAspectRatio:"xMinYMin slice"});l=y.makeSvgSpan(["hide-tail"],[M],e),l.style.height=T(b),o=t.depth+c+p}else{/cancel/.test(a)?h||t.classes.push("cancel-pad"):a==="angl"?t.classes.push("anglpad"):t.classes.push("boxpad");var C=0,N=0,E=0;/box/.test(a)?(E=Math.max(e.fontMetrics().fboxrule,e.minRuleThickness),C=e.fontMetrics().fboxsep+(a==="colorbox"?0:E),N=C):a==="angl"?(E=Math.max(e.fontMetrics().defaultRuleThickness,e.minRuleThickness),C=4*E,N=Math.max(0,.25-t.depth)):(C=h?.2:0,N=C),l=I0.encloseSpan(t,a,C,N,e),/fbox|boxed|fcolorbox/.test(a)?(l.style.borderStyle="solid",l.style.borderWidth=T(E)):a==="angl"&&E!==.049&&(l.style.borderTopWidth=T(E),l.style.borderRightWidth=T(E)),o=t.depth+N,r.backgroundColor&&(l.style.backgroundColor=r.backgroundColor,r.borderColor&&(l.style.borderColor=r.borderColor))}var L;if(r.backgroundColor)L=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:l,shift:o},{type:"elem",elem:t,shift:0}]},e);else{var F=/cancel|phase/.test(a)?["svg-align"]:[];L=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:t,shift:0},{type:"elem",elem:l,shift:o,wrapperClasses:F}]},e)}return/cancel/.test(a)&&(L.height=t.height,L.depth=t.depth),/cancel/.test(a)&&!h?y.makeSpan(["mord","cancel-lap"],[L],e):y.makeSpan(["mord"],[L],e)},Ht=(r,e)=>{var t=0,a=new S.MathNode(r.label.indexOf("colorbox")>-1?"mpadded":"menclose",[$(r.body,e)]);switch(r.label){case"\\cancel":a.setAttribute("notation","updiagonalstrike");break;case"\\bcancel":a.setAttribute("notation","downdiagonalstrike");break;case"\\phase":a.setAttribute("notation","phasorangle");break;case"\\sout":a.setAttribute("notation","horizontalstrike");break;case"\\fbox":a.setAttribute("notation","box");break;case"\\angl":a.setAttribute("notation","actuarial");break;case"\\fcolorbox":case"\\colorbox":if(t=e.fontMetrics().fboxsep*e.fontMetrics().ptPerEm,a.setAttribute("width","+"+2*t+"pt"),a.setAttribute("height","+"+2*t+"pt"),a.setAttribute("lspace",t+"pt"),a.setAttribute("voffset",t+"pt"),r.label==="\\fcolorbox"){var n=Math.max(e.fontMetrics().fboxrule,e.minRuleThickness);a.setAttribute("style","border: "+n+"em solid "+String(r.borderColor))}break;case"\\xcancel":a.setAttribute("notation","updiagonalstrike downdiagonalstrike");break}return r.backgroundColor&&a.setAttribute("mathbackground",r.backgroundColor),a};D({type:"enclose",names:["\\colorbox"],props:{numArgs:2,allowedInText:!0,argTypes:["color","text"]},handler(r,e,t){var{parser:a,funcName:n}=r,l=H(e[0],"color-token").color,o=e[1];return{type:"enclose",mode:a.mode,label:n,backgroundColor:l,body:o}},htmlBuilder:Pt,mathmlBuilder:Ht});D({type:"enclose",names:["\\fcolorbox"],props:{numArgs:3,allowedInText:!0,argTypes:["color","color","text"]},handler(r,e,t){var{parser:a,funcName:n}=r,l=H(e[0],"color-token").color,o=H(e[1],"color-token").color,h=e[2];return{type:"enclose",mode:a.mode,label:n,backgroundColor:o,borderColor:l,body:h}},htmlBuilder:Pt,mathmlBuilder:Ht});D({type:"enclose",names:["\\fbox"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!0},handler(r,e){var{parser:t}=r;return{type:"enclose",mode:t.mode,label:"\\fbox",body:e[0]}}});D({type:"enclose",names:["\\cancel","\\bcancel","\\xcancel","\\sout","\\phase"],props:{numArgs:1},handler(r,e){var{parser:t,funcName:a}=r,n=e[0];return{type:"enclose",mode:t.mode,label:a,body:n}},htmlBuilder:Pt,mathmlBuilder:Ht});D({type:"enclose",names:["\\angl"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!1},handler(r,e){var{parser:t}=r;return{type:"enclose",mode:t.mode,label:"\\angl",body:e[0]}}});var Oa={};function T0(r){for(var{type:e,names:t,props:a,handler:n,htmlBuilder:l,mathmlBuilder:o}=r,h={type:e,numArgs:a.numArgs||0,allowedInText:!1,numOptionalArgs:0,handler:n},c=0;c<t.length;++c)Oa[t[c]]=h;l&&(Pe[e]=l),o&&(He[e]=o)}var La={};function m(r,e){La[r]=e}function Ar(r){var e=[];r.consumeSpaces();var t=r.fetch().text;for(t==="\\relax"&&(r.consume(),r.consumeSpaces(),t=r.fetch().text);t==="\\hline"||t==="\\hdashline";)r.consume(),e.push(t==="\\hdashline"),r.consumeSpaces(),t=r.fetch().text;return e}var je=r=>{var e=r.parser.settings;if(!e.displayMode)throw new A("{"+r.envName+"} can be used only in display mode.")};function Vt(r){if(r.indexOf("ed")===-1)return r.indexOf("*")===-1}function j0(r,e,t){var{hskipBeforeAndAfter:a,addJot:n,cols:l,arraystretch:o,colSeparationType:h,autoTag:c,singleRow:p,emptySingleRow:g,maxNumCols:b,leqno:x}=e;if(r.gullet.beginGroup(),p||r.gullet.macros.set("\\cr","\\\\\\relax"),!o){var w=r.gullet.expandMacroAsText("\\arraystretch");if(w==null)o=1;else if(o=parseFloat(w),!o||o<0)throw new A("Invalid \\arraystretch: "+w)}r.gullet.beginGroup();var M=[],C=[M],N=[],E=[],L=c!=null?[]:void 0;function F(){c&&r.gullet.macros.set("\\@eqnsw","1",!0)}function Y(){L&&(r.gullet.macros.get("\\df@tag")?(L.push(r.subparse([new x0("\\df@tag")])),r.gullet.macros.set("\\df@tag",void 0,!0)):L.push(!!c&&r.gullet.macros.get("\\@eqnsw")==="1"))}for(F(),E.push(Ar(r));;){var V=r.parseExpression(!1,p?"\\end":"\\\\");r.gullet.endGroup(),r.gullet.beginGroup(),V={type:"ordgroup",mode:r.mode,body:V},t&&(V={type:"styling",mode:r.mode,style:t,body:[V]}),M.push(V);var W=r.fetch().text;if(W==="&"){if(b&&M.length===b){if(p||h)throw new A("Too many tab characters: &",r.nextToken);r.settings.reportNonstrict("textEnv","Too few columns specified in the {array} column argument.")}r.consume()}else if(W==="\\end"){Y(),M.length===1&&V.type==="styling"&&V.body[0].body.length===0&&(C.length>1||!g)&&C.pop(),E.length<C.length+1&&E.push([]);break}else if(W==="\\\\"){r.consume();var G=void 0;r.gullet.future().text!==" "&&(G=r.parseSizeGroup(!0)),N.push(G?G.value:null),Y(),E.push(Ar(r)),M=[],C.push(M),F()}else throw new A("Expected & or \\\\ or \\cr or \\end",r.nextToken)}return r.gullet.endGroup(),r.gullet.endGroup(),{type:"array",mode:r.mode,addJot:n,arraystretch:o,body:C,cols:l,rowGaps:N,hskipBeforeAndAfter:a,hLinesBeforeRow:E,colSeparationType:h,tags:L,leqno:x}}function Ut(r){return r.slice(0,1)==="d"?"display":"text"}var C0=function(e,t){var a,n,l=e.body.length,o=e.hLinesBeforeRow,h=0,c=new Array(l),p=[],g=Math.max(t.fontMetrics().arrayRuleWidth,t.minRuleThickness),b=1/t.fontMetrics().ptPerEm,x=5*b;if(e.colSeparationType&&e.colSeparationType==="small"){var w=t.havingStyle(O.SCRIPT).sizeMultiplier;x=.2778*(w/t.sizeMultiplier)}var M=e.colSeparationType==="CD"?_({number:3,unit:"ex"},t):12*b,C=3*b,N=e.arraystretch*M,E=.7*N,L=.3*N,F=0;function Y(ze){for(var Ae=0;Ae<ze.length;++Ae)Ae>0&&(F+=.25),p.push({pos:F,isDashed:ze[Ae]})}for(Y(o[0]),a=0;a<e.body.length;++a){var V=e.body[a],W=E,G=L;h<V.length&&(h=V.length);var J=new Array(V.length);for(n=0;n<V.length;++n){var X=U(V[n],t);G<X.depth&&(G=X.depth),W<X.height&&(W=X.height),J[n]=X}var B0=e.rowGaps[a],i0=0;B0&&(i0=_(B0,t),i0>0&&(i0+=L,G<i0&&(G=i0),i0=0)),e.addJot&&(G+=C),J.height=W,J.depth=G,F+=W,J.pos=F,F+=G+i0,c[a]=J,Y(o[a+1])}var n0=F/2+t.fontMetrics().axisHeight,K0=e.cols||[],h0=[],k0,P0,re=[];if(e.tags&&e.tags.some(ze=>ze))for(a=0;a<l;++a){var ae=c[a],Ze=ae.pos-n0,H0=e.tags[a],V0=void 0;H0===!0?V0=y.makeSpan(["eqn-num"],[],t):H0===!1?V0=y.makeSpan([],[],t):V0=y.makeSpan([],l0(H0,t,!0),t),V0.depth=ae.depth,V0.height=ae.height,re.push({type:"elem",elem:V0,shift:Ze})}for(n=0,P0=0;n<h||P0<K0.length;++n,++P0){for(var b0=K0[P0]||{},fe=!0;b0.type==="separator";){if(fe||(k0=y.makeSpan(["arraycolsep"],[]),k0.style.width=T(t.fontMetrics().doubleRuleSep),h0.push(k0)),b0.separator==="|"||b0.separator===":"){var Je=b0.separator==="|"?"solid":"dashed",ne=y.makeSpan(["vertical-separator"],[],t);ne.style.height=T(F),ne.style.borderRightWidth=T(g),ne.style.borderRightStyle=Je,ne.style.margin="0 "+T(-g/2);var $t=F-n0;$t&&(ne.style.verticalAlign=T(-$t)),h0.push(ne)}else throw new A("Invalid separator type: "+b0.separator);P0++,b0=K0[P0]||{},fe=!1}if(!(n>=h)){var le=void 0;(n>0||e.hskipBeforeAndAfter)&&(le=q.deflt(b0.pregap,x),le!==0&&(k0=y.makeSpan(["arraycolsep"],[]),k0.style.width=T(le),h0.push(k0)));var ie=[];for(a=0;a<l;++a){var Se=c[a],Me=Se[n];if(Me){var Ja=Se.pos-n0;Me.depth=Se.depth,Me.height=Se.height,ie.push({type:"elem",elem:Me,shift:Ja})}}ie=y.makeVList({positionType:"individualShift",children:ie},t),ie=y.makeSpan(["col-align-"+(b0.align||"c")],[ie]),h0.push(ie),(n<h-1||e.hskipBeforeAndAfter)&&(le=q.deflt(b0.postgap,x),le!==0&&(k0=y.makeSpan(["arraycolsep"],[]),k0.style.width=T(le),h0.push(k0)))}}if(c=y.makeSpan(["mtable"],h0),p.length>0){for(var Qa=y.makeLineSpan("hline",t,g),_a=y.makeLineSpan("hdashline",t,g),Qe=[{type:"elem",elem:c,shift:0}];p.length>0;){var jt=p.pop(),Kt=jt.pos-n0;jt.isDashed?Qe.push({type:"elem",elem:_a,shift:Kt}):Qe.push({type:"elem",elem:Qa,shift:Kt})}c=y.makeVList({positionType:"individualShift",children:Qe},t)}if(re.length===0)return y.makeSpan(["mord"],[c],t);var _e=y.makeVList({positionType:"individualShift",children:re},t);return _e=y.makeSpan(["tag"],[_e],t),y.makeFragment([c,_e])},kl={c:"center ",l:"left ",r:"right "},D0=function(e,t){for(var a=[],n=new S.MathNode("mtd",[],["mtr-glue"]),l=new S.MathNode("mtd",[],["mml-eqn-num"]),o=0;o<e.body.length;o++){for(var h=e.body[o],c=[],p=0;p<h.length;p++)c.push(new S.MathNode("mtd",[$(h[p],t)]));e.tags&&e.tags[o]&&(c.unshift(n),c.push(n),e.leqno?c.unshift(l):c.push(l)),a.push(new S.MathNode("mtr",c))}var g=new S.MathNode("mtable",a),b=e.arraystretch===.5?.1:.16+e.arraystretch-1+(e.addJot?.09:0);g.setAttribute("rowspacing",T(b));var x="",w="";if(e.cols&&e.cols.length>0){var M=e.cols,C="",N=!1,E=0,L=M.length;M[0].type==="separator"&&(x+="top ",E=1),M[M.length-1].type==="separator"&&(x+="bottom ",L-=1);for(var F=E;F<L;F++)M[F].type==="align"?(w+=kl[M[F].align],N&&(C+="none "),N=!0):M[F].type==="separator"&&N&&(C+=M[F].separator==="|"?"solid ":"dashed ",N=!1);g.setAttribute("columnalign",w.trim()),/[sd]/.test(C)&&g.setAttribute("columnlines",C.trim())}if(e.colSeparationType==="align"){for(var Y=e.cols||[],V="",W=1;W<Y.length;W++)V+=W%2?"0em ":"1em ";g.setAttribute("columnspacing",V.trim())}else e.colSeparationType==="alignat"||e.colSeparationType==="gather"?g.setAttribute("columnspacing","0em"):e.colSeparationType==="small"?g.setAttribute("columnspacing","0.2778em"):e.colSeparationType==="CD"?g.setAttribute("columnspacing","0.5em"):g.setAttribute("columnspacing","1em");var G="",J=e.hLinesBeforeRow;x+=J[0].length>0?"left ":"",x+=J[J.length-1].length>0?"right ":"";for(var X=1;X<J.length-1;X++)G+=J[X].length===0?"none ":J[X][0]?"dashed ":"solid ";return/[sd]/.test(G)&&g.setAttribute("rowlines",G.trim()),x!==""&&(g=new S.MathNode("menclose",[g]),g.setAttribute("notation",x.trim())),e.arraystretch&&e.arraystretch<1&&(g=new S.MathNode("mstyle",[g]),g.setAttribute("scriptlevel","1")),g},Ia=function(e,t){e.envName.indexOf("ed")===-1&&je(e);var a=[],n=e.envName.indexOf("at")>-1?"alignat":"align",l=e.envName==="split",o=j0(e.parser,{cols:a,addJot:!0,autoTag:l?void 0:Vt(e.envName),emptySingleRow:!0,colSeparationType:n,maxNumCols:l?2:void 0,leqno:e.parser.settings.leqno},"display"),h,c=0,p={type:"ordgroup",mode:e.mode,body:[]};if(t[0]&&t[0].type==="ordgroup"){for(var g="",b=0;b<t[0].body.length;b++){var x=H(t[0].body[b],"textord");g+=x.text}h=Number(g),c=h*2}var w=!c;o.body.forEach(function(E){for(var L=1;L<E.length;L+=2){var F=H(E[L],"styling"),Y=H(F.body[0],"ordgroup");Y.body.unshift(p)}if(w)c<E.length&&(c=E.length);else{var V=E.length/2;if(h<V)throw new A("Too many math in a row: "+("expected "+h+", but got "+V),E[0])}});for(var M=0;M<c;++M){var C="r",N=0;M%2===1?C="l":M>0&&w&&(N=1),a[M]={type:"align",align:C,pregap:N,postgap:0}}return o.colSeparationType=w?"align":"alignat",o};T0({type:"array",names:["array","darray"],props:{numArgs:1},handler(r,e){var t=We(e[0]),a=t?[e[0]]:H(e[0],"ordgroup").body,n=a.map(function(o){var h=Lt(o),c=h.text;if("lcr".indexOf(c)!==-1)return{type:"align",align:c};if(c==="|")return{type:"separator",separator:"|"};if(c===":")return{type:"separator",separator:":"};throw new A("Unknown column alignment: "+c,o)}),l={cols:n,hskipBeforeAndAfter:!0,maxNumCols:n.length};return j0(r.parser,l,Ut(r.envName))},htmlBuilder:C0,mathmlBuilder:D0});T0({type:"array",names:["matrix","pmatrix","bmatrix","Bmatrix","vmatrix","Vmatrix","matrix*","pmatrix*","bmatrix*","Bmatrix*","vmatrix*","Vmatrix*"],props:{numArgs:0},handler(r){var e={matrix:null,pmatrix:["(",")"],bmatrix:["[","]"],Bmatrix:["\\{","\\}"],vmatrix:["|","|"],Vmatrix:["\\Vert","\\Vert"]}[r.envName.replace("*","")],t="c",a={hskipBeforeAndAfter:!1,cols:[{type:"align",align:t}]};if(r.envName.charAt(r.envName.length-1)==="*"){var n=r.parser;if(n.consumeSpaces(),n.fetch().text==="["){if(n.consume(),n.consumeSpaces(),t=n.fetch().text,"lcr".indexOf(t)===-1)throw new A("Expected l or c or r",n.nextToken);n.consume(),n.consumeSpaces(),n.expect("]"),n.consume(),a.cols=[{type:"align",align:t}]}}var l=j0(r.parser,a,Ut(r.envName)),o=Math.max(0,...l.body.map(h=>h.length));return l.cols=new Array(o).fill({type:"align",align:t}),e?{type:"leftright",mode:r.mode,body:[l],left:e[0],right:e[1],rightColor:void 0}:l},htmlBuilder:C0,mathmlBuilder:D0});T0({type:"array",names:["smallmatrix"],props:{numArgs:0},handler(r){var e={arraystretch:.5},t=j0(r.parser,e,"script");return t.colSeparationType="small",t},htmlBuilder:C0,mathmlBuilder:D0});T0({type:"array",names:["subarray"],props:{numArgs:1},handler(r,e){var t=We(e[0]),a=t?[e[0]]:H(e[0],"ordgroup").body,n=a.map(function(o){var h=Lt(o),c=h.text;if("lc".indexOf(c)!==-1)return{type:"align",align:c};throw new A("Unknown column alignment: "+c,o)});if(n.length>1)throw new A("{subarray} can contain only one column");var l={cols:n,hskipBeforeAndAfter:!1,arraystretch:.5};if(l=j0(r.parser,l,"script"),l.body.length>0&&l.body[0].length>1)throw new A("{subarray} can contain only one column");return l},htmlBuilder:C0,mathmlBuilder:D0});T0({type:"array",names:["cases","dcases","rcases","drcases"],props:{numArgs:0},handler(r){var e={arraystretch:1.2,cols:[{type:"align",align:"l",pregap:0,postgap:1},{type:"align",align:"l",pregap:0,postgap:0}]},t=j0(r.parser,e,Ut(r.envName));return{type:"leftright",mode:r.mode,body:[t],left:r.envName.indexOf("r")>-1?".":"\\{",right:r.envName.indexOf("r")>-1?"\\}":".",rightColor:void 0}},htmlBuilder:C0,mathmlBuilder:D0});T0({type:"array",names:["align","align*","aligned","split"],props:{numArgs:0},handler:Ia,htmlBuilder:C0,mathmlBuilder:D0});T0({type:"array",names:["gathered","gather","gather*"],props:{numArgs:0},handler(r){q.contains(["gather","gather*"],r.envName)&&je(r);var e={cols:[{type:"align",align:"c"}],addJot:!0,colSeparationType:"gather",autoTag:Vt(r.envName),emptySingleRow:!0,leqno:r.parser.settings.leqno};return j0(r.parser,e,"display")},htmlBuilder:C0,mathmlBuilder:D0});T0({type:"array",names:["alignat","alignat*","alignedat"],props:{numArgs:1},handler:Ia,htmlBuilder:C0,mathmlBuilder:D0});T0({type:"array",names:["equation","equation*"],props:{numArgs:0},handler(r){je(r);var e={autoTag:Vt(r.envName),emptySingleRow:!0,singleRow:!0,maxNumCols:1,leqno:r.parser.settings.leqno};return j0(r.parser,e,"display")},htmlBuilder:C0,mathmlBuilder:D0});T0({type:"array",names:["CD"],props:{numArgs:0},handler(r){return je(r),ol(r.parser)},htmlBuilder:C0,mathmlBuilder:D0});m("\\nonumber","\\gdef\\@eqnsw{0}");m("\\notag","\\nonumber");D({type:"text",names:["\\hline","\\hdashline"],props:{numArgs:0,allowedInText:!0,allowedInMath:!0},handler(r,e){throw new A(r.funcName+" valid only within array environment")}});var Tr=Oa;D({type:"environment",names:["\\begin","\\end"],props:{numArgs:1,argTypes:["text"]},handler(r,e){var{parser:t,funcName:a}=r,n=e[0];if(n.type!=="ordgroup")throw new A("Invalid environment name",n);for(var l="",o=0;o<n.body.length;++o)l+=H(n.body[o],"textord").text;if(a==="\\begin"){if(!Tr.hasOwnProperty(l))throw new A("No such environment: "+l,n);var h=Tr[l],{args:c,optArgs:p}=t.parseArguments("\\begin{"+l+"}",h),g={mode:t.mode,envName:l,parser:t},b=h.handler(g,c,p);t.expect("\\end",!1);var x=t.nextToken,w=H(t.parseFunction(),"environment");if(w.name!==l)throw new A("Mismatch: \\begin{"+l+"} matched by \\end{"+w.name+"}",x);return b}return{type:"environment",mode:t.mode,name:l,nameGroup:n}}});var Fa=(r,e)=>{var t=r.font,a=e.withFont(t);return U(r.body,a)},Pa=(r,e)=>{var t=r.font,a=e.withFont(t);return $(r.body,a)},Cr={"\\Bbb":"\\mathbb","\\bold":"\\mathbf","\\frak":"\\mathfrak","\\bm":"\\boldsymbol"};D({type:"font",names:["\\mathrm","\\mathit","\\mathbf","\\mathnormal","\\mathsfit","\\mathbb","\\mathcal","\\mathfrak","\\mathscr","\\mathsf","\\mathtt","\\Bbb","\\bold","\\frak"],props:{numArgs:1,allowedInArgument:!0},handler:(r,e)=>{var{parser:t,funcName:a}=r,n=Ve(e[0]),l=a;return l in Cr&&(l=Cr[l]),{type:"font",mode:t.mode,font:l.slice(1),body:n}},htmlBuilder:Fa,mathmlBuilder:Pa});D({type:"mclass",names:["\\boldsymbol","\\bm"],props:{numArgs:1},handler:(r,e)=>{var{parser:t}=r,a=e[0],n=q.isCharacterBox(a);return{type:"mclass",mode:t.mode,mclass:Xe(a),body:[{type:"font",mode:t.mode,font:"boldsymbol",body:a}],isCharacterBox:n}}});D({type:"font",names:["\\rm","\\sf","\\tt","\\bf","\\it","\\cal"],props:{numArgs:0,allowedInText:!0},handler:(r,e)=>{var{parser:t,funcName:a,breakOnTokenText:n}=r,{mode:l}=t,o=t.parseExpression(!0,n),h="math"+a.slice(1);return{type:"font",mode:l,font:h,body:{type:"ordgroup",mode:t.mode,body:o}}},htmlBuilder:Fa,mathmlBuilder:Pa});var Ha=(r,e)=>{var t=e;return r==="display"?t=t.id>=O.SCRIPT.id?t.text():O.DISPLAY:r==="text"&&t.size===O.DISPLAY.size?t=O.TEXT:r==="script"?t=O.SCRIPT:r==="scriptscript"&&(t=O.SCRIPTSCRIPT),t},Gt=(r,e)=>{var t=Ha(r.size,e.style),a=t.fracNum(),n=t.fracDen(),l;l=e.havingStyle(a);var o=U(r.numer,l,e);if(r.continued){var h=8.5/e.fontMetrics().ptPerEm,c=3.5/e.fontMetrics().ptPerEm;o.height=o.height<h?h:o.height,o.depth=o.depth<c?c:o.depth}l=e.havingStyle(n);var p=U(r.denom,l,e),g,b,x;r.hasBarLine?(r.barSize?(b=_(r.barSize,e),g=y.makeLineSpan("frac-line",e,b)):g=y.makeLineSpan("frac-line",e),b=g.height,x=g.height):(g=null,b=0,x=e.fontMetrics().defaultRuleThickness);var w,M,C;t.size===O.DISPLAY.size||r.size==="display"?(w=e.fontMetrics().num1,b>0?M=3*x:M=7*x,C=e.fontMetrics().denom1):(b>0?(w=e.fontMetrics().num2,M=x):(w=e.fontMetrics().num3,M=3*x),C=e.fontMetrics().denom2);var N;if(g){var L=e.fontMetrics().axisHeight;w-o.depth-(L+.5*b)<M&&(w+=M-(w-o.depth-(L+.5*b))),L-.5*b-(p.height-C)<M&&(C+=M-(L-.5*b-(p.height-C)));var F=-(L-.5*b);N=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:p,shift:C},{type:"elem",elem:g,shift:F},{type:"elem",elem:o,shift:-w}]},e)}else{var E=w-o.depth-(p.height-C);E<M&&(w+=.5*(M-E),C+=.5*(M-E)),N=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:p,shift:C},{type:"elem",elem:o,shift:-w}]},e)}l=e.havingStyle(t),N.height*=l.sizeMultiplier/e.sizeMultiplier,N.depth*=l.sizeMultiplier/e.sizeMultiplier;var Y;t.size===O.DISPLAY.size?Y=e.fontMetrics().delim1:t.size===O.SCRIPTSCRIPT.size?Y=e.havingStyle(O.SCRIPT).fontMetrics().delim2:Y=e.fontMetrics().delim2;var V,W;return r.leftDelim==null?V=ye(e,["mopen"]):V=O0.customSizedDelim(r.leftDelim,Y,!0,e.havingStyle(t),r.mode,["mopen"]),r.continued?W=y.makeSpan([]):r.rightDelim==null?W=ye(e,["mclose"]):W=O0.customSizedDelim(r.rightDelim,Y,!0,e.havingStyle(t),r.mode,["mclose"]),y.makeSpan(["mord"].concat(l.sizingClasses(e)),[V,y.makeSpan(["mfrac"],[N]),W],e)},Yt=(r,e)=>{var t=new S.MathNode("mfrac",[$(r.numer,e),$(r.denom,e)]);if(!r.hasBarLine)t.setAttribute("linethickness","0px");else if(r.barSize){var a=_(r.barSize,e);t.setAttribute("linethickness",T(a))}var n=Ha(r.size,e.style);if(n.size!==e.style.size){t=new S.MathNode("mstyle",[t]);var l=n.size===O.DISPLAY.size?"true":"false";t.setAttribute("displaystyle",l),t.setAttribute("scriptlevel","0")}if(r.leftDelim!=null||r.rightDelim!=null){var o=[];if(r.leftDelim!=null){var h=new S.MathNode("mo",[new S.TextNode(r.leftDelim.replace("\\",""))]);h.setAttribute("fence","true"),o.push(h)}if(o.push(t),r.rightDelim!=null){var c=new S.MathNode("mo",[new S.TextNode(r.rightDelim.replace("\\",""))]);c.setAttribute("fence","true"),o.push(c)}return Rt(o)}return t};D({type:"genfrac",names:["\\dfrac","\\frac","\\tfrac","\\dbinom","\\binom","\\tbinom","\\\\atopfrac","\\\\bracefrac","\\\\brackfrac"],props:{numArgs:2,allowedInArgument:!0},handler:(r,e)=>{var{parser:t,funcName:a}=r,n=e[0],l=e[1],o,h=null,c=null,p="auto";switch(a){case"\\dfrac":case"\\frac":case"\\tfrac":o=!0;break;case"\\\\atopfrac":o=!1;break;case"\\dbinom":case"\\binom":case"\\tbinom":o=!1,h="(",c=")";break;case"\\\\bracefrac":o=!1,h="\\{",c="\\}";break;case"\\\\brackfrac":o=!1,h="[",c="]";break;default:throw new Error("Unrecognized genfrac command")}switch(a){case"\\dfrac":case"\\dbinom":p="display";break;case"\\tfrac":case"\\tbinom":p="text";break}return{type:"genfrac",mode:t.mode,continued:!1,numer:n,denom:l,hasBarLine:o,leftDelim:h,rightDelim:c,size:p,barSize:null}},htmlBuilder:Gt,mathmlBuilder:Yt});D({type:"genfrac",names:["\\cfrac"],props:{numArgs:2},handler:(r,e)=>{var{parser:t,funcName:a}=r,n=e[0],l=e[1];return{type:"genfrac",mode:t.mode,continued:!0,numer:n,denom:l,hasBarLine:!0,leftDelim:null,rightDelim:null,size:"display",barSize:null}}});D({type:"infix",names:["\\over","\\choose","\\atop","\\brace","\\brack"],props:{numArgs:0,infix:!0},handler(r){var{parser:e,funcName:t,token:a}=r,n;switch(t){case"\\over":n="\\frac";break;case"\\choose":n="\\binom";break;case"\\atop":n="\\\\atopfrac";break;case"\\brace":n="\\\\bracefrac";break;case"\\brack":n="\\\\brackfrac";break;default:throw new Error("Unrecognized infix genfrac command")}return{type:"infix",mode:e.mode,replaceWith:n,token:a}}});var Dr=["display","text","script","scriptscript"],Br=function(e){var t=null;return e.length>0&&(t=e,t=t==="."?null:t),t};D({type:"genfrac",names:["\\genfrac"],props:{numArgs:6,allowedInArgument:!0,argTypes:["math","math","size","text","math","math"]},handler(r,e){var{parser:t}=r,a=e[4],n=e[5],l=Ve(e[0]),o=l.type==="atom"&&l.family==="open"?Br(l.text):null,h=Ve(e[1]),c=h.type==="atom"&&h.family==="close"?Br(h.text):null,p=H(e[2],"size"),g,b=null;p.isBlank?g=!0:(b=p.value,g=b.number>0);var x="auto",w=e[3];if(w.type==="ordgroup"){if(w.body.length>0){var M=H(w.body[0],"textord");x=Dr[Number(M.text)]}}else w=H(w,"textord"),x=Dr[Number(w.text)];return{type:"genfrac",mode:t.mode,numer:a,denom:n,continued:!1,hasBarLine:g,barSize:b,leftDelim:o,rightDelim:c,size:x}},htmlBuilder:Gt,mathmlBuilder:Yt});D({type:"infix",names:["\\above"],props:{numArgs:1,argTypes:["size"],infix:!0},handler(r,e){var{parser:t,funcName:a,token:n}=r;return{type:"infix",mode:t.mode,replaceWith:"\\\\abovefrac",size:H(e[0],"size").value,token:n}}});D({type:"genfrac",names:["\\\\abovefrac"],props:{numArgs:3,argTypes:["math","size","math"]},handler:(r,e)=>{var{parser:t,funcName:a}=r,n=e[0],l=Jn(H(e[1],"infix").size),o=e[2],h=l.number>0;return{type:"genfrac",mode:t.mode,numer:n,denom:o,continued:!1,hasBarLine:h,barSize:l,leftDelim:null,rightDelim:null,size:"auto"}},htmlBuilder:Gt,mathmlBuilder:Yt});var Va=(r,e)=>{var t=e.style,a,n;r.type==="supsub"?(a=r.sup?U(r.sup,e.havingStyle(t.sup()),e):U(r.sub,e.havingStyle(t.sub()),e),n=H(r.base,"horizBrace")):n=H(r,"horizBrace");var l=U(n.base,e.havingBaseStyle(O.DISPLAY)),o=I0.svgSpan(n,e),h;if(n.isOver?(h=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:l},{type:"kern",size:.1},{type:"elem",elem:o}]},e),h.children[0].children[0].children[1].classes.push("svg-align")):(h=y.makeVList({positionType:"bottom",positionData:l.depth+.1+o.height,children:[{type:"elem",elem:o},{type:"kern",size:.1},{type:"elem",elem:l}]},e),h.children[0].children[0].children[0].classes.push("svg-align")),a){var c=y.makeSpan(["mord",n.isOver?"mover":"munder"],[h],e);n.isOver?h=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:c},{type:"kern",size:.2},{type:"elem",elem:a}]},e):h=y.makeVList({positionType:"bottom",positionData:c.depth+.2+a.height+a.depth,children:[{type:"elem",elem:a},{type:"kern",size:.2},{type:"elem",elem:c}]},e)}return y.makeSpan(["mord",n.isOver?"mover":"munder"],[h],e)},Sl=(r,e)=>{var t=I0.mathMLnode(r.label);return new S.MathNode(r.isOver?"mover":"munder",[$(r.base,e),t])};D({type:"horizBrace",names:["\\overbrace","\\underbrace"],props:{numArgs:1},handler(r,e){var{parser:t,funcName:a}=r;return{type:"horizBrace",mode:t.mode,label:a,isOver:/^\\over/.test(a),base:e[0]}},htmlBuilder:Va,mathmlBuilder:Sl});D({type:"href",names:["\\href"],props:{numArgs:2,argTypes:["url","original"],allowedInText:!0},handler:(r,e)=>{var{parser:t}=r,a=e[1],n=H(e[0],"url").url;return t.settings.isTrusted({command:"\\href",url:n})?{type:"href",mode:t.mode,href:n,body:t0(a)}:t.formatUnsupportedCmd("\\href")},htmlBuilder:(r,e)=>{var t=l0(r.body,e,!1);return y.makeAnchor(r.href,[],t,e)},mathmlBuilder:(r,e)=>{var t=$0(r.body,e);return t instanceof v0||(t=new v0("mrow",[t])),t.setAttribute("href",r.href),t}});D({type:"href",names:["\\url"],props:{numArgs:1,argTypes:["url"],allowedInText:!0},handler:(r,e)=>{var{parser:t}=r,a=H(e[0],"url").url;if(!t.settings.isTrusted({command:"\\url",url:a}))return t.formatUnsupportedCmd("\\url");for(var n=[],l=0;l<a.length;l++){var o=a[l];o==="~"&&(o="\\textasciitilde"),n.push({type:"textord",mode:"text",text:o})}var h={type:"text",mode:t.mode,font:"\\texttt",body:n};return{type:"href",mode:t.mode,href:a,body:t0(h)}}});D({type:"hbox",names:["\\hbox"],props:{numArgs:1,argTypes:["text"],allowedInText:!0,primitive:!0},handler(r,e){var{parser:t}=r;return{type:"hbox",mode:t.mode,body:t0(e[0])}},htmlBuilder(r,e){var t=l0(r.body,e,!1);return y.makeFragment(t)},mathmlBuilder(r,e){return new S.MathNode("mrow",d0(r.body,e))}});D({type:"html",names:["\\htmlClass","\\htmlId","\\htmlStyle","\\htmlData"],props:{numArgs:2,argTypes:["raw","original"],allowedInText:!0},handler:(r,e)=>{var{parser:t,funcName:a,token:n}=r,l=H(e[0],"raw").string,o=e[1];t.settings.strict&&t.settings.reportNonstrict("htmlExtension","HTML extension is disabled on strict mode");var h,c={};switch(a){case"\\htmlClass":c.class=l,h={command:"\\htmlClass",class:l};break;case"\\htmlId":c.id=l,h={command:"\\htmlId",id:l};break;case"\\htmlStyle":c.style=l,h={command:"\\htmlStyle",style:l};break;case"\\htmlData":{for(var p=l.split(","),g=0;g<p.length;g++){var b=p[g].split("=");if(b.length!==2)throw new A("Error parsing key-value for \\htmlData");c["data-"+b[0].trim()]=b[1].trim()}h={command:"\\htmlData",attributes:c};break}default:throw new Error("Unrecognized html command")}return t.settings.isTrusted(h)?{type:"html",mode:t.mode,attributes:c,body:t0(o)}:t.formatUnsupportedCmd(a)},htmlBuilder:(r,e)=>{var t=l0(r.body,e,!1),a=["enclosing"];r.attributes.class&&a.push(...r.attributes.class.trim().split(/\s+/));var n=y.makeSpan(a,t,e);for(var l in r.attributes)l!=="class"&&r.attributes.hasOwnProperty(l)&&n.setAttribute(l,r.attributes[l]);return n},mathmlBuilder:(r,e)=>$0(r.body,e)});D({type:"htmlmathml",names:["\\html@mathml"],props:{numArgs:2,allowedInText:!0},handler:(r,e)=>{var{parser:t}=r;return{type:"htmlmathml",mode:t.mode,html:t0(e[0]),mathml:t0(e[1])}},htmlBuilder:(r,e)=>{var t=l0(r.html,e,!1);return y.makeFragment(t)},mathmlBuilder:(r,e)=>$0(r.mathml,e)});var ft=function(e){if(/^[-+]? *(\d+(\.\d*)?|\.\d+)$/.test(e))return{number:+e,unit:"bp"};var t=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(e);if(!t)throw new A("Invalid size: '"+e+"' in \\includegraphics");var a={number:+(t[1]+t[2]),unit:t[3]};if(!sa(a))throw new A("Invalid unit: '"+a.unit+"' in \\includegraphics.");return a};D({type:"includegraphics",names:["\\includegraphics"],props:{numArgs:1,numOptionalArgs:1,argTypes:["raw","url"],allowedInText:!1},handler:(r,e,t)=>{var{parser:a}=r,n={number:0,unit:"em"},l={number:.9,unit:"em"},o={number:0,unit:"em"},h="";if(t[0])for(var c=H(t[0],"raw").string,p=c.split(","),g=0;g<p.length;g++){var b=p[g].split("=");if(b.length===2){var x=b[1].trim();switch(b[0].trim()){case"alt":h=x;break;case"width":n=ft(x);break;case"height":l=ft(x);break;case"totalheight":o=ft(x);break;default:throw new A("Invalid key: '"+b[0]+"' in \\includegraphics.")}}}var w=H(e[0],"url").url;return h===""&&(h=w,h=h.replace(/^.*[\\/]/,""),h=h.substring(0,h.lastIndexOf("."))),a.settings.isTrusted({command:"\\includegraphics",url:w})?{type:"includegraphics",mode:a.mode,alt:h,width:n,height:l,totalheight:o,src:w}:a.formatUnsupportedCmd("\\includegraphics")},htmlBuilder:(r,e)=>{var t=_(r.height,e),a=0;r.totalheight.number>0&&(a=_(r.totalheight,e)-t);var n=0;r.width.number>0&&(n=_(r.width,e));var l={height:T(t+a)};n>0&&(l.width=T(n)),a>0&&(l.verticalAlign=T(-a));var o=new k1(r.src,r.alt,l);return o.height=t,o.depth=a,o},mathmlBuilder:(r,e)=>{var t=new S.MathNode("mglyph",[]);t.setAttribute("alt",r.alt);var a=_(r.height,e),n=0;if(r.totalheight.number>0&&(n=_(r.totalheight,e)-a,t.setAttribute("valign",T(-n))),t.setAttribute("height",T(a+n)),r.width.number>0){var l=_(r.width,e);t.setAttribute("width",T(l))}return t.setAttribute("src",r.src),t}});D({type:"kern",names:["\\kern","\\mkern","\\hskip","\\mskip"],props:{numArgs:1,argTypes:["size"],primitive:!0,allowedInText:!0},handler(r,e){var{parser:t,funcName:a}=r,n=H(e[0],"size");if(t.settings.strict){var l=a[1]==="m",o=n.value.unit==="mu";l?(o||t.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+a+" supports only mu units, "+("not "+n.value.unit+" units")),t.mode!=="math"&&t.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+a+" works only in math mode")):o&&t.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+a+" doesn't support mu units")}return{type:"kern",mode:t.mode,dimension:n.value}},htmlBuilder(r,e){return y.makeGlue(r.dimension,e)},mathmlBuilder(r,e){var t=_(r.dimension,e);return new S.SpaceNode(t)}});D({type:"lap",names:["\\mathllap","\\mathrlap","\\mathclap"],props:{numArgs:1,allowedInText:!0},handler:(r,e)=>{var{parser:t,funcName:a}=r,n=e[0];return{type:"lap",mode:t.mode,alignment:a.slice(5),body:n}},htmlBuilder:(r,e)=>{var t;r.alignment==="clap"?(t=y.makeSpan([],[U(r.body,e)]),t=y.makeSpan(["inner"],[t],e)):t=y.makeSpan(["inner"],[U(r.body,e)]);var a=y.makeSpan(["fix"],[]),n=y.makeSpan([r.alignment],[t,a],e),l=y.makeSpan(["strut"]);return l.style.height=T(n.height+n.depth),n.depth&&(l.style.verticalAlign=T(-n.depth)),n.children.unshift(l),n=y.makeSpan(["thinbox"],[n],e),y.makeSpan(["mord","vbox"],[n],e)},mathmlBuilder:(r,e)=>{var t=new S.MathNode("mpadded",[$(r.body,e)]);if(r.alignment!=="rlap"){var a=r.alignment==="llap"?"-1":"-0.5";t.setAttribute("lspace",a+"width")}return t.setAttribute("width","0px"),t}});D({type:"styling",names:["\\(","$"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(r,e){var{funcName:t,parser:a}=r,n=a.mode;a.switchMode("math");var l=t==="\\("?"\\)":"$",o=a.parseExpression(!1,l);return a.expect(l),a.switchMode(n),{type:"styling",mode:a.mode,style:"text",body:o}}});D({type:"text",names:["\\)","\\]"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(r,e){throw new A("Mismatched "+r.funcName)}});var Nr=(r,e)=>{switch(e.style.size){case O.DISPLAY.size:return r.display;case O.TEXT.size:return r.text;case O.SCRIPT.size:return r.script;case O.SCRIPTSCRIPT.size:return r.scriptscript;default:return r.text}};D({type:"mathchoice",names:["\\mathchoice"],props:{numArgs:4,primitive:!0},handler:(r,e)=>{var{parser:t}=r;return{type:"mathchoice",mode:t.mode,display:t0(e[0]),text:t0(e[1]),script:t0(e[2]),scriptscript:t0(e[3])}},htmlBuilder:(r,e)=>{var t=Nr(r,e),a=l0(t,e,!1);return y.makeFragment(a)},mathmlBuilder:(r,e)=>{var t=Nr(r,e);return $0(t,e)}});var Ua=(r,e,t,a,n,l,o)=>{r=y.makeSpan([],[r]);var h=t&&q.isCharacterBox(t),c,p;if(e){var g=U(e,a.havingStyle(n.sup()),a);p={elem:g,kern:Math.max(a.fontMetrics().bigOpSpacing1,a.fontMetrics().bigOpSpacing3-g.depth)}}if(t){var b=U(t,a.havingStyle(n.sub()),a);c={elem:b,kern:Math.max(a.fontMetrics().bigOpSpacing2,a.fontMetrics().bigOpSpacing4-b.height)}}var x;if(p&&c){var w=a.fontMetrics().bigOpSpacing5+c.elem.height+c.elem.depth+c.kern+r.depth+o;x=y.makeVList({positionType:"bottom",positionData:w,children:[{type:"kern",size:a.fontMetrics().bigOpSpacing5},{type:"elem",elem:c.elem,marginLeft:T(-l)},{type:"kern",size:c.kern},{type:"elem",elem:r},{type:"kern",size:p.kern},{type:"elem",elem:p.elem,marginLeft:T(l)},{type:"kern",size:a.fontMetrics().bigOpSpacing5}]},a)}else if(c){var M=r.height-o;x=y.makeVList({positionType:"top",positionData:M,children:[{type:"kern",size:a.fontMetrics().bigOpSpacing5},{type:"elem",elem:c.elem,marginLeft:T(-l)},{type:"kern",size:c.kern},{type:"elem",elem:r}]},a)}else if(p){var C=r.depth+o;x=y.makeVList({positionType:"bottom",positionData:C,children:[{type:"elem",elem:r},{type:"kern",size:p.kern},{type:"elem",elem:p.elem,marginLeft:T(l)},{type:"kern",size:a.fontMetrics().bigOpSpacing5}]},a)}else return r;var N=[x];if(c&&l!==0&&!h){var E=y.makeSpan(["mspace"],[],a);E.style.marginRight=T(l),N.unshift(E)}return y.makeSpan(["mop","op-limits"],N,a)},Ga=["\\smallint"],de=(r,e)=>{var t,a,n=!1,l;r.type==="supsub"?(t=r.sup,a=r.sub,l=H(r.base,"op"),n=!0):l=H(r,"op");var o=e.style,h=!1;o.size===O.DISPLAY.size&&l.symbol&&!q.contains(Ga,l.name)&&(h=!0);var c;if(l.symbol){var p=h?"Size2-Regular":"Size1-Regular",g="";if((l.name==="\\oiint"||l.name==="\\oiiint")&&(g=l.name.slice(1),l.name=g==="oiint"?"\\iint":"\\iiint"),c=y.makeSymbol(l.name,p,"math",e,["mop","op-symbol",h?"large-op":"small-op"]),g.length>0){var b=c.italic,x=y.staticSvg(g+"Size"+(h?"2":"1"),e);c=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:c,shift:0},{type:"elem",elem:x,shift:h?.08:0}]},e),l.name="\\"+g,c.classes.unshift("mop"),c.italic=b}}else if(l.body){var w=l0(l.body,e,!0);w.length===1&&w[0]instanceof M0?(c=w[0],c.classes[0]="mop"):c=y.makeSpan(["mop"],w,e)}else{for(var M=[],C=1;C<l.name.length;C++)M.push(y.mathsym(l.name[C],l.mode,e));c=y.makeSpan(["mop"],M,e)}var N=0,E=0;return(c instanceof M0||l.name==="\\oiint"||l.name==="\\oiiint")&&!l.suppressBaseShift&&(N=(c.height-c.depth)/2-e.fontMetrics().axisHeight,E=c.italic),n?Ua(c,t,a,e,o,E,N):(N&&(c.style.position="relative",c.style.top=T(N)),c)},ke=(r,e)=>{var t;if(r.symbol)t=new v0("mo",[w0(r.name,r.mode)]),q.contains(Ga,r.name)&&t.setAttribute("largeop","false");else if(r.body)t=new v0("mo",d0(r.body,e));else{t=new v0("mi",[new A0(r.name.slice(1))]);var a=new v0("mo",[w0("⁡","text")]);r.parentIsSupSub?t=new v0("mrow",[t,a]):t=ya([t,a])}return t},Ml={"∏":"\\prod","∐":"\\coprod","∑":"\\sum","⋀":"\\bigwedge","⋁":"\\bigvee","⋂":"\\bigcap","⋃":"\\bigcup","⨀":"\\bigodot","⨁":"\\bigoplus","⨂":"\\bigotimes","⨄":"\\biguplus","⨆":"\\bigsqcup"};D({type:"op",names:["\\coprod","\\bigvee","\\bigwedge","\\biguplus","\\bigcap","\\bigcup","\\intop","\\prod","\\sum","\\bigotimes","\\bigoplus","\\bigodot","\\bigsqcup","\\smallint","∏","∐","∑","⋀","⋁","⋂","⋃","⨀","⨁","⨂","⨄","⨆"],props:{numArgs:0},handler:(r,e)=>{var{parser:t,funcName:a}=r,n=a;return n.length===1&&(n=Ml[n]),{type:"op",mode:t.mode,limits:!0,parentIsSupSub:!1,symbol:!0,name:n}},htmlBuilder:de,mathmlBuilder:ke});D({type:"op",names:["\\mathop"],props:{numArgs:1,primitive:!0},handler:(r,e)=>{var{parser:t}=r,a=e[0];return{type:"op",mode:t.mode,limits:!1,parentIsSupSub:!1,symbol:!1,body:t0(a)}},htmlBuilder:de,mathmlBuilder:ke});var zl={"∫":"\\int","∬":"\\iint","∭":"\\iiint","∮":"\\oint","∯":"\\oiint","∰":"\\oiiint"};D({type:"op",names:["\\arcsin","\\arccos","\\arctan","\\arctg","\\arcctg","\\arg","\\ch","\\cos","\\cosec","\\cosh","\\cot","\\cotg","\\coth","\\csc","\\ctg","\\cth","\\deg","\\dim","\\exp","\\hom","\\ker","\\lg","\\ln","\\log","\\sec","\\sin","\\sinh","\\sh","\\tan","\\tanh","\\tg","\\th"],props:{numArgs:0},handler(r){var{parser:e,funcName:t}=r;return{type:"op",mode:e.mode,limits:!1,parentIsSupSub:!1,symbol:!1,name:t}},htmlBuilder:de,mathmlBuilder:ke});D({type:"op",names:["\\det","\\gcd","\\inf","\\lim","\\max","\\min","\\Pr","\\sup"],props:{numArgs:0},handler(r){var{parser:e,funcName:t}=r;return{type:"op",mode:e.mode,limits:!0,parentIsSupSub:!1,symbol:!1,name:t}},htmlBuilder:de,mathmlBuilder:ke});D({type:"op",names:["\\int","\\iint","\\iiint","\\oint","\\oiint","\\oiiint","∫","∬","∭","∮","∯","∰"],props:{numArgs:0},handler(r){var{parser:e,funcName:t}=r,a=t;return a.length===1&&(a=zl[a]),{type:"op",mode:e.mode,limits:!1,parentIsSupSub:!1,symbol:!0,name:a}},htmlBuilder:de,mathmlBuilder:ke});var Ya=(r,e)=>{var t,a,n=!1,l;r.type==="supsub"?(t=r.sup,a=r.sub,l=H(r.base,"operatorname"),n=!0):l=H(r,"operatorname");var o;if(l.body.length>0){for(var h=l.body.map(b=>{var x=b.text;return typeof x=="string"?{type:"textord",mode:b.mode,text:x}:b}),c=l0(h,e.withFont("mathrm"),!0),p=0;p<c.length;p++){var g=c[p];g instanceof M0&&(g.text=g.text.replace(/\u2212/,"-").replace(/\u2217/,"*"))}o=y.makeSpan(["mop"],c,e)}else o=y.makeSpan(["mop"],[],e);return n?Ua(o,t,a,e,e.style,0,0):o},Al=(r,e)=>{for(var t=d0(r.body,e.withFont("mathrm")),a=!0,n=0;n<t.length;n++){var l=t[n];if(!(l instanceof S.SpaceNode))if(l instanceof S.MathNode)switch(l.type){case"mi":case"mn":case"ms":case"mspace":case"mtext":break;case"mo":{var o=l.children[0];l.children.length===1&&o instanceof S.TextNode?o.text=o.text.replace(/\u2212/,"-").replace(/\u2217/,"*"):a=!1;break}default:a=!1}else a=!1}if(a){var h=t.map(g=>g.toText()).join("");t=[new S.TextNode(h)]}var c=new S.MathNode("mi",t);c.setAttribute("mathvariant","normal");var p=new S.MathNode("mo",[w0("⁡","text")]);return r.parentIsSupSub?new S.MathNode("mrow",[c,p]):S.newDocumentFragment([c,p])};D({type:"operatorname",names:["\\operatorname@","\\operatornamewithlimits"],props:{numArgs:1},handler:(r,e)=>{var{parser:t,funcName:a}=r,n=e[0];return{type:"operatorname",mode:t.mode,body:t0(n),alwaysHandleSupSub:a==="\\operatornamewithlimits",limits:!1,parentIsSupSub:!1}},htmlBuilder:Ya,mathmlBuilder:Al});m("\\operatorname","\\@ifstar\\operatornamewithlimits\\operatorname@");te({type:"ordgroup",htmlBuilder(r,e){return r.semisimple?y.makeFragment(l0(r.body,e,!1)):y.makeSpan(["mord"],l0(r.body,e,!0),e)},mathmlBuilder(r,e){return $0(r.body,e,!0)}});D({type:"overline",names:["\\overline"],props:{numArgs:1},handler(r,e){var{parser:t}=r,a=e[0];return{type:"overline",mode:t.mode,body:a}},htmlBuilder(r,e){var t=U(r.body,e.havingCrampedStyle()),a=y.makeLineSpan("overline-line",e),n=e.fontMetrics().defaultRuleThickness,l=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:t},{type:"kern",size:3*n},{type:"elem",elem:a},{type:"kern",size:n}]},e);return y.makeSpan(["mord","overline"],[l],e)},mathmlBuilder(r,e){var t=new S.MathNode("mo",[new S.TextNode("‾")]);t.setAttribute("stretchy","true");var a=new S.MathNode("mover",[$(r.body,e),t]);return a.setAttribute("accent","true"),a}});D({type:"phantom",names:["\\phantom"],props:{numArgs:1,allowedInText:!0},handler:(r,e)=>{var{parser:t}=r,a=e[0];return{type:"phantom",mode:t.mode,body:t0(a)}},htmlBuilder:(r,e)=>{var t=l0(r.body,e.withPhantom(),!1);return y.makeFragment(t)},mathmlBuilder:(r,e)=>{var t=d0(r.body,e);return new S.MathNode("mphantom",t)}});D({type:"hphantom",names:["\\hphantom"],props:{numArgs:1,allowedInText:!0},handler:(r,e)=>{var{parser:t}=r,a=e[0];return{type:"hphantom",mode:t.mode,body:a}},htmlBuilder:(r,e)=>{var t=y.makeSpan([],[U(r.body,e.withPhantom())]);if(t.height=0,t.depth=0,t.children)for(var a=0;a<t.children.length;a++)t.children[a].height=0,t.children[a].depth=0;return t=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:t}]},e),y.makeSpan(["mord"],[t],e)},mathmlBuilder:(r,e)=>{var t=d0(t0(r.body),e),a=new S.MathNode("mphantom",t),n=new S.MathNode("mpadded",[a]);return n.setAttribute("height","0px"),n.setAttribute("depth","0px"),n}});D({type:"vphantom",names:["\\vphantom"],props:{numArgs:1,allowedInText:!0},handler:(r,e)=>{var{parser:t}=r,a=e[0];return{type:"vphantom",mode:t.mode,body:a}},htmlBuilder:(r,e)=>{var t=y.makeSpan(["inner"],[U(r.body,e.withPhantom())]),a=y.makeSpan(["fix"],[]);return y.makeSpan(["mord","rlap"],[t,a],e)},mathmlBuilder:(r,e)=>{var t=d0(t0(r.body),e),a=new S.MathNode("mphantom",t),n=new S.MathNode("mpadded",[a]);return n.setAttribute("width","0px"),n}});D({type:"raisebox",names:["\\raisebox"],props:{numArgs:2,argTypes:["size","hbox"],allowedInText:!0},handler(r,e){var{parser:t}=r,a=H(e[0],"size").value,n=e[1];return{type:"raisebox",mode:t.mode,dy:a,body:n}},htmlBuilder(r,e){var t=U(r.body,e),a=_(r.dy,e);return y.makeVList({positionType:"shift",positionData:-a,children:[{type:"elem",elem:t}]},e)},mathmlBuilder(r,e){var t=new S.MathNode("mpadded",[$(r.body,e)]),a=r.dy.number+r.dy.unit;return t.setAttribute("voffset",a),t}});D({type:"internal",names:["\\relax"],props:{numArgs:0,allowedInText:!0,allowedInArgument:!0},handler(r){var{parser:e}=r;return{type:"internal",mode:e.mode}}});D({type:"rule",names:["\\rule"],props:{numArgs:2,numOptionalArgs:1,allowedInText:!0,allowedInMath:!0,argTypes:["size","size","size"]},handler(r,e,t){var{parser:a}=r,n=t[0],l=H(e[0],"size"),o=H(e[1],"size");return{type:"rule",mode:a.mode,shift:n&&H(n,"size").value,width:l.value,height:o.value}},htmlBuilder(r,e){var t=y.makeSpan(["mord","rule"],[],e),a=_(r.width,e),n=_(r.height,e),l=r.shift?_(r.shift,e):0;return t.style.borderRightWidth=T(a),t.style.borderTopWidth=T(n),t.style.bottom=T(l),t.width=a,t.height=n+l,t.depth=-l,t.maxFontSize=n*1.125*e.sizeMultiplier,t},mathmlBuilder(r,e){var t=_(r.width,e),a=_(r.height,e),n=r.shift?_(r.shift,e):0,l=e.color&&e.getColor()||"black",o=new S.MathNode("mspace");o.setAttribute("mathbackground",l),o.setAttribute("width",T(t)),o.setAttribute("height",T(a));var h=new S.MathNode("mpadded",[o]);return n>=0?h.setAttribute("height",T(n)):(h.setAttribute("height",T(n)),h.setAttribute("depth",T(-n))),h.setAttribute("voffset",T(n)),h}});function Wa(r,e,t){for(var a=l0(r,e,!1),n=e.sizeMultiplier/t.sizeMultiplier,l=0;l<a.length;l++){var o=a[l].classes.indexOf("sizing");o<0?Array.prototype.push.apply(a[l].classes,e.sizingClasses(t)):a[l].classes[o+1]==="reset-size"+e.size&&(a[l].classes[o+1]="reset-size"+t.size),a[l].height*=n,a[l].depth*=n}return y.makeFragment(a)}var Er=["\\tiny","\\sixptsize","\\scriptsize","\\footnotesize","\\small","\\normalsize","\\large","\\Large","\\LARGE","\\huge","\\Huge"],Tl=(r,e)=>{var t=e.havingSize(r.size);return Wa(r.body,t,e)};D({type:"sizing",names:Er,props:{numArgs:0,allowedInText:!0},handler:(r,e)=>{var{breakOnTokenText:t,funcName:a,parser:n}=r,l=n.parseExpression(!1,t);return{type:"sizing",mode:n.mode,size:Er.indexOf(a)+1,body:l}},htmlBuilder:Tl,mathmlBuilder:(r,e)=>{var t=e.havingSize(r.size),a=d0(r.body,t),n=new S.MathNode("mstyle",a);return n.setAttribute("mathsize",T(t.sizeMultiplier)),n}});D({type:"smash",names:["\\smash"],props:{numArgs:1,numOptionalArgs:1,allowedInText:!0},handler:(r,e,t)=>{var{parser:a}=r,n=!1,l=!1,o=t[0]&&H(t[0],"ordgroup");if(o)for(var h="",c=0;c<o.body.length;++c){var p=o.body[c];if(h=p.text,h==="t")n=!0;else if(h==="b")l=!0;else{n=!1,l=!1;break}}else n=!0,l=!0;var g=e[0];return{type:"smash",mode:a.mode,body:g,smashHeight:n,smashDepth:l}},htmlBuilder:(r,e)=>{var t=y.makeSpan([],[U(r.body,e)]);if(!r.smashHeight&&!r.smashDepth)return t;if(r.smashHeight&&(t.height=0,t.children))for(var a=0;a<t.children.length;a++)t.children[a].height=0;if(r.smashDepth&&(t.depth=0,t.children))for(var n=0;n<t.children.length;n++)t.children[n].depth=0;var l=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:t}]},e);return y.makeSpan(["mord"],[l],e)},mathmlBuilder:(r,e)=>{var t=new S.MathNode("mpadded",[$(r.body,e)]);return r.smashHeight&&t.setAttribute("height","0px"),r.smashDepth&&t.setAttribute("depth","0px"),t}});D({type:"sqrt",names:["\\sqrt"],props:{numArgs:1,numOptionalArgs:1},handler(r,e,t){var{parser:a}=r,n=t[0],l=e[0];return{type:"sqrt",mode:a.mode,body:l,index:n}},htmlBuilder(r,e){var t=U(r.body,e.havingCrampedStyle());t.height===0&&(t.height=e.fontMetrics().xHeight),t=y.wrapFragment(t,e);var a=e.fontMetrics(),n=a.defaultRuleThickness,l=n;e.style.id<O.TEXT.id&&(l=e.fontMetrics().xHeight);var o=n+l/4,h=t.height+t.depth+o+n,{span:c,ruleWidth:p,advanceWidth:g}=O0.sqrtImage(h,e),b=c.height-p;b>t.height+t.depth+o&&(o=(o+b-t.height-t.depth)/2);var x=c.height-t.height-o-p;t.style.paddingLeft=T(g);var w=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:t,wrapperClasses:["svg-align"]},{type:"kern",size:-(t.height+x)},{type:"elem",elem:c},{type:"kern",size:p}]},e);if(r.index){var M=e.havingStyle(O.SCRIPTSCRIPT),C=U(r.index,M,e),N=.6*(w.height-w.depth),E=y.makeVList({positionType:"shift",positionData:-N,children:[{type:"elem",elem:C}]},e),L=y.makeSpan(["root"],[E]);return y.makeSpan(["mord","sqrt"],[L,w],e)}else return y.makeSpan(["mord","sqrt"],[w],e)},mathmlBuilder(r,e){var{body:t,index:a}=r;return a?new S.MathNode("mroot",[$(t,e),$(a,e)]):new S.MathNode("msqrt",[$(t,e)])}});var qr={display:O.DISPLAY,text:O.TEXT,script:O.SCRIPT,scriptscript:O.SCRIPTSCRIPT};D({type:"styling",names:["\\displaystyle","\\textstyle","\\scriptstyle","\\scriptscriptstyle"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(r,e){var{breakOnTokenText:t,funcName:a,parser:n}=r,l=n.parseExpression(!0,t),o=a.slice(1,a.length-5);return{type:"styling",mode:n.mode,style:o,body:l}},htmlBuilder(r,e){var t=qr[r.style],a=e.havingStyle(t).withFont("");return Wa(r.body,a,e)},mathmlBuilder(r,e){var t=qr[r.style],a=e.havingStyle(t),n=d0(r.body,a),l=new S.MathNode("mstyle",n),o={display:["0","true"],text:["0","false"],script:["1","false"],scriptscript:["2","false"]},h=o[r.style];return l.setAttribute("scriptlevel",h[0]),l.setAttribute("displaystyle",h[1]),l}});var Cl=function(e,t){var a=e.base;if(a)if(a.type==="op"){var n=a.limits&&(t.style.size===O.DISPLAY.size||a.alwaysHandleSupSub);return n?de:null}else if(a.type==="operatorname"){var l=a.alwaysHandleSupSub&&(t.style.size===O.DISPLAY.size||a.limits);return l?Ya:null}else{if(a.type==="accent")return q.isCharacterBox(a.base)?It:null;if(a.type==="horizBrace"){var o=!e.sub;return o===a.isOver?Va:null}else return null}else return null};te({type:"supsub",htmlBuilder(r,e){var t=Cl(r,e);if(t)return t(r,e);var{base:a,sup:n,sub:l}=r,o=U(a,e),h,c,p=e.fontMetrics(),g=0,b=0,x=a&&q.isCharacterBox(a);if(n){var w=e.havingStyle(e.style.sup());h=U(n,w,e),x||(g=o.height-w.fontMetrics().supDrop*w.sizeMultiplier/e.sizeMultiplier)}if(l){var M=e.havingStyle(e.style.sub());c=U(l,M,e),x||(b=o.depth+M.fontMetrics().subDrop*M.sizeMultiplier/e.sizeMultiplier)}var C;e.style===O.DISPLAY?C=p.sup1:e.style.cramped?C=p.sup3:C=p.sup2;var N=e.sizeMultiplier,E=T(.5/p.ptPerEm/N),L=null;if(c){var F=r.base&&r.base.type==="op"&&r.base.name&&(r.base.name==="\\oiint"||r.base.name==="\\oiiint");(o instanceof M0||F)&&(L=T(-o.italic))}var Y;if(h&&c){g=Math.max(g,C,h.depth+.25*p.xHeight),b=Math.max(b,p.sub2);var V=p.defaultRuleThickness,W=4*V;if(g-h.depth-(c.height-b)<W){b=W-(g-h.depth)+c.height;var G=.8*p.xHeight-(g-h.depth);G>0&&(g+=G,b-=G)}var J=[{type:"elem",elem:c,shift:b,marginRight:E,marginLeft:L},{type:"elem",elem:h,shift:-g,marginRight:E}];Y=y.makeVList({positionType:"individualShift",children:J},e)}else if(c){b=Math.max(b,p.sub1,c.height-.8*p.xHeight);var X=[{type:"elem",elem:c,marginLeft:L,marginRight:E}];Y=y.makeVList({positionType:"shift",positionData:b,children:X},e)}else if(h)g=Math.max(g,C,h.depth+.25*p.xHeight),Y=y.makeVList({positionType:"shift",positionData:-g,children:[{type:"elem",elem:h,marginRight:E}]},e);else throw new Error("supsub must have either sup or sub.");var B0=zt(o,"right")||"mord";return y.makeSpan([B0],[o,y.makeSpan(["msupsub"],[Y])],e)},mathmlBuilder(r,e){var t=!1,a,n;r.base&&r.base.type==="horizBrace"&&(n=!!r.sup,n===r.base.isOver&&(t=!0,a=r.base.isOver)),r.base&&(r.base.type==="op"||r.base.type==="operatorname")&&(r.base.parentIsSupSub=!0);var l=[$(r.base,e)];r.sub&&l.push($(r.sub,e)),r.sup&&l.push($(r.sup,e));var o;if(t)o=a?"mover":"munder";else if(r.sub)if(r.sup){var p=r.base;p&&p.type==="op"&&p.limits&&e.style===O.DISPLAY||p&&p.type==="operatorname"&&p.alwaysHandleSupSub&&(e.style===O.DISPLAY||p.limits)?o="munderover":o="msubsup"}else{var c=r.base;c&&c.type==="op"&&c.limits&&(e.style===O.DISPLAY||c.alwaysHandleSupSub)||c&&c.type==="operatorname"&&c.alwaysHandleSupSub&&(c.limits||e.style===O.DISPLAY)?o="munder":o="msub"}else{var h=r.base;h&&h.type==="op"&&h.limits&&(e.style===O.DISPLAY||h.alwaysHandleSupSub)||h&&h.type==="operatorname"&&h.alwaysHandleSupSub&&(h.limits||e.style===O.DISPLAY)?o="mover":o="msup"}return new S.MathNode(o,l)}});te({type:"atom",htmlBuilder(r,e){return y.mathsym(r.text,r.mode,e,["m"+r.family])},mathmlBuilder(r,e){var t=new S.MathNode("mo",[w0(r.text,r.mode)]);if(r.family==="bin"){var a=Ot(r,e);a==="bold-italic"&&t.setAttribute("mathvariant",a)}else r.family==="punct"?t.setAttribute("separator","true"):(r.family==="open"||r.family==="close")&&t.setAttribute("stretchy","false");return t}});var Xa={mi:"italic",mn:"normal",mtext:"normal"};te({type:"mathord",htmlBuilder(r,e){return y.makeOrd(r,e,"mathord")},mathmlBuilder(r,e){var t=new S.MathNode("mi",[w0(r.text,r.mode,e)]),a=Ot(r,e)||"italic";return a!==Xa[t.type]&&t.setAttribute("mathvariant",a),t}});te({type:"textord",htmlBuilder(r,e){return y.makeOrd(r,e,"textord")},mathmlBuilder(r,e){var t=w0(r.text,r.mode,e),a=Ot(r,e)||"normal",n;return r.mode==="text"?n=new S.MathNode("mtext",[t]):/[0-9]/.test(r.text)?n=new S.MathNode("mn",[t]):r.text==="\\prime"?n=new S.MathNode("mo",[t]):n=new S.MathNode("mi",[t]),a!==Xa[n.type]&&n.setAttribute("mathvariant",a),n}});var pt={"\\nobreak":"nobreak","\\allowbreak":"allowbreak"},vt={" ":{},"\\ ":{},"~":{className:"nobreak"},"\\space":{},"\\nobreakspace":{className:"nobreak"}};te({type:"spacing",htmlBuilder(r,e){if(vt.hasOwnProperty(r.text)){var t=vt[r.text].className||"";if(r.mode==="text"){var a=y.makeOrd(r,e,"textord");return a.classes.push(t),a}else return y.makeSpan(["mspace",t],[y.mathsym(r.text,r.mode,e)],e)}else{if(pt.hasOwnProperty(r.text))return y.makeSpan(["mspace",pt[r.text]],[],e);throw new A('Unknown type of space "'+r.text+'"')}},mathmlBuilder(r,e){var t;if(vt.hasOwnProperty(r.text))t=new S.MathNode("mtext",[new S.TextNode(" ")]);else{if(pt.hasOwnProperty(r.text))return new S.MathNode("mspace");throw new A('Unknown type of space "'+r.text+'"')}return t}});var Rr=()=>{var r=new S.MathNode("mtd",[]);return r.setAttribute("width","50%"),r};te({type:"tag",mathmlBuilder(r,e){var t=new S.MathNode("mtable",[new S.MathNode("mtr",[Rr(),new S.MathNode("mtd",[$0(r.body,e)]),Rr(),new S.MathNode("mtd",[$0(r.tag,e)])])]);return t.setAttribute("width","100%"),t}});var Or={"\\text":void 0,"\\textrm":"textrm","\\textsf":"textsf","\\texttt":"texttt","\\textnormal":"textrm"},Lr={"\\textbf":"textbf","\\textmd":"textmd"},Dl={"\\textit":"textit","\\textup":"textup"},Ir=(r,e)=>{var t=r.font;if(t){if(Or[t])return e.withTextFontFamily(Or[t]);if(Lr[t])return e.withTextFontWeight(Lr[t]);if(t==="\\emph")return e.fontShape==="textit"?e.withTextFontShape("textup"):e.withTextFontShape("textit")}else return e;return e.withTextFontShape(Dl[t])};D({type:"text",names:["\\text","\\textrm","\\textsf","\\texttt","\\textnormal","\\textbf","\\textmd","\\textit","\\textup","\\emph"],props:{numArgs:1,argTypes:["text"],allowedInArgument:!0,allowedInText:!0},handler(r,e){var{parser:t,funcName:a}=r,n=e[0];return{type:"text",mode:t.mode,body:t0(n),font:a}},htmlBuilder(r,e){var t=Ir(r,e),a=l0(r.body,t,!0);return y.makeSpan(["mord","text"],a,t)},mathmlBuilder(r,e){var t=Ir(r,e);return $0(r.body,t)}});D({type:"underline",names:["\\underline"],props:{numArgs:1,allowedInText:!0},handler(r,e){var{parser:t}=r;return{type:"underline",mode:t.mode,body:e[0]}},htmlBuilder(r,e){var t=U(r.body,e),a=y.makeLineSpan("underline-line",e),n=e.fontMetrics().defaultRuleThickness,l=y.makeVList({positionType:"top",positionData:t.height,children:[{type:"kern",size:n},{type:"elem",elem:a},{type:"kern",size:3*n},{type:"elem",elem:t}]},e);return y.makeSpan(["mord","underline"],[l],e)},mathmlBuilder(r,e){var t=new S.MathNode("mo",[new S.TextNode("‾")]);t.setAttribute("stretchy","true");var a=new S.MathNode("munder",[$(r.body,e),t]);return a.setAttribute("accentunder","true"),a}});D({type:"vcenter",names:["\\vcenter"],props:{numArgs:1,argTypes:["original"],allowedInText:!1},handler(r,e){var{parser:t}=r;return{type:"vcenter",mode:t.mode,body:e[0]}},htmlBuilder(r,e){var t=U(r.body,e),a=e.fontMetrics().axisHeight,n=.5*(t.height-a-(t.depth+a));return y.makeVList({positionType:"shift",positionData:n,children:[{type:"elem",elem:t}]},e)},mathmlBuilder(r,e){return new S.MathNode("mpadded",[$(r.body,e)],["vcenter"])}});D({type:"verb",names:["\\verb"],props:{numArgs:0,allowedInText:!0},handler(r,e,t){throw new A("\\verb ended by end of line instead of matching delimiter")},htmlBuilder(r,e){for(var t=Fr(r),a=[],n=e.havingStyle(e.style.text()),l=0;l<t.length;l++){var o=t[l];o==="~"&&(o="\\textasciitilde"),a.push(y.makeSymbol(o,"Typewriter-Regular",r.mode,n,["mord","texttt"]))}return y.makeSpan(["mord","text"].concat(n.sizingClasses(e)),y.tryCombineChars(a),n)},mathmlBuilder(r,e){var t=new S.TextNode(Fr(r)),a=new S.MathNode("mtext",[t]);return a.setAttribute("mathvariant","monospace"),a}});var Fr=r=>r.body.replace(/ /g,r.star?"␣":" "),Y0=ga,$a=`[ \r
	]`,Bl="\\\\[a-zA-Z@]+",Nl="\\\\[^\uD800-\uDFFF]",El="("+Bl+")"+$a+"*",ql=`\\\\(
|[ \r	]+
?)[ \r	]*`,Ct="[̀-ͯ]",Rl=new RegExp(Ct+"+$"),Ol="("+$a+"+)|"+(ql+"|")+"([!-\\[\\]-‧‪-퟿豈-￿]"+(Ct+"*")+"|[\uD800-\uDBFF][\uDC00-\uDFFF]"+(Ct+"*")+"|\\\\verb\\*([^]).*?\\4|\\\\verb([^*a-zA-Z]).*?\\5"+("|"+El)+("|"+Nl+")");class Pr{constructor(e,t){this.input=void 0,this.settings=void 0,this.tokenRegex=void 0,this.catcodes=void 0,this.input=e,this.settings=t,this.tokenRegex=new RegExp(Ol,"g"),this.catcodes={"%":14,"~":13}}setCatcode(e,t){this.catcodes[e]=t}lex(){var e=this.input,t=this.tokenRegex.lastIndex;if(t===e.length)return new x0("EOF",new p0(this,t,t));var a=this.tokenRegex.exec(e);if(a===null||a.index!==t)throw new A("Unexpected character: '"+e[t]+"'",new x0(e[t],new p0(this,t,t+1)));var n=a[6]||a[3]||(a[2]?"\\ ":" ");if(this.catcodes[n]===14){var l=e.indexOf(`
`,this.tokenRegex.lastIndex);return l===-1?(this.tokenRegex.lastIndex=e.length,this.settings.reportNonstrict("commentAtEnd","% comment has no terminating newline; LaTeX would fail because of commenting the end of math mode (e.g. $)")):this.tokenRegex.lastIndex=l+1,this.lex()}return new x0(n,new p0(this,t,this.tokenRegex.lastIndex))}}class Ll{constructor(e,t){e===void 0&&(e={}),t===void 0&&(t={}),this.current=void 0,this.builtins=void 0,this.undefStack=void 0,this.current=t,this.builtins=e,this.undefStack=[]}beginGroup(){this.undefStack.push({})}endGroup(){if(this.undefStack.length===0)throw new A("Unbalanced namespace destruction: attempt to pop global namespace; please report this as a bug");var e=this.undefStack.pop();for(var t in e)e.hasOwnProperty(t)&&(e[t]==null?delete this.current[t]:this.current[t]=e[t])}endGroups(){for(;this.undefStack.length>0;)this.endGroup()}has(e){return this.current.hasOwnProperty(e)||this.builtins.hasOwnProperty(e)}get(e){return this.current.hasOwnProperty(e)?this.current[e]:this.builtins[e]}set(e,t,a){if(a===void 0&&(a=!1),a){for(var n=0;n<this.undefStack.length;n++)delete this.undefStack[n][e];this.undefStack.length>0&&(this.undefStack[this.undefStack.length-1][e]=t)}else{var l=this.undefStack[this.undefStack.length-1];l&&!l.hasOwnProperty(e)&&(l[e]=this.current[e])}t==null?delete this.current[e]:this.current[e]=t}}var Il=La;m("\\noexpand",function(r){var e=r.popToken();return r.isExpandable(e.text)&&(e.noexpand=!0,e.treatAsRelax=!0),{tokens:[e],numArgs:0}});m("\\expandafter",function(r){var e=r.popToken();return r.expandOnce(!0),{tokens:[e],numArgs:0}});m("\\@firstoftwo",function(r){var e=r.consumeArgs(2);return{tokens:e[0],numArgs:0}});m("\\@secondoftwo",function(r){var e=r.consumeArgs(2);return{tokens:e[1],numArgs:0}});m("\\@ifnextchar",function(r){var e=r.consumeArgs(3);r.consumeSpaces();var t=r.future();return e[0].length===1&&e[0][0].text===t.text?{tokens:e[1],numArgs:0}:{tokens:e[2],numArgs:0}});m("\\@ifstar","\\@ifnextchar *{\\@firstoftwo{#1}}");m("\\TextOrMath",function(r){var e=r.consumeArgs(2);return r.mode==="text"?{tokens:e[0],numArgs:0}:{tokens:e[1],numArgs:0}});var Hr={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,a:10,A:10,b:11,B:11,c:12,C:12,d:13,D:13,e:14,E:14,f:15,F:15};m("\\char",function(r){var e=r.popToken(),t,a="";if(e.text==="'")t=8,e=r.popToken();else if(e.text==='"')t=16,e=r.popToken();else if(e.text==="`")if(e=r.popToken(),e.text[0]==="\\")a=e.text.charCodeAt(1);else{if(e.text==="EOF")throw new A("\\char` missing argument");a=e.text.charCodeAt(0)}else t=10;if(t){if(a=Hr[e.text],a==null||a>=t)throw new A("Invalid base-"+t+" digit "+e.text);for(var n;(n=Hr[r.future().text])!=null&&n<t;)a*=t,a+=n,r.popToken()}return"\\@char{"+a+"}"});var Wt=(r,e,t,a)=>{var n=r.consumeArg().tokens;if(n.length!==1)throw new A("\\newcommand's first argument must be a macro name");var l=n[0].text,o=r.isDefined(l);if(o&&!e)throw new A("\\newcommand{"+l+"} attempting to redefine "+(l+"; use \\renewcommand"));if(!o&&!t)throw new A("\\renewcommand{"+l+"} when command "+l+" does not yet exist; use \\newcommand");var h=0;if(n=r.consumeArg().tokens,n.length===1&&n[0].text==="["){for(var c="",p=r.expandNextToken();p.text!=="]"&&p.text!=="EOF";)c+=p.text,p=r.expandNextToken();if(!c.match(/^\s*[0-9]+\s*$/))throw new A("Invalid number of arguments: "+c);h=parseInt(c),n=r.consumeArg().tokens}return o&&a||r.macros.set(l,{tokens:n,numArgs:h}),""};m("\\newcommand",r=>Wt(r,!1,!0,!1));m("\\renewcommand",r=>Wt(r,!0,!1,!1));m("\\providecommand",r=>Wt(r,!0,!0,!0));m("\\message",r=>{var e=r.consumeArgs(1)[0];return console.log(e.reverse().map(t=>t.text).join("")),""});m("\\errmessage",r=>{var e=r.consumeArgs(1)[0];return console.error(e.reverse().map(t=>t.text).join("")),""});m("\\show",r=>{var e=r.popToken(),t=e.text;return console.log(e,r.macros.get(t),Y0[t],K.math[t],K.text[t]),""});m("\\bgroup","{");m("\\egroup","}");m("~","\\nobreakspace");m("\\lq","`");m("\\rq","'");m("\\aa","\\r a");m("\\AA","\\r A");m("\\textcopyright","\\html@mathml{\\textcircled{c}}{\\char`©}");m("\\copyright","\\TextOrMath{\\textcopyright}{\\text{\\textcopyright}}");m("\\textregistered","\\html@mathml{\\textcircled{\\scriptsize R}}{\\char`®}");m("ℬ","\\mathscr{B}");m("ℰ","\\mathscr{E}");m("ℱ","\\mathscr{F}");m("ℋ","\\mathscr{H}");m("ℐ","\\mathscr{I}");m("ℒ","\\mathscr{L}");m("ℳ","\\mathscr{M}");m("ℛ","\\mathscr{R}");m("ℭ","\\mathfrak{C}");m("ℌ","\\mathfrak{H}");m("ℨ","\\mathfrak{Z}");m("\\Bbbk","\\Bbb{k}");m("·","\\cdotp");m("\\llap","\\mathllap{\\textrm{#1}}");m("\\rlap","\\mathrlap{\\textrm{#1}}");m("\\clap","\\mathclap{\\textrm{#1}}");m("\\mathstrut","\\vphantom{(}");m("\\underbar","\\underline{\\text{#1}}");m("\\not",'\\html@mathml{\\mathrel{\\mathrlap\\@not}}{\\char"338}');m("\\neq","\\html@mathml{\\mathrel{\\not=}}{\\mathrel{\\char`≠}}");m("\\ne","\\neq");m("≠","\\neq");m("\\notin","\\html@mathml{\\mathrel{{\\in}\\mathllap{/\\mskip1mu}}}{\\mathrel{\\char`∉}}");m("∉","\\notin");m("≘","\\html@mathml{\\mathrel{=\\kern{-1em}\\raisebox{0.4em}{$\\scriptsize\\frown$}}}{\\mathrel{\\char`≘}}");m("≙","\\html@mathml{\\stackrel{\\tiny\\wedge}{=}}{\\mathrel{\\char`≘}}");m("≚","\\html@mathml{\\stackrel{\\tiny\\vee}{=}}{\\mathrel{\\char`≚}}");m("≛","\\html@mathml{\\stackrel{\\scriptsize\\star}{=}}{\\mathrel{\\char`≛}}");m("≝","\\html@mathml{\\stackrel{\\tiny\\mathrm{def}}{=}}{\\mathrel{\\char`≝}}");m("≞","\\html@mathml{\\stackrel{\\tiny\\mathrm{m}}{=}}{\\mathrel{\\char`≞}}");m("≟","\\html@mathml{\\stackrel{\\tiny?}{=}}{\\mathrel{\\char`≟}}");m("⟂","\\perp");m("‼","\\mathclose{!\\mkern-0.8mu!}");m("∌","\\notni");m("⌜","\\ulcorner");m("⌝","\\urcorner");m("⌞","\\llcorner");m("⌟","\\lrcorner");m("©","\\copyright");m("®","\\textregistered");m("️","\\textregistered");m("\\ulcorner",'\\html@mathml{\\@ulcorner}{\\mathop{\\char"231c}}');m("\\urcorner",'\\html@mathml{\\@urcorner}{\\mathop{\\char"231d}}');m("\\llcorner",'\\html@mathml{\\@llcorner}{\\mathop{\\char"231e}}');m("\\lrcorner",'\\html@mathml{\\@lrcorner}{\\mathop{\\char"231f}}');m("\\vdots","{\\varvdots\\rule{0pt}{15pt}}");m("⋮","\\vdots");m("\\varGamma","\\mathit{\\Gamma}");m("\\varDelta","\\mathit{\\Delta}");m("\\varTheta","\\mathit{\\Theta}");m("\\varLambda","\\mathit{\\Lambda}");m("\\varXi","\\mathit{\\Xi}");m("\\varPi","\\mathit{\\Pi}");m("\\varSigma","\\mathit{\\Sigma}");m("\\varUpsilon","\\mathit{\\Upsilon}");m("\\varPhi","\\mathit{\\Phi}");m("\\varPsi","\\mathit{\\Psi}");m("\\varOmega","\\mathit{\\Omega}");m("\\substack","\\begin{subarray}{c}#1\\end{subarray}");m("\\colon","\\nobreak\\mskip2mu\\mathpunct{}\\mathchoice{\\mkern-3mu}{\\mkern-3mu}{}{}{:}\\mskip6mu\\relax");m("\\boxed","\\fbox{$\\displaystyle{#1}$}");m("\\iff","\\DOTSB\\;\\Longleftrightarrow\\;");m("\\implies","\\DOTSB\\;\\Longrightarrow\\;");m("\\impliedby","\\DOTSB\\;\\Longleftarrow\\;");m("\\dddot","{\\overset{\\raisebox{-0.1ex}{\\normalsize ...}}{#1}}");m("\\ddddot","{\\overset{\\raisebox{-0.1ex}{\\normalsize ....}}{#1}}");var Vr={",":"\\dotsc","\\not":"\\dotsb","+":"\\dotsb","=":"\\dotsb","<":"\\dotsb",">":"\\dotsb","-":"\\dotsb","*":"\\dotsb",":":"\\dotsb","\\DOTSB":"\\dotsb","\\coprod":"\\dotsb","\\bigvee":"\\dotsb","\\bigwedge":"\\dotsb","\\biguplus":"\\dotsb","\\bigcap":"\\dotsb","\\bigcup":"\\dotsb","\\prod":"\\dotsb","\\sum":"\\dotsb","\\bigotimes":"\\dotsb","\\bigoplus":"\\dotsb","\\bigodot":"\\dotsb","\\bigsqcup":"\\dotsb","\\And":"\\dotsb","\\longrightarrow":"\\dotsb","\\Longrightarrow":"\\dotsb","\\longleftarrow":"\\dotsb","\\Longleftarrow":"\\dotsb","\\longleftrightarrow":"\\dotsb","\\Longleftrightarrow":"\\dotsb","\\mapsto":"\\dotsb","\\longmapsto":"\\dotsb","\\hookrightarrow":"\\dotsb","\\doteq":"\\dotsb","\\mathbin":"\\dotsb","\\mathrel":"\\dotsb","\\relbar":"\\dotsb","\\Relbar":"\\dotsb","\\xrightarrow":"\\dotsb","\\xleftarrow":"\\dotsb","\\DOTSI":"\\dotsi","\\int":"\\dotsi","\\oint":"\\dotsi","\\iint":"\\dotsi","\\iiint":"\\dotsi","\\iiiint":"\\dotsi","\\idotsint":"\\dotsi","\\DOTSX":"\\dotsx"};m("\\dots",function(r){var e="\\dotso",t=r.expandAfterFuture().text;return t in Vr?e=Vr[t]:(t.slice(0,4)==="\\not"||t in K.math&&q.contains(["bin","rel"],K.math[t].group))&&(e="\\dotsb"),e});var Xt={")":!0,"]":!0,"\\rbrack":!0,"\\}":!0,"\\rbrace":!0,"\\rangle":!0,"\\rceil":!0,"\\rfloor":!0,"\\rgroup":!0,"\\rmoustache":!0,"\\right":!0,"\\bigr":!0,"\\biggr":!0,"\\Bigr":!0,"\\Biggr":!0,$:!0,";":!0,".":!0,",":!0};m("\\dotso",function(r){var e=r.future().text;return e in Xt?"\\ldots\\,":"\\ldots"});m("\\dotsc",function(r){var e=r.future().text;return e in Xt&&e!==","?"\\ldots\\,":"\\ldots"});m("\\cdots",function(r){var e=r.future().text;return e in Xt?"\\@cdots\\,":"\\@cdots"});m("\\dotsb","\\cdots");m("\\dotsm","\\cdots");m("\\dotsi","\\!\\cdots");m("\\dotsx","\\ldots\\,");m("\\DOTSI","\\relax");m("\\DOTSB","\\relax");m("\\DOTSX","\\relax");m("\\tmspace","\\TextOrMath{\\kern#1#3}{\\mskip#1#2}\\relax");m("\\,","\\tmspace+{3mu}{.1667em}");m("\\thinspace","\\,");m("\\>","\\mskip{4mu}");m("\\:","\\tmspace+{4mu}{.2222em}");m("\\medspace","\\:");m("\\;","\\tmspace+{5mu}{.2777em}");m("\\thickspace","\\;");m("\\!","\\tmspace-{3mu}{.1667em}");m("\\negthinspace","\\!");m("\\negmedspace","\\tmspace-{4mu}{.2222em}");m("\\negthickspace","\\tmspace-{5mu}{.277em}");m("\\enspace","\\kern.5em ");m("\\enskip","\\hskip.5em\\relax");m("\\quad","\\hskip1em\\relax");m("\\qquad","\\hskip2em\\relax");m("\\tag","\\@ifstar\\tag@literal\\tag@paren");m("\\tag@paren","\\tag@literal{({#1})}");m("\\tag@literal",r=>{if(r.macros.get("\\df@tag"))throw new A("Multiple \\tag");return"\\gdef\\df@tag{\\text{#1}}"});m("\\bmod","\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}\\mathbin{\\rm mod}\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}");m("\\pod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern8mu}{\\mkern8mu}{\\mkern8mu}(#1)");m("\\pmod","\\pod{{\\rm mod}\\mkern6mu#1}");m("\\mod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern12mu}{\\mkern12mu}{\\mkern12mu}{\\rm mod}\\,\\,#1");m("\\newline","\\\\\\relax");m("\\TeX","\\textrm{\\html@mathml{T\\kern-.1667em\\raisebox{-.5ex}{E}\\kern-.125emX}{TeX}}");var ja=T(q0["Main-Regular"][84][1]-.7*q0["Main-Regular"][65][1]);m("\\LaTeX","\\textrm{\\html@mathml{"+("L\\kern-.36em\\raisebox{"+ja+"}{\\scriptstyle A}")+"\\kern-.15em\\TeX}{LaTeX}}");m("\\KaTeX","\\textrm{\\html@mathml{"+("K\\kern-.17em\\raisebox{"+ja+"}{\\scriptstyle A}")+"\\kern-.15em\\TeX}{KaTeX}}");m("\\hspace","\\@ifstar\\@hspacer\\@hspace");m("\\@hspace","\\hskip #1\\relax");m("\\@hspacer","\\rule{0pt}{0pt}\\hskip #1\\relax");m("\\ordinarycolon",":");m("\\vcentcolon","\\mathrel{\\mathop\\ordinarycolon}");m("\\dblcolon",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-.9mu}\\vcentcolon}}{\\mathop{\\char"2237}}');m("\\coloneqq",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2254}}');m("\\Coloneqq",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2237\\char"3d}}');m("\\coloneq",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"3a\\char"2212}}');m("\\Coloneq",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"2237\\char"2212}}');m("\\eqqcolon",'\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2255}}');m("\\Eqqcolon",'\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"3d\\char"2237}}');m("\\eqcolon",'\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2239}}');m("\\Eqcolon",'\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"2212\\char"2237}}');m("\\colonapprox",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"3a\\char"2248}}');m("\\Colonapprox",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"2237\\char"2248}}');m("\\colonsim",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"3a\\char"223c}}');m("\\Colonsim",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"2237\\char"223c}}');m("∷","\\dblcolon");m("∹","\\eqcolon");m("≔","\\coloneqq");m("≕","\\eqqcolon");m("⩴","\\Coloneqq");m("\\ratio","\\vcentcolon");m("\\coloncolon","\\dblcolon");m("\\colonequals","\\coloneqq");m("\\coloncolonequals","\\Coloneqq");m("\\equalscolon","\\eqqcolon");m("\\equalscoloncolon","\\Eqqcolon");m("\\colonminus","\\coloneq");m("\\coloncolonminus","\\Coloneq");m("\\minuscolon","\\eqcolon");m("\\minuscoloncolon","\\Eqcolon");m("\\coloncolonapprox","\\Colonapprox");m("\\coloncolonsim","\\Colonsim");m("\\simcolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\vcentcolon}");m("\\simcoloncolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\dblcolon}");m("\\approxcolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\vcentcolon}");m("\\approxcoloncolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\dblcolon}");m("\\notni","\\html@mathml{\\not\\ni}{\\mathrel{\\char`∌}}");m("\\limsup","\\DOTSB\\operatorname*{lim\\,sup}");m("\\liminf","\\DOTSB\\operatorname*{lim\\,inf}");m("\\injlim","\\DOTSB\\operatorname*{inj\\,lim}");m("\\projlim","\\DOTSB\\operatorname*{proj\\,lim}");m("\\varlimsup","\\DOTSB\\operatorname*{\\overline{lim}}");m("\\varliminf","\\DOTSB\\operatorname*{\\underline{lim}}");m("\\varinjlim","\\DOTSB\\operatorname*{\\underrightarrow{lim}}");m("\\varprojlim","\\DOTSB\\operatorname*{\\underleftarrow{lim}}");m("\\gvertneqq","\\html@mathml{\\@gvertneqq}{≩}");m("\\lvertneqq","\\html@mathml{\\@lvertneqq}{≨}");m("\\ngeqq","\\html@mathml{\\@ngeqq}{≱}");m("\\ngeqslant","\\html@mathml{\\@ngeqslant}{≱}");m("\\nleqq","\\html@mathml{\\@nleqq}{≰}");m("\\nleqslant","\\html@mathml{\\@nleqslant}{≰}");m("\\nshortmid","\\html@mathml{\\@nshortmid}{∤}");m("\\nshortparallel","\\html@mathml{\\@nshortparallel}{∦}");m("\\nsubseteqq","\\html@mathml{\\@nsubseteqq}{⊈}");m("\\nsupseteqq","\\html@mathml{\\@nsupseteqq}{⊉}");m("\\varsubsetneq","\\html@mathml{\\@varsubsetneq}{⊊}");m("\\varsubsetneqq","\\html@mathml{\\@varsubsetneqq}{⫋}");m("\\varsupsetneq","\\html@mathml{\\@varsupsetneq}{⊋}");m("\\varsupsetneqq","\\html@mathml{\\@varsupsetneqq}{⫌}");m("\\imath","\\html@mathml{\\@imath}{ı}");m("\\jmath","\\html@mathml{\\@jmath}{ȷ}");m("\\llbracket","\\html@mathml{\\mathopen{[\\mkern-3.2mu[}}{\\mathopen{\\char`⟦}}");m("\\rrbracket","\\html@mathml{\\mathclose{]\\mkern-3.2mu]}}{\\mathclose{\\char`⟧}}");m("⟦","\\llbracket");m("⟧","\\rrbracket");m("\\lBrace","\\html@mathml{\\mathopen{\\{\\mkern-3.2mu[}}{\\mathopen{\\char`⦃}}");m("\\rBrace","\\html@mathml{\\mathclose{]\\mkern-3.2mu\\}}}{\\mathclose{\\char`⦄}}");m("⦃","\\lBrace");m("⦄","\\rBrace");m("\\minuso","\\mathbin{\\html@mathml{{\\mathrlap{\\mathchoice{\\kern{0.145em}}{\\kern{0.145em}}{\\kern{0.1015em}}{\\kern{0.0725em}}\\circ}{-}}}{\\char`⦵}}");m("⦵","\\minuso");m("\\darr","\\downarrow");m("\\dArr","\\Downarrow");m("\\Darr","\\Downarrow");m("\\lang","\\langle");m("\\rang","\\rangle");m("\\uarr","\\uparrow");m("\\uArr","\\Uparrow");m("\\Uarr","\\Uparrow");m("\\N","\\mathbb{N}");m("\\R","\\mathbb{R}");m("\\Z","\\mathbb{Z}");m("\\alef","\\aleph");m("\\alefsym","\\aleph");m("\\Alpha","\\mathrm{A}");m("\\Beta","\\mathrm{B}");m("\\bull","\\bullet");m("\\Chi","\\mathrm{X}");m("\\clubs","\\clubsuit");m("\\cnums","\\mathbb{C}");m("\\Complex","\\mathbb{C}");m("\\Dagger","\\ddagger");m("\\diamonds","\\diamondsuit");m("\\empty","\\emptyset");m("\\Epsilon","\\mathrm{E}");m("\\Eta","\\mathrm{H}");m("\\exist","\\exists");m("\\harr","\\leftrightarrow");m("\\hArr","\\Leftrightarrow");m("\\Harr","\\Leftrightarrow");m("\\hearts","\\heartsuit");m("\\image","\\Im");m("\\infin","\\infty");m("\\Iota","\\mathrm{I}");m("\\isin","\\in");m("\\Kappa","\\mathrm{K}");m("\\larr","\\leftarrow");m("\\lArr","\\Leftarrow");m("\\Larr","\\Leftarrow");m("\\lrarr","\\leftrightarrow");m("\\lrArr","\\Leftrightarrow");m("\\Lrarr","\\Leftrightarrow");m("\\Mu","\\mathrm{M}");m("\\natnums","\\mathbb{N}");m("\\Nu","\\mathrm{N}");m("\\Omicron","\\mathrm{O}");m("\\plusmn","\\pm");m("\\rarr","\\rightarrow");m("\\rArr","\\Rightarrow");m("\\Rarr","\\Rightarrow");m("\\real","\\Re");m("\\reals","\\mathbb{R}");m("\\Reals","\\mathbb{R}");m("\\Rho","\\mathrm{P}");m("\\sdot","\\cdot");m("\\sect","\\S");m("\\spades","\\spadesuit");m("\\sub","\\subset");m("\\sube","\\subseteq");m("\\supe","\\supseteq");m("\\Tau","\\mathrm{T}");m("\\thetasym","\\vartheta");m("\\weierp","\\wp");m("\\Zeta","\\mathrm{Z}");m("\\argmin","\\DOTSB\\operatorname*{arg\\,min}");m("\\argmax","\\DOTSB\\operatorname*{arg\\,max}");m("\\plim","\\DOTSB\\mathop{\\operatorname{plim}}\\limits");m("\\bra","\\mathinner{\\langle{#1}|}");m("\\ket","\\mathinner{|{#1}\\rangle}");m("\\braket","\\mathinner{\\langle{#1}\\rangle}");m("\\Bra","\\left\\langle#1\\right|");m("\\Ket","\\left|#1\\right\\rangle");var Ka=r=>e=>{var t=e.consumeArg().tokens,a=e.consumeArg().tokens,n=e.consumeArg().tokens,l=e.consumeArg().tokens,o=e.macros.get("|"),h=e.macros.get("\\|");e.macros.beginGroup();var c=b=>x=>{r&&(x.macros.set("|",o),n.length&&x.macros.set("\\|",h));var w=b;if(!b&&n.length){var M=x.future();M.text==="|"&&(x.popToken(),w=!0)}return{tokens:w?n:a,numArgs:0}};e.macros.set("|",c(!1)),n.length&&e.macros.set("\\|",c(!0));var p=e.consumeArg().tokens,g=e.expandTokens([...l,...p,...t]);return e.macros.endGroup(),{tokens:g.reverse(),numArgs:0}};m("\\bra@ket",Ka(!1));m("\\bra@set",Ka(!0));m("\\Braket","\\bra@ket{\\left\\langle}{\\,\\middle\\vert\\,}{\\,\\middle\\vert\\,}{\\right\\rangle}");m("\\Set","\\bra@set{\\left\\{\\:}{\\;\\middle\\vert\\;}{\\;\\middle\\Vert\\;}{\\:\\right\\}}");m("\\set","\\bra@set{\\{\\,}{\\mid}{}{\\,\\}}");m("\\angln","{\\angl n}");m("\\blue","\\textcolor{##6495ed}{#1}");m("\\orange","\\textcolor{##ffa500}{#1}");m("\\pink","\\textcolor{##ff00af}{#1}");m("\\red","\\textcolor{##df0030}{#1}");m("\\green","\\textcolor{##28ae7b}{#1}");m("\\gray","\\textcolor{gray}{#1}");m("\\purple","\\textcolor{##9d38bd}{#1}");m("\\blueA","\\textcolor{##ccfaff}{#1}");m("\\blueB","\\textcolor{##80f6ff}{#1}");m("\\blueC","\\textcolor{##63d9ea}{#1}");m("\\blueD","\\textcolor{##11accd}{#1}");m("\\blueE","\\textcolor{##0c7f99}{#1}");m("\\tealA","\\textcolor{##94fff5}{#1}");m("\\tealB","\\textcolor{##26edd5}{#1}");m("\\tealC","\\textcolor{##01d1c1}{#1}");m("\\tealD","\\textcolor{##01a995}{#1}");m("\\tealE","\\textcolor{##208170}{#1}");m("\\greenA","\\textcolor{##b6ffb0}{#1}");m("\\greenB","\\textcolor{##8af281}{#1}");m("\\greenC","\\textcolor{##74cf70}{#1}");m("\\greenD","\\textcolor{##1fab54}{#1}");m("\\greenE","\\textcolor{##0d923f}{#1}");m("\\goldA","\\textcolor{##ffd0a9}{#1}");m("\\goldB","\\textcolor{##ffbb71}{#1}");m("\\goldC","\\textcolor{##ff9c39}{#1}");m("\\goldD","\\textcolor{##e07d10}{#1}");m("\\goldE","\\textcolor{##a75a05}{#1}");m("\\redA","\\textcolor{##fca9a9}{#1}");m("\\redB","\\textcolor{##ff8482}{#1}");m("\\redC","\\textcolor{##f9685d}{#1}");m("\\redD","\\textcolor{##e84d39}{#1}");m("\\redE","\\textcolor{##bc2612}{#1}");m("\\maroonA","\\textcolor{##ffbde0}{#1}");m("\\maroonB","\\textcolor{##ff92c6}{#1}");m("\\maroonC","\\textcolor{##ed5fa6}{#1}");m("\\maroonD","\\textcolor{##ca337c}{#1}");m("\\maroonE","\\textcolor{##9e034e}{#1}");m("\\purpleA","\\textcolor{##ddd7ff}{#1}");m("\\purpleB","\\textcolor{##c6b9fc}{#1}");m("\\purpleC","\\textcolor{##aa87ff}{#1}");m("\\purpleD","\\textcolor{##7854ab}{#1}");m("\\purpleE","\\textcolor{##543b78}{#1}");m("\\mintA","\\textcolor{##f5f9e8}{#1}");m("\\mintB","\\textcolor{##edf2df}{#1}");m("\\mintC","\\textcolor{##e0e5cc}{#1}");m("\\grayA","\\textcolor{##f6f7f7}{#1}");m("\\grayB","\\textcolor{##f0f1f2}{#1}");m("\\grayC","\\textcolor{##e3e5e6}{#1}");m("\\grayD","\\textcolor{##d6d8da}{#1}");m("\\grayE","\\textcolor{##babec2}{#1}");m("\\grayF","\\textcolor{##888d93}{#1}");m("\\grayG","\\textcolor{##626569}{#1}");m("\\grayH","\\textcolor{##3b3e40}{#1}");m("\\grayI","\\textcolor{##21242c}{#1}");m("\\kaBlue","\\textcolor{##314453}{#1}");m("\\kaGreen","\\textcolor{##71B307}{#1}");var Za={"^":!0,_:!0,"\\limits":!0,"\\nolimits":!0};class Fl{constructor(e,t,a){this.settings=void 0,this.expansionCount=void 0,this.lexer=void 0,this.macros=void 0,this.stack=void 0,this.mode=void 0,this.settings=t,this.expansionCount=0,this.feed(e),this.macros=new Ll(Il,t.macros),this.mode=a,this.stack=[]}feed(e){this.lexer=new Pr(e,this.settings)}switchMode(e){this.mode=e}beginGroup(){this.macros.beginGroup()}endGroup(){this.macros.endGroup()}endGroups(){this.macros.endGroups()}future(){return this.stack.length===0&&this.pushToken(this.lexer.lex()),this.stack[this.stack.length-1]}popToken(){return this.future(),this.stack.pop()}pushToken(e){this.stack.push(e)}pushTokens(e){this.stack.push(...e)}scanArgument(e){var t,a,n;if(e){if(this.consumeSpaces(),this.future().text!=="[")return null;t=this.popToken(),{tokens:n,end:a}=this.consumeArg(["]"])}else({tokens:n,start:t,end:a}=this.consumeArg());return this.pushToken(new x0("EOF",a.loc)),this.pushTokens(n),t.range(a,"")}consumeSpaces(){for(;;){var e=this.future();if(e.text===" ")this.stack.pop();else break}}consumeArg(e){var t=[],a=e&&e.length>0;a||this.consumeSpaces();var n=this.future(),l,o=0,h=0;do{if(l=this.popToken(),t.push(l),l.text==="{")++o;else if(l.text==="}"){if(--o,o===-1)throw new A("Extra }",l)}else if(l.text==="EOF")throw new A("Unexpected end of input in a macro argument, expected '"+(e&&a?e[h]:"}")+"'",l);if(e&&a)if((o===0||o===1&&e[h]==="{")&&l.text===e[h]){if(++h,h===e.length){t.splice(-h,h);break}}else h=0}while(o!==0||a);return n.text==="{"&&t[t.length-1].text==="}"&&(t.pop(),t.shift()),t.reverse(),{tokens:t,start:n,end:l}}consumeArgs(e,t){if(t){if(t.length!==e+1)throw new A("The length of delimiters doesn't match the number of args!");for(var a=t[0],n=0;n<a.length;n++){var l=this.popToken();if(a[n]!==l.text)throw new A("Use of the macro doesn't match its definition",l)}}for(var o=[],h=0;h<e;h++)o.push(this.consumeArg(t&&t[h+1]).tokens);return o}countExpansion(e){if(this.expansionCount+=e,this.expansionCount>this.settings.maxExpand)throw new A("Too many expansions: infinite loop or need to increase maxExpand setting")}expandOnce(e){var t=this.popToken(),a=t.text,n=t.noexpand?null:this._getExpansion(a);if(n==null||e&&n.unexpandable){if(e&&n==null&&a[0]==="\\"&&!this.isDefined(a))throw new A("Undefined control sequence: "+a);return this.pushToken(t),!1}this.countExpansion(1);var l=n.tokens,o=this.consumeArgs(n.numArgs,n.delimiters);if(n.numArgs){l=l.slice();for(var h=l.length-1;h>=0;--h){var c=l[h];if(c.text==="#"){if(h===0)throw new A("Incomplete placeholder at end of macro body",c);if(c=l[--h],c.text==="#")l.splice(h+1,1);else if(/^[1-9]$/.test(c.text))l.splice(h,2,...o[+c.text-1]);else throw new A("Not a valid argument number",c)}}}return this.pushTokens(l),l.length}expandAfterFuture(){return this.expandOnce(),this.future()}expandNextToken(){for(;;)if(this.expandOnce()===!1){var e=this.stack.pop();return e.treatAsRelax&&(e.text="\\relax"),e}throw new Error}expandMacro(e){return this.macros.has(e)?this.expandTokens([new x0(e)]):void 0}expandTokens(e){var t=[],a=this.stack.length;for(this.pushTokens(e);this.stack.length>a;)if(this.expandOnce(!0)===!1){var n=this.stack.pop();n.treatAsRelax&&(n.noexpand=!1,n.treatAsRelax=!1),t.push(n)}return this.countExpansion(t.length),t}expandMacroAsText(e){var t=this.expandMacro(e);return t&&t.map(a=>a.text).join("")}_getExpansion(e){var t=this.macros.get(e);if(t==null)return t;if(e.length===1){var a=this.lexer.catcodes[e];if(a!=null&&a!==13)return}var n=typeof t=="function"?t(this):t;if(typeof n=="string"){var l=0;if(n.indexOf("#")!==-1)for(var o=n.replace(/##/g,"");o.indexOf("#"+(l+1))!==-1;)++l;for(var h=new Pr(n,this.settings),c=[],p=h.lex();p.text!=="EOF";)c.push(p),p=h.lex();c.reverse();var g={tokens:c,numArgs:l};return g}return n}isDefined(e){return this.macros.has(e)||Y0.hasOwnProperty(e)||K.math.hasOwnProperty(e)||K.text.hasOwnProperty(e)||Za.hasOwnProperty(e)}isExpandable(e){var t=this.macros.get(e);return t!=null?typeof t=="string"||typeof t=="function"||!t.unexpandable:Y0.hasOwnProperty(e)&&!Y0[e].primitive}}var Ur=/^[₊₋₌₍₎₀₁₂₃₄₅₆₇₈₉ₐₑₕᵢⱼₖₗₘₙₒₚᵣₛₜᵤᵥₓᵦᵧᵨᵩᵪ]/,Oe=Object.freeze({"₊":"+","₋":"-","₌":"=","₍":"(","₎":")","₀":"0","₁":"1","₂":"2","₃":"3","₄":"4","₅":"5","₆":"6","₇":"7","₈":"8","₉":"9","ₐ":"a","ₑ":"e","ₕ":"h","ᵢ":"i","ⱼ":"j","ₖ":"k","ₗ":"l","ₘ":"m","ₙ":"n","ₒ":"o","ₚ":"p","ᵣ":"r","ₛ":"s","ₜ":"t","ᵤ":"u","ᵥ":"v","ₓ":"x","ᵦ":"β","ᵧ":"γ","ᵨ":"ρ","ᵩ":"ϕ","ᵪ":"χ","⁺":"+","⁻":"-","⁼":"=","⁽":"(","⁾":")","⁰":"0","¹":"1","²":"2","³":"3","⁴":"4","⁵":"5","⁶":"6","⁷":"7","⁸":"8","⁹":"9","ᴬ":"A","ᴮ":"B","ᴰ":"D","ᴱ":"E","ᴳ":"G","ᴴ":"H","ᴵ":"I","ᴶ":"J","ᴷ":"K","ᴸ":"L","ᴹ":"M","ᴺ":"N","ᴼ":"O","ᴾ":"P","ᴿ":"R","ᵀ":"T","ᵁ":"U","ⱽ":"V","ᵂ":"W","ᵃ":"a","ᵇ":"b","ᶜ":"c","ᵈ":"d","ᵉ":"e","ᶠ":"f","ᵍ":"g",ʰ:"h","ⁱ":"i",ʲ:"j","ᵏ":"k",ˡ:"l","ᵐ":"m",ⁿ:"n","ᵒ":"o","ᵖ":"p",ʳ:"r",ˢ:"s","ᵗ":"t","ᵘ":"u","ᵛ":"v",ʷ:"w",ˣ:"x",ʸ:"y","ᶻ":"z","ᵝ":"β","ᵞ":"γ","ᵟ":"δ","ᵠ":"ϕ","ᵡ":"χ","ᶿ":"θ"}),gt={"́":{text:"\\'",math:"\\acute"},"̀":{text:"\\`",math:"\\grave"},"̈":{text:'\\"',math:"\\ddot"},"̃":{text:"\\~",math:"\\tilde"},"̄":{text:"\\=",math:"\\bar"},"̆":{text:"\\u",math:"\\breve"},"̌":{text:"\\v",math:"\\check"},"̂":{text:"\\^",math:"\\hat"},"̇":{text:"\\.",math:"\\dot"},"̊":{text:"\\r",math:"\\mathring"},"̋":{text:"\\H"},"̧":{text:"\\c"}},Gr={á:"á",à:"à",ä:"ä",ǟ:"ǟ",ã:"ã",ā:"ā",ă:"ă",ắ:"ắ",ằ:"ằ",ẵ:"ẵ",ǎ:"ǎ",â:"â",ấ:"ấ",ầ:"ầ",ẫ:"ẫ",ȧ:"ȧ",ǡ:"ǡ",å:"å",ǻ:"ǻ",ḃ:"ḃ",ć:"ć",ḉ:"ḉ",č:"č",ĉ:"ĉ",ċ:"ċ",ç:"ç",ď:"ď",ḋ:"ḋ",ḑ:"ḑ",é:"é",è:"è",ë:"ë",ẽ:"ẽ",ē:"ē",ḗ:"ḗ",ḕ:"ḕ",ĕ:"ĕ",ḝ:"ḝ",ě:"ě",ê:"ê",ế:"ế",ề:"ề",ễ:"ễ",ė:"ė",ȩ:"ȩ",ḟ:"ḟ",ǵ:"ǵ",ḡ:"ḡ",ğ:"ğ",ǧ:"ǧ",ĝ:"ĝ",ġ:"ġ",ģ:"ģ",ḧ:"ḧ",ȟ:"ȟ",ĥ:"ĥ",ḣ:"ḣ",ḩ:"ḩ",í:"í",ì:"ì",ï:"ï",ḯ:"ḯ",ĩ:"ĩ",ī:"ī",ĭ:"ĭ",ǐ:"ǐ",î:"î",ǰ:"ǰ",ĵ:"ĵ",ḱ:"ḱ",ǩ:"ǩ",ķ:"ķ",ĺ:"ĺ",ľ:"ľ",ļ:"ļ",ḿ:"ḿ",ṁ:"ṁ",ń:"ń",ǹ:"ǹ",ñ:"ñ",ň:"ň",ṅ:"ṅ",ņ:"ņ",ó:"ó",ò:"ò",ö:"ö",ȫ:"ȫ",õ:"õ",ṍ:"ṍ",ṏ:"ṏ",ȭ:"ȭ",ō:"ō",ṓ:"ṓ",ṑ:"ṑ",ŏ:"ŏ",ǒ:"ǒ",ô:"ô",ố:"ố",ồ:"ồ",ỗ:"ỗ",ȯ:"ȯ",ȱ:"ȱ",ő:"ő",ṕ:"ṕ",ṗ:"ṗ",ŕ:"ŕ",ř:"ř",ṙ:"ṙ",ŗ:"ŗ",ś:"ś",ṥ:"ṥ",š:"š",ṧ:"ṧ",ŝ:"ŝ",ṡ:"ṡ",ş:"ş",ẗ:"ẗ",ť:"ť",ṫ:"ṫ",ţ:"ţ",ú:"ú",ù:"ù",ü:"ü",ǘ:"ǘ",ǜ:"ǜ",ǖ:"ǖ",ǚ:"ǚ",ũ:"ũ",ṹ:"ṹ",ū:"ū",ṻ:"ṻ",ŭ:"ŭ",ǔ:"ǔ",û:"û",ů:"ů",ű:"ű",ṽ:"ṽ",ẃ:"ẃ",ẁ:"ẁ",ẅ:"ẅ",ŵ:"ŵ",ẇ:"ẇ",ẘ:"ẘ",ẍ:"ẍ",ẋ:"ẋ",ý:"ý",ỳ:"ỳ",ÿ:"ÿ",ỹ:"ỹ",ȳ:"ȳ",ŷ:"ŷ",ẏ:"ẏ",ẙ:"ẙ",ź:"ź",ž:"ž",ẑ:"ẑ",ż:"ż",Á:"Á",À:"À",Ä:"Ä",Ǟ:"Ǟ",Ã:"Ã",Ā:"Ā",Ă:"Ă",Ắ:"Ắ",Ằ:"Ằ",Ẵ:"Ẵ",Ǎ:"Ǎ",Â:"Â",Ấ:"Ấ",Ầ:"Ầ",Ẫ:"Ẫ",Ȧ:"Ȧ",Ǡ:"Ǡ",Å:"Å",Ǻ:"Ǻ",Ḃ:"Ḃ",Ć:"Ć",Ḉ:"Ḉ",Č:"Č",Ĉ:"Ĉ",Ċ:"Ċ",Ç:"Ç",Ď:"Ď",Ḋ:"Ḋ",Ḑ:"Ḑ",É:"É",È:"È",Ë:"Ë",Ẽ:"Ẽ",Ē:"Ē",Ḗ:"Ḗ",Ḕ:"Ḕ",Ĕ:"Ĕ",Ḝ:"Ḝ",Ě:"Ě",Ê:"Ê",Ế:"Ế",Ề:"Ề",Ễ:"Ễ",Ė:"Ė",Ȩ:"Ȩ",Ḟ:"Ḟ",Ǵ:"Ǵ",Ḡ:"Ḡ",Ğ:"Ğ",Ǧ:"Ǧ",Ĝ:"Ĝ",Ġ:"Ġ",Ģ:"Ģ",Ḧ:"Ḧ",Ȟ:"Ȟ",Ĥ:"Ĥ",Ḣ:"Ḣ",Ḩ:"Ḩ",Í:"Í",Ì:"Ì",Ï:"Ï",Ḯ:"Ḯ",Ĩ:"Ĩ",Ī:"Ī",Ĭ:"Ĭ",Ǐ:"Ǐ",Î:"Î",İ:"İ",Ĵ:"Ĵ",Ḱ:"Ḱ",Ǩ:"Ǩ",Ķ:"Ķ",Ĺ:"Ĺ",Ľ:"Ľ",Ļ:"Ļ",Ḿ:"Ḿ",Ṁ:"Ṁ",Ń:"Ń",Ǹ:"Ǹ",Ñ:"Ñ",Ň:"Ň",Ṅ:"Ṅ",Ņ:"Ņ",Ó:"Ó",Ò:"Ò",Ö:"Ö",Ȫ:"Ȫ",Õ:"Õ",Ṍ:"Ṍ",Ṏ:"Ṏ",Ȭ:"Ȭ",Ō:"Ō",Ṓ:"Ṓ",Ṑ:"Ṑ",Ŏ:"Ŏ",Ǒ:"Ǒ",Ô:"Ô",Ố:"Ố",Ồ:"Ồ",Ỗ:"Ỗ",Ȯ:"Ȯ",Ȱ:"Ȱ",Ő:"Ő",Ṕ:"Ṕ",Ṗ:"Ṗ",Ŕ:"Ŕ",Ř:"Ř",Ṙ:"Ṙ",Ŗ:"Ŗ",Ś:"Ś",Ṥ:"Ṥ",Š:"Š",Ṧ:"Ṧ",Ŝ:"Ŝ",Ṡ:"Ṡ",Ş:"Ş",Ť:"Ť",Ṫ:"Ṫ",Ţ:"Ţ",Ú:"Ú",Ù:"Ù",Ü:"Ü",Ǘ:"Ǘ",Ǜ:"Ǜ",Ǖ:"Ǖ",Ǚ:"Ǚ",Ũ:"Ũ",Ṹ:"Ṹ",Ū:"Ū",Ṻ:"Ṻ",Ŭ:"Ŭ",Ǔ:"Ǔ",Û:"Û",Ů:"Ů",Ű:"Ű",Ṽ:"Ṽ",Ẃ:"Ẃ",Ẁ:"Ẁ",Ẅ:"Ẅ",Ŵ:"Ŵ",Ẇ:"Ẇ",Ẍ:"Ẍ",Ẋ:"Ẋ",Ý:"Ý",Ỳ:"Ỳ",Ÿ:"Ÿ",Ỹ:"Ỹ",Ȳ:"Ȳ",Ŷ:"Ŷ",Ẏ:"Ẏ",Ź:"Ź",Ž:"Ž",Ẑ:"Ẑ",Ż:"Ż",ά:"ά",ὰ:"ὰ",ᾱ:"ᾱ",ᾰ:"ᾰ",έ:"έ",ὲ:"ὲ",ή:"ή",ὴ:"ὴ",ί:"ί",ὶ:"ὶ",ϊ:"ϊ",ΐ:"ΐ",ῒ:"ῒ",ῑ:"ῑ",ῐ:"ῐ",ό:"ό",ὸ:"ὸ",ύ:"ύ",ὺ:"ὺ",ϋ:"ϋ",ΰ:"ΰ",ῢ:"ῢ",ῡ:"ῡ",ῠ:"ῠ",ώ:"ώ",ὼ:"ὼ",Ύ:"Ύ",Ὺ:"Ὺ",Ϋ:"Ϋ",Ῡ:"Ῡ",Ῠ:"Ῠ",Ώ:"Ώ",Ὼ:"Ὼ"};class Ke{constructor(e,t){this.mode=void 0,this.gullet=void 0,this.settings=void 0,this.leftrightDepth=void 0,this.nextToken=void 0,this.mode="math",this.gullet=new Fl(e,t,this.mode),this.settings=t,this.leftrightDepth=0}expect(e,t){if(t===void 0&&(t=!0),this.fetch().text!==e)throw new A("Expected '"+e+"', got '"+this.fetch().text+"'",this.fetch());t&&this.consume()}consume(){this.nextToken=null}fetch(){return this.nextToken==null&&(this.nextToken=this.gullet.expandNextToken()),this.nextToken}switchMode(e){this.mode=e,this.gullet.switchMode(e)}parse(){this.settings.globalGroup||this.gullet.beginGroup(),this.settings.colorIsTextColor&&this.gullet.macros.set("\\color","\\textcolor");try{var e=this.parseExpression(!1);return this.expect("EOF"),this.settings.globalGroup||this.gullet.endGroup(),e}finally{this.gullet.endGroups()}}subparse(e){var t=this.nextToken;this.consume(),this.gullet.pushToken(new x0("}")),this.gullet.pushTokens(e);var a=this.parseExpression(!1);return this.expect("}"),this.nextToken=t,a}parseExpression(e,t){for(var a=[];;){this.mode==="math"&&this.consumeSpaces();var n=this.fetch();if(Ke.endOfExpression.indexOf(n.text)!==-1||t&&n.text===t||e&&Y0[n.text]&&Y0[n.text].infix)break;var l=this.parseAtom(t);if(l){if(l.type==="internal")continue}else break;a.push(l)}return this.mode==="text"&&this.formLigatures(a),this.handleInfixNodes(a)}handleInfixNodes(e){for(var t=-1,a,n=0;n<e.length;n++)if(e[n].type==="infix"){if(t!==-1)throw new A("only one infix operator per group",e[n].token);t=n,a=e[n].replaceWith}if(t!==-1&&a){var l,o,h=e.slice(0,t),c=e.slice(t+1);h.length===1&&h[0].type==="ordgroup"?l=h[0]:l={type:"ordgroup",mode:this.mode,body:h},c.length===1&&c[0].type==="ordgroup"?o=c[0]:o={type:"ordgroup",mode:this.mode,body:c};var p;return a==="\\\\abovefrac"?p=this.callFunction(a,[l,e[t],o],[]):p=this.callFunction(a,[l,o],[]),[p]}else return e}handleSupSubscript(e){var t=this.fetch(),a=t.text;this.consume(),this.consumeSpaces();var n;do{var l;n=this.parseGroup(e)}while(((l=n)==null?void 0:l.type)==="internal");if(!n)throw new A("Expected group after '"+a+"'",t);return n}formatUnsupportedCmd(e){for(var t=[],a=0;a<e.length;a++)t.push({type:"textord",mode:"text",text:e[a]});var n={type:"text",mode:this.mode,body:t},l={type:"color",mode:this.mode,color:this.settings.errorColor,body:[n]};return l}parseAtom(e){var t=this.parseGroup("atom",e);if((t==null?void 0:t.type)==="internal"||this.mode==="text")return t;for(var a,n;;){this.consumeSpaces();var l=this.fetch();if(l.text==="\\limits"||l.text==="\\nolimits"){if(t&&t.type==="op"){var o=l.text==="\\limits";t.limits=o,t.alwaysHandleSupSub=!0}else if(t&&t.type==="operatorname")t.alwaysHandleSupSub&&(t.limits=l.text==="\\limits");else throw new A("Limit controls must follow a math operator",l);this.consume()}else if(l.text==="^"){if(a)throw new A("Double superscript",l);a=this.handleSupSubscript("superscript")}else if(l.text==="_"){if(n)throw new A("Double subscript",l);n=this.handleSupSubscript("subscript")}else if(l.text==="'"){if(a)throw new A("Double superscript",l);var h={type:"textord",mode:this.mode,text:"\\prime"},c=[h];for(this.consume();this.fetch().text==="'";)c.push(h),this.consume();this.fetch().text==="^"&&c.push(this.handleSupSubscript("superscript")),a={type:"ordgroup",mode:this.mode,body:c}}else if(Oe[l.text]){var p=Ur.test(l.text),g=[];for(g.push(new x0(Oe[l.text])),this.consume();;){var b=this.fetch().text;if(!Oe[b]||Ur.test(b)!==p)break;g.unshift(new x0(Oe[b])),this.consume()}var x=this.subparse(g);p?n={type:"ordgroup",mode:"math",body:x}:a={type:"ordgroup",mode:"math",body:x}}else break}return a||n?{type:"supsub",mode:this.mode,base:t,sup:a,sub:n}:t}parseFunction(e,t){var a=this.fetch(),n=a.text,l=Y0[n];if(!l)return null;if(this.consume(),t&&t!=="atom"&&!l.allowedInArgument)throw new A("Got function '"+n+"' with no arguments"+(t?" as "+t:""),a);if(this.mode==="text"&&!l.allowedInText)throw new A("Can't use function '"+n+"' in text mode",a);if(this.mode==="math"&&l.allowedInMath===!1)throw new A("Can't use function '"+n+"' in math mode",a);var{args:o,optArgs:h}=this.parseArguments(n,l);return this.callFunction(n,o,h,a,e)}callFunction(e,t,a,n,l){var o={funcName:e,parser:this,token:n,breakOnTokenText:l},h=Y0[e];if(h&&h.handler)return h.handler(o,t,a);throw new A("No function handler for "+e)}parseArguments(e,t){var a=t.numArgs+t.numOptionalArgs;if(a===0)return{args:[],optArgs:[]};for(var n=[],l=[],o=0;o<a;o++){var h=t.argTypes&&t.argTypes[o],c=o<t.numOptionalArgs;(t.primitive&&h==null||t.type==="sqrt"&&o===1&&l[0]==null)&&(h="primitive");var p=this.parseGroupOfType("argument to '"+e+"'",h,c);if(c)l.push(p);else if(p!=null)n.push(p);else throw new A("Null argument, please report this as a bug")}return{args:n,optArgs:l}}parseGroupOfType(e,t,a){switch(t){case"color":return this.parseColorGroup(a);case"size":return this.parseSizeGroup(a);case"url":return this.parseUrlGroup(a);case"math":case"text":return this.parseArgumentGroup(a,t);case"hbox":{var n=this.parseArgumentGroup(a,"text");return n!=null?{type:"styling",mode:n.mode,body:[n],style:"text"}:null}case"raw":{var l=this.parseStringGroup("raw",a);return l!=null?{type:"raw",mode:"text",string:l.text}:null}case"primitive":{if(a)throw new A("A primitive argument cannot be optional");var o=this.parseGroup(e);if(o==null)throw new A("Expected group as "+e,this.fetch());return o}case"original":case null:case void 0:return this.parseArgumentGroup(a);default:throw new A("Unknown group type as "+e,this.fetch())}}consumeSpaces(){for(;this.fetch().text===" ";)this.consume()}parseStringGroup(e,t){var a=this.gullet.scanArgument(t);if(a==null)return null;for(var n="",l;(l=this.fetch()).text!=="EOF";)n+=l.text,this.consume();return this.consume(),a.text=n,a}parseRegexGroup(e,t){for(var a=this.fetch(),n=a,l="",o;(o=this.fetch()).text!=="EOF"&&e.test(l+o.text);)n=o,l+=n.text,this.consume();if(l==="")throw new A("Invalid "+t+": '"+a.text+"'",a);return a.range(n,l)}parseColorGroup(e){var t=this.parseStringGroup("color",e);if(t==null)return null;var a=/^(#[a-f0-9]{3}|#?[a-f0-9]{6}|[a-z]+)$/i.exec(t.text);if(!a)throw new A("Invalid color: '"+t.text+"'",t);var n=a[0];return/^[0-9a-f]{6}$/i.test(n)&&(n="#"+n),{type:"color-token",mode:this.mode,color:n}}parseSizeGroup(e){var t,a=!1;if(this.gullet.consumeSpaces(),!e&&this.gullet.future().text!=="{"?t=this.parseRegexGroup(/^[-+]? *(?:$|\d+|\d+\.\d*|\.\d*) *[a-z]{0,2} *$/,"size"):t=this.parseStringGroup("size",e),!t)return null;!e&&t.text.length===0&&(t.text="0pt",a=!0);var n=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(t.text);if(!n)throw new A("Invalid size: '"+t.text+"'",t);var l={number:+(n[1]+n[2]),unit:n[3]};if(!sa(l))throw new A("Invalid unit: '"+l.unit+"'",t);return{type:"size",mode:this.mode,value:l,isBlank:a}}parseUrlGroup(e){this.gullet.lexer.setCatcode("%",13),this.gullet.lexer.setCatcode("~",12);var t=this.parseStringGroup("url",e);if(this.gullet.lexer.setCatcode("%",14),this.gullet.lexer.setCatcode("~",13),t==null)return null;var a=t.text.replace(/\\([#$%&~_^{}])/g,"$1");return{type:"url",mode:this.mode,url:a}}parseArgumentGroup(e,t){var a=this.gullet.scanArgument(e);if(a==null)return null;var n=this.mode;t&&this.switchMode(t),this.gullet.beginGroup();var l=this.parseExpression(!1,"EOF");this.expect("EOF"),this.gullet.endGroup();var o={type:"ordgroup",mode:this.mode,loc:a.loc,body:l};return t&&this.switchMode(n),o}parseGroup(e,t){var a=this.fetch(),n=a.text,l;if(n==="{"||n==="\\begingroup"){this.consume();var o=n==="{"?"}":"\\endgroup";this.gullet.beginGroup();var h=this.parseExpression(!1,o),c=this.fetch();this.expect(o),this.gullet.endGroup(),l={type:"ordgroup",mode:this.mode,loc:p0.range(a,c),body:h,semisimple:n==="\\begingroup"||void 0}}else if(l=this.parseFunction(t,e)||this.parseSymbol(),l==null&&n[0]==="\\"&&!Za.hasOwnProperty(n)){if(this.settings.throwOnError)throw new A("Undefined control sequence: "+n,a);l=this.formatUnsupportedCmd(n),this.consume()}return l}formLigatures(e){for(var t=e.length-1,a=0;a<t;++a){var n=e[a],l=n.text;l==="-"&&e[a+1].text==="-"&&(a+1<t&&e[a+2].text==="-"?(e.splice(a,3,{type:"textord",mode:"text",loc:p0.range(n,e[a+2]),text:"---"}),t-=2):(e.splice(a,2,{type:"textord",mode:"text",loc:p0.range(n,e[a+1]),text:"--"}),t-=1)),(l==="'"||l==="`")&&e[a+1].text===l&&(e.splice(a,2,{type:"textord",mode:"text",loc:p0.range(n,e[a+1]),text:l+l}),t-=1)}}parseSymbol(){var e=this.fetch(),t=e.text;if(/^\\verb[^a-zA-Z]/.test(t)){this.consume();var a=t.slice(5),n=a.charAt(0)==="*";if(n&&(a=a.slice(1)),a.length<2||a.charAt(0)!==a.slice(-1))throw new A(`\\verb assertion failed --
                    please report what input caused this bug`);return a=a.slice(1,-1),{type:"verb",mode:"text",body:a,star:n}}Gr.hasOwnProperty(t[0])&&!K[this.mode][t[0]]&&(this.settings.strict&&this.mode==="math"&&this.settings.reportNonstrict("unicodeTextInMathMode",'Accented Unicode text character "'+t[0]+'" used in math mode',e),t=Gr[t[0]]+t.slice(1));var l=Rl.exec(t);l&&(t=t.substring(0,l.index),t==="i"?t="ı":t==="j"&&(t="ȷ"));var o;if(K[this.mode][t]){this.settings.strict&&this.mode==="math"&&Mt.indexOf(t)>=0&&this.settings.reportNonstrict("unicodeTextInMathMode",'Latin-1/Unicode text character "'+t[0]+'" used in math mode',e);var h=K[this.mode][t].group,c=p0.range(e),p;if(z1.hasOwnProperty(h)){var g=h;p={type:"atom",mode:this.mode,family:g,loc:c,text:t}}else p={type:h,mode:this.mode,loc:c,text:t};o=p}else if(t.charCodeAt(0)>=128)this.settings.strict&&(ia(t.charCodeAt(0))?this.mode==="math"&&this.settings.reportNonstrict("unicodeTextInMathMode",'Unicode text character "'+t[0]+'" used in math mode',e):this.settings.reportNonstrict("unknownSymbol",'Unrecognized Unicode character "'+t[0]+'"'+(" ("+t.charCodeAt(0)+")"),e)),o={type:"textord",mode:"text",loc:p0.range(e),text:t};else return null;if(this.consume(),l)for(var b=0;b<l[0].length;b++){var x=l[0][b];if(!gt[x])throw new A("Unknown accent ' "+x+"'",e);var w=gt[x][this.mode]||gt[x].text;if(!w)throw new A("Accent "+x+" unsupported in "+this.mode+" mode",e);o={type:"accent",mode:this.mode,loc:p0.range(e),label:w,isStretchy:!1,isShifty:!0,base:o}}return o}}Ke.endOfExpression=["}","\\endgroup","\\end","\\right","&"];var Pl=function(e,t){if(!(typeof e=="string"||e instanceof String))throw new TypeError("KaTeX can only parse string typed expression");var a=new Ke(e,t);delete a.gullet.macros.current["\\df@tag"];var n=a.parse();if(delete a.gullet.macros.current["\\current@color"],delete a.gullet.macros.current["\\color"],a.gullet.macros.get("\\df@tag")){if(!t.displayMode)throw new A("\\tag works only in display equations");n=[{type:"tag",mode:"text",body:n,tag:a.subparse([new x0("\\df@tag")])}]}return n};typeof document<"u"&&document.compatMode!=="CSS1Compat"&&typeof console<"u"&&console.warn("Warning: KaTeX doesn't work in quirks mode. Make sure your website has a suitable doctype.");var Hl=function(e,t){var a=Ul(e,t).toMarkup();return a},Vl=function(e,t,a){if(a.throwOnError||!(e instanceof A))throw e;var n=y.makeSpan(["katex-error"],[new M0(t)]);return n.setAttribute("title",e.toString()),n.setAttribute("style","color:"+a.errorColor),n},Ul=function(e,t){var a=new e1(t);try{var n=Pl(e,a);return Z1(n,e,a)}catch(l){return Vl(l,e,a)}},Yr={renderToString:Hl};const Gl={},Yl=[];function jl(r){const e=r||Gl;return function(t,a){tn(t,"element",function(n,l){const o=Array.isArray(n.properties.className)?n.properties.className:Yl,h=o.includes("language-math"),c=o.includes("math-display"),p=o.includes("math-inline");let g=c;if(!h&&!c&&!p)return;let b=l[l.length-1],x=n;if(n.tagName==="code"&&h&&b&&b.type==="element"&&b.tagName==="pre"&&(x=b,b=l[l.length-2],g=!0),!b)return;const w=Ln(x,{whitespace:"pre"});let M;try{M=Yr.renderToString(w,{...e,displayMode:g,throwOnError:!0})}catch(N){const E=N,L=E.name.toLowerCase();a.message("Could not render math with KaTeX",{ancestors:[...l,n],cause:E,place:n.position,ruleId:L,source:"rehype-katex"});try{M=Yr.renderToString(w,{...e,displayMode:g,strict:"ignore",throwOnError:!1})}catch{M=[{type:"element",tagName:"span",properties:{className:["katex-error"],style:"color:"+(e.errorColor||"#cc0000"),title:String(N)},children:[{type:"text",value:w}]}]}}typeof M=="string"&&(M=Cn(M).children);const C=b.children.indexOf(x);return b.children.splice(C,1,...M),rn})}}export{jl as default};
