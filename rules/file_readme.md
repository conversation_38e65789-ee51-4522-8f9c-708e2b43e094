# 文件管理模块开发指南

本文档总结了文件管理模块的开发指南、任务列表和实现流程，可作为将来开发类似功能的参考模板。

## 开发指导方针

1. **MinIO 客户端**:
   - 不使用重试机制
   - 捕获异常后记录错误并重新抛出给上层
   - 使用 `from app.log import logger` 进行日志记录

2. **FileController 实现**:
   - 在整个项目中统一使用 `bucket_name` 和 `hash_algorithm`
   - 从 `settings` 中获取配置值
   - 异常处理：记录错误并重新抛出给上层
   - 使用 `CRUDBase` 进行数据库操作
   - 使用 `obj` 而不是 `db_file` 作为变量名，保持与 CRUDBase 一致的命名风格
   - 不使用 `get_or_404` 方法，让上层处理异常
   - 利用 TimestampMixin 自动更新 `updated_at` 字段

3. **文件过滤功能**:
   - 只根据 `name` 和 `parent_id` 字段进行过滤
   - 保留 `include_deleted` 设置

4. **API 层实现**:
   - 使用 Pydantic schemas 进行 API 的入参和过滤器
   - 使用 app 下面的 logger 进行日志记录
   - 依赖框架在 API router 层面提供的统一认证授权，不需要单独实现
   - 使用 Success 和 Fail 响应模型统一返回格式

5. **代码风格**:
   - 使用类型注解
   - 提供详细的文档字符串
   - 使用异步函数
   - 遵循 PEP 8 规范

## 任务列表

1. ✅ 创建 `FileBase` schema 用于返回文件详细信息
2. ✅ 更新 `settings` 添加 `HASH_ALGORITHM` 配置
3. ✅ 实现 `upload_file` 方法
4. ✅ 实现 `soft_delete` 方法
5. ✅ 实现 `update_file_info` 方法
6. ✅ 实现 `get_file` 方法
7. ✅ 实现 `list_files_by_filter` 方法
8. ✅ 实现 `get_download_url` 方法
9. ✅ 修改 FileController 使用 `obj` 变量名并移除 `get_or_404`
10. ✅ 实现 API 层接口

## 实现流程

### 1. 创建 FileBase schema

1. 在 `app/schemas/files.py` 中添加 `FileBase` 类
2. 包含所有需要返回的文件元数据字段
3. 确保字段类型正确，并添加描述

```python
class FileBase(BaseModel):
    """
    Base schema for file output.
    Contains all the fields that are returned when a file is retrieved.
    """
    id: int = Field(..., description="文件ID")
    name: str = Field(..., description="文件名")
    # ... 其他字段

    class Config:
        from_attributes = True
```

### 2. 更新 settings 添加 HASH_ALGORITHM 配置

1. 在 `app/settings/config.py` 中添加 `HASH_ALGORITHM` 配置
2. 设置默认值为 "sha256"

```python
# Default hash algorithm for file content hashing
HASH_ALGORITHM: str = "sha256"
```

### 3. 实现 upload_file 方法

1. 在 `FileController` 类中添加 `upload_file` 方法
2. 接受 `UploadFile` 和 `FileCreate` 参数
3. 读取文件内容并计算哈希值
4. 上传文件到 MinIO
5. 创建文件元数据记录
6. 返回 `FileBase` 对象

```python
async def upload_file(self, file: UploadFile, file_create: FileCreate) -> FileBase:
    try:
        # 读取文件内容
        file_content = await file.read()

        # 计算哈希值
        content_hash = hashlib.new(settings.HASH_ALGORITHM, file_content).hexdigest()

        # 上传到 MinIO
        # ... 代码实现

        # 创建数据库记录
        obj_data = {
            "name": original_name,
            "parent_id": file_create.parent_id,
            "user_id": file_create.user_id,
            # ... 其他字段
        }

        obj = await self.model.create(**obj_data)

        return FileBase.model_validate(obj)
    except Exception as e:
        logger.error(f"Error uploading file: {e}")
        raise
```

### 4. 实现 soft_delete 方法

1. 在 `FileController` 类中添加 `soft_delete` 方法
2. 接受 `file_id` 参数
3. 设置 `deleted_at` 字段为当前时间
4. 返回更新后的 `FileBase` 对象

```python
async def soft_delete(self, file_id: int) -> FileBase:
    try:
        # 获取文件
        obj = await self.model.get_or_none(id=file_id, deleted_at__isnull=True)
        if not obj:
            raise HTTPException(status_code=404, detail="File not found for soft deletion")

        # 设置删除时间
        now = datetime.now(timezone.utc)
        obj.deleted_at = now
        await obj.save()  # Remove update_fields to allow updated_at to be automatically updated

        return FileBase.model_validate(obj)
    except Exception as e:
        logger.error(f"Error soft deleting file: {e}")
        raise
```

### 5. 实现 update_file_info 方法

1. 在 `FileController` 类中添加 `update_file_info` 方法
2. 接受 `file_id` 和 `file_update` 参数
3. 检查名称冲突
4. 更新文件元数据
5. 返回更新后的 `FileBase` 对象

```python
async def update_file_info(self, file_id: int, file_update: FileUpdate) -> FileBase:
    try:
        # 获取文件
        obj = await self.model.get_or_none(id=file_id, deleted_at__isnull=True)
        if not obj:
            raise HTTPException(status_code=404, detail="File not found for update")

        # 准备更新数据
        update_data = file_update.model_dump(exclude_unset=True, exclude={"id"})

        # 检查名称冲突
        if "name" in update_data and update_data["name"] != obj.name:
            # ... 检查逻辑
            pass

        # 更新文件元数据
        obj.update_from_dict(update_data)
        await obj.save()  # Remove update_fields to allow updated_at to be automatically updated

        return FileBase.model_validate(obj)
    except Exception as e:
        logger.error(f"Error updating file: {e}")
        raise
```

### 6. 实现 get_file 方法

1. 在 `FileController` 类中添加 `get_file` 方法
2. 接受 `file_id` 参数
3. 返回 `FileBase` 对象

```python
async def get_file(self, file_id: int) -> FileBase:
    try:
        # 获取文件
        obj = await self.model.get_or_none(id=file_id, deleted_at__isnull=True)
        if not obj:
            raise HTTPException(status_code=404, detail="File not found")

        return FileBase.model_validate(obj)
    except Exception as e:
        logger.error(f"Error getting file: {e}")
        raise
```

### 7. 创建 FileFilter schema

1. 在 `app/schemas/files.py` 中添加 `FileFilter` 类
2. 只包含 `name`、`parent_id` 和 `include_deleted` 字段
3. 添加分页参数 `page` 和 `page_size`

```python
class FileFilter(BaseModel):
    """
    Schema for filtering files.
    Only filters by name, parent_id, and whether to include deleted files.
    """
    parent_id: Optional[int] = Field(None, description="父级目录ID")
    name: Optional[str] = Field(None, description="文件名 (模糊匹配)")
    include_deleted: Optional[bool] = Field(False, description="是否包含已删除文件")
    page: int = Field(1, description="页码", ge=1)
    page_size: int = Field(20, description="每页数量", ge=1, le=100)

    class Config:
        from_attributes = True
```

### 8. 实现 list_files_by_filter 方法

1. 在 `FileController` 类中添加 `list_files_by_filter` 方法
2. 接受 `FileFilter` 参数
3. 根据 `name` 和 `parent_id` 构建查询条件
4. 处理 `include_deleted` 设置
5. 返回 `FileListResponse` 对象

```python
async def list_files_by_filter(self, file_filter: FileFilter) -> FileListResponse:
    try:
        # 构建查询条件
        query_filters = Q()

        if file_filter.parent_id is not None:
            query_filters &= Q(parent_id=file_filter.parent_id)

        if file_filter.name is not None:
            query_filters &= Q(name__icontains=file_filter.name)

        if not file_filter.include_deleted:
            query_filters &= Q(deleted_at__isnull=True)

        # 获取文件列表
        # ... 代码实现

        return response
    except Exception as e:
        logger.error(f"Error listing files by filter: {e}")
        raise
```

### 9. 实现 API 层

1. 在 `app/api/v1/files/files.py` 中实现 REST API 接口
2. 使用 Pydantic schemas 进行参数验证
3. 使用 app 的 logger 进行日志记录
4. 依赖框架的认证授权机制
5. 使用 Success 和 Fail 响应模型

```python
@router.get("/list", summary="列出文件和目录", response_model=Success[FileListResponse])
async def list_files(
    parent_id: Optional[int] = Query(None, description="父级目录ID"),
    name: Optional[str] = Query(None, description="文件名 (模糊匹配)"),
    include_deleted: bool = Query(False, description="是否包含已删除文件"),
    page: int = Query(1, description="页码", ge=1),
    page_size: int = Query(20, description="每页数量", ge=1, le=100),
):
    """
    列出文件和目录，支持按父级目录ID和名称筛选
    """
    file_filter = FileFilter(
        parent_id=parent_id,
        name=name,
        include_deleted=include_deleted,
        page=page,
        page_size=page_size
    )

    result = await file_controller.list_files_by_filter(file_filter)
    return Success(data=result)
```

## AI 提示模板

当需要开发类似功能时，可以使用以下提示模板指导 AI 进行代码生成：

```
我需要在文件管理模块中实现以下功能：

[描述需要实现的功能]

请遵循以下指导方针：
1. MinIO 客户端：不使用重试，捕获异常后记录错误并重新抛出
2. 统一使用 settings 中的 bucket_name 和 hash_algorithm
3. 异常处理：记录错误并重新抛出给上层
4. 使用 CRUDBase 进行数据库操作
5. 使用 obj 而不是 db_file 作为变量名
6. 不使用 get_or_404 方法，让上层处理异常
7. 利用 TimestampMixin 自动更新 updated_at 字段
8. 提供详细的文档字符串和类型注解

对于 API 层实现：
1. 使用 Pydantic schemas 进行 API 的入参和过滤器
2. 使用 app 下面的 logger 进行日志记录
3. 依赖框架在 API router 层面提供的统一认证授权
4. 使用 Success 和 Fail 响应模型统一返回格式

请按照以下步骤实现：
1. 创建/更新必要的 schema 类
2. 实现控制器方法
3. 确保错误处理
4. 实现 API 接口
5. 返回适当的响应对象

请提供完整的代码实现。
```

## 注意事项

1. 确保所有方法都有适当的错误处理
2. 使用事务保证数据一致性
3. 返回标准化的响应格式
4. 遵循项目的命名和代码风格约定
5. 使用 model_validate 替代 from_tortoise_orm
6. API 层不需要单独实现认证授权

## 测试方法

### AI辅助自动化调试流程

在本项目中，我们使用了基于AI的自动化调试方法来快速定位和修复问题。以下是推荐的调试步骤：

1. **运行程序并收集错误信息**
   - 使用 `uv run run.py` 命令启动程序
   - 观察控制台输出，收集错误信息和堆栈跟踪
   - 确定错误的具体位置和类型

2. **分析错误根本原因**
   - 检查错误消息，理解错误的本质
   - 查看相关代码文件，了解上下文
   - 识别可能的问题模式（类型错误、未定义方法、序列化问题等）

3. **逐步修复问题**
   - 从最基本的错误开始修复
   - 一次只修改一个问题，避免引入新的错误
   - 修复后立即重新运行程序验证

4. **常见问题及解决方案**
   - **类型错误**：确保使用正确的类型注解，特别是在泛型类型中
   - **序列化问题**：使用 `model_dump()` 将 Pydantic 模型转换为可序列化的字典
   - **方法名不匹配**：确保调用的方法名与实际实现的方法名一致
   - **响应模型问题**：确保响应模型能够正确处理返回的数据类型

5. **热重载测试**
   - 利用 Uvicorn 的热重载功能，在修改代码后自动重启服务
   - 观察控制台输出，确认错误是否已解决
   - 如果出现新错误，重复上述步骤

### 实际案例：修复 FastAPI 应用程序

在本项目中，我们遇到并修复了以下问题：

1. **类型注解错误**：`Success[FileListResponse]` 类型注解无效，因为 `Success` 类是 `JSONResponse` 的子类，不支持泛型。
   - 解决方案：移除 `response_model` 参数，直接返回 `Success` 对象。

2. **方法名不匹配**：API 调用 `get_file` 方法，但控制器中实现的是 `get_file_info`。
   - 解决方案：修改 API 调用，使用正确的方法名 `get_file_info`。

3. **序列化问题**：Pydantic 模型不能直接在 `Success` 响应中序列化。
   - 解决方案：使用 `model_dump()` 方法将 Pydantic 模型转换为字典。

通过这种系统化的调试方法，我们能够快速定位和修复问题，确保应用程序正常运行。这种方法也适用于其他模块的开发和测试。
